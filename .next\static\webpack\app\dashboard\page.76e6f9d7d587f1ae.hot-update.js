"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/attachmentService.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/attachmentService.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachmentService: function() { return /* binding */ attachmentService; }\n/* harmony export */ });\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\nclass AttachmentService {\n    /**\n   * Valida se o arquivo é suportado\n   */ validateFile(file) {\n        // Verificar tamanho\n        if (file.size > this.MAX_FILE_SIZE) {\n            return {\n                isValid: false,\n                error: \"Arquivo muito grande. M\\xe1ximo permitido: 10MB\"\n            };\n        }\n        // Verificar tipo\n        const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n        const isPdf = file.type === this.SUPPORTED_PDF_TYPE;\n        if (!isImage && !isPdf) {\n            return {\n                isValid: false,\n                error: \"Tipo de arquivo n\\xe3o suportado. Use PNG, JPEG, WebP ou PDF\"\n            };\n        }\n        return {\n            isValid: true\n        };\n    }\n    /**\n   * Converte arquivo para base64\n   */ async fileToBase64(file) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>{\n                const result = reader.result;\n                // Remover o prefixo data:type;base64,\n                const base64 = result.split(\",\")[1];\n                resolve(base64);\n            };\n            reader.onerror = reject;\n            reader.readAsDataURL(file);\n        });\n    }\n    /**\n   * Gera ID único para anexo\n   */ generateAttachmentId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Faz upload do arquivo para Firebase Storage\n   */ async uploadAttachment(params) {\n        const { file, username, chatId } = params;\n        console.log(\"\\uD83D\\uDD04 Iniciando upload do arquivo: \".concat(file.name));\n        console.log(\"\\uD83D\\uDCCA Tamanho: \".concat(file.size, \" bytes, Tipo: \").concat(file.type));\n        console.log(\"\\uD83D\\uDC64 Username: \".concat(username, \", \\uD83D\\uDCAC ChatId: \").concat(chatId));\n        // Validar arquivo\n        const validation = this.validateFile(file);\n        if (!validation.isValid) {\n            console.log(\"❌ Valida\\xe7\\xe3o falhou: \".concat(validation.error));\n            throw new Error(validation.error);\n        }\n        console.log(\"✅ Arquivo validado com sucesso\");\n        try {\n            // Gerar ID único para o anexo\n            const attachmentId = this.generateAttachmentId();\n            console.log(\"\\uD83C\\uDD94 ID do anexo gerado: \".concat(attachmentId));\n            // Determinar tipo\n            const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n            const type = isImage ? \"image\" : \"pdf\";\n            console.log(\"\\uD83D\\uDCC4 Tipo determinado: \".concat(type));\n            // Criar caminho no Storage\n            const storagePath = \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/anexos/\").concat(attachmentId, \"_\").concat(file.name);\n            console.log(\"\\uD83D\\uDCC1 Caminho no Storage: \".concat(storagePath));\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.storage, storagePath);\n            // Upload do arquivo\n            console.log(\"\\uD83D\\uDE80 Iniciando upload para Firebase Storage...\");\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.uploadBytes)(storageRef, file);\n            console.log(\"✅ Upload para Firebase Storage conclu\\xeddo\");\n            // Obter URL de download\n            console.log(\"\\uD83D\\uDD17 Obtendo URL de download...\");\n            const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.getDownloadURL)(storageRef);\n            console.log(\"✅ URL de download obtida: \".concat(downloadURL.substring(0, 100), \"...\"));\n            // Preparar metadados\n            const metadata = {\n                id: attachmentId,\n                type,\n                filename: file.name,\n                url: downloadURL,\n                size: file.size,\n                uploadedAt: Date.now(),\n                storagePath,\n                isActive: true // Por padrão, anexos são ativos\n            };\n            console.log(\"\\uD83D\\uDCCB Metadados preparados:\", metadata);\n            // Para PDFs, também converter para base64 para envio ao OpenRouter\n            let base64Data;\n            if (type === \"pdf\") {\n                console.log(\"\\uD83D\\uDCC4 Convertendo PDF para base64...\");\n                base64Data = await this.fileToBase64(file);\n                metadata.base64Data = base64Data;\n                console.log(\"✅ PDF convertido para base64 (\".concat(base64Data.length, \" caracteres)\"));\n            }\n            console.log(\"\\uD83C\\uDF89 Upload do arquivo \".concat(file.name, \" conclu\\xeddo com sucesso!\"));\n            return {\n                metadata,\n                base64Data\n            };\n        } catch (error) {\n            console.error(\"❌ Erro ao fazer upload do anexo \".concat(file.name, \":\"), error);\n            throw new Error(\"Falha no upload do arquivo \".concat(file.name, \". Tente novamente.\"));\n        }\n    }\n    /**\n   * Processa múltiplos arquivos\n   */ async uploadMultipleAttachments(files, username, chatId) {\n        const results = [];\n        for (const file of files){\n            try {\n                const result = await this.uploadAttachment({\n                    file,\n                    username,\n                    chatId\n                });\n                results.push(result);\n            } catch (error) {\n                console.error(\"Erro ao processar arquivo \".concat(file.name, \":\"), error);\n            // Continuar com os outros arquivos\n            }\n        }\n        return results;\n    }\n    /**\n   * Prepara anexos para envio ao OpenRouter\n   */ prepareAttachmentsForOpenRouter(attachments) {\n        const openRouterContent = [];\n        for (const attachment of attachments){\n            if (attachment.type === \"image\") {\n                // Para imagens, usar URL\n                openRouterContent.push({\n                    type: \"image_url\",\n                    image_url: {\n                        url: attachment.url\n                    }\n                });\n            } else if (attachment.type === \"pdf\" && attachment.base64Data) {\n                // Para PDFs, usar base64 com formato file\n                const dataUrl = \"data:application/pdf;base64,\".concat(attachment.base64Data);\n                openRouterContent.push({\n                    type: \"file\",\n                    file: {\n                        filename: attachment.filename,\n                        file_data: dataUrl\n                    }\n                });\n            }\n        }\n        return openRouterContent;\n    }\n    /**\n   * Prepara plugins para PDFs (engine mistral-ocr)\n   */ preparePDFPlugins(attachments) {\n        const hasPDF = attachments.some((att)=>att.type === \"pdf\");\n        if (!hasPDF) {\n            return [];\n        }\n        return [\n            {\n                id: \"file-parser\",\n                pdf: {\n                    engine: \"mistral-ocr\"\n                }\n            }\n        ];\n    }\n    /**\n   * Limpa anexos temporários (se necessário)\n   */ async cleanupTemporaryAttachments(attachments) {\n        // Implementar limpeza se necessário\n        // Por enquanto, mantemos os arquivos no Storage\n        console.log(\"Cleanup de anexos tempor\\xe1rios:\", attachments.length);\n    }\n    constructor(){\n        this.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB\n        ;\n        this.SUPPORTED_IMAGE_TYPES = [\n            \"image/png\",\n            \"image/jpeg\",\n            \"image/webp\"\n        ];\n        this.SUPPORTED_PDF_TYPE = \"application/pdf\";\n    }\n}\n// Exportar instância singleton\nconst attachmentService = new AttachmentService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (attachmentService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/attachmentService.ts\n"));

/***/ })

});