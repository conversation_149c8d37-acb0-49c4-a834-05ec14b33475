"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/ModelSelectionModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/settingsService */ \"(app-pages-browser)/./src/lib/services/settingsService.ts\");\n/* harmony import */ var _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/openRouterService */ \"(app-pages-browser)/./src/lib/services/openRouterService.ts\");\n/* harmony import */ var _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/deepSeekService */ \"(app-pages-browser)/./src/lib/services/deepSeekService.ts\");\n/* harmony import */ var _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/modelFavoritesService */ \"(app-pages-browser)/./src/lib/services/modelFavoritesService.ts\");\n/* harmony import */ var _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAdvancedSearch */ \"(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\");\n/* harmony import */ var _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/advancedFiltersService */ \"(app-pages-browser)/./src/lib/services/advancedFiltersService.ts\");\n/* harmony import */ var _components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/AdvancedSearchInput */ \"(app-pages-browser)/./src/components/AdvancedSearchInput.tsx\");\n/* harmony import */ var _ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ExpensiveModelConfirmationModal */ \"(app-pages-browser)/./src/components/dashboard/ExpensiveModelConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Constantes para cache\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutos\nconst ENDPOINTS_CACHE_DURATION = 10 * 60 * 1000; // 10 minutos\nconst ModelSelectionModal = (param)=>{\n    let { isOpen, onClose, currentModel, onModelSelect } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [endpoints, setEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedEndpoint, setSelectedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteModelIds, setFavoriteModelIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [displayedModelsCount, setDisplayedModelsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(4);\n    const MODELS_PER_PAGE = 4;\n    const [customModelId, setCustomModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showExpensiveModelModal, setShowExpensiveModelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingExpensiveModel, setPendingExpensiveModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [smartCategories, setSmartCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteToggling, setFavoriteToggling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [lastLoadedEndpoint, setLastLoadedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelsCache, setModelsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [endpointsCache, setEndpointsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"paid\",\n        sortBy: \"newest\",\n        searchTerm: \"\"\n    });\n    // Hook de busca avançada\n    const { searchTerm, setSearchTerm, searchResults, suggestions, isSearching, hasSearched, clearSearch, trackModelSelection } = (0,_hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch)(models, {\n        debounceMs: 300,\n        enableSuggestions: false,\n        cacheResults: true,\n        fuzzyThreshold: 0.6,\n        maxResults: 50,\n        boostFavorites: true,\n        userId: (user === null || user === void 0 ? void 0 : user.email) || null,\n        trackAnalytics: true\n    });\n    // Load user endpoints apenas se necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && isOpen) {\n            // Verificar se temos cache válido\n            if (endpointsCache && Date.now() - endpointsCache.timestamp < ENDPOINTS_CACHE_DURATION) {\n                setEndpoints(endpointsCache.endpoints);\n                // Selecionar endpoint se ainda não tiver um selecionado\n                if (!selectedEndpoint && endpointsCache.endpoints.length > 0) {\n                    const openRouterEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"OpenRouter\");\n                    const deepSeekEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"DeepSeek\");\n                    if (openRouterEndpoint) {\n                        setSelectedEndpoint(openRouterEndpoint);\n                    } else if (deepSeekEndpoint) {\n                        setSelectedEndpoint(deepSeekEndpoint);\n                    } else {\n                        setSelectedEndpoint(endpointsCache.endpoints[0]);\n                    }\n                }\n            } else {\n                loadEndpoints();\n            }\n        }\n    }, [\n        user,\n        isOpen\n    ]);\n    // Load models when endpoint changes ou não há cache\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            const cachedData = modelsCache.get(cacheKey);\n            // Verificar se temos cache válido para este endpoint\n            if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {\n                setModels(cachedData.models);\n                setLastLoadedEndpoint(selectedEndpoint.id);\n            } else {\n                // Só carregar se mudou de endpoint ou não há cache válido\n                if (lastLoadedEndpoint !== selectedEndpoint.id || !cachedData) {\n                    if (selectedEndpoint.name === \"OpenRouter\") {\n                        loadOpenRouterModels();\n                    } else if (selectedEndpoint.name === \"DeepSeek\") {\n                        loadDeepSeekModels();\n                    }\n                }\n            }\n        }\n    }, [\n        selectedEndpoint,\n        lastLoadedEndpoint,\n        modelsCache\n    ]);\n    // Load smart categories\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSmartCategories(_lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getSmartCategories());\n    }, []);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                return userDoc.data().username || userDoc.id;\n            }\n            return \"unknown\";\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return \"unknown\";\n        }\n    };\n    const loadEndpoints = async ()=>{\n        if (!user) {\n            console.log(\"No user found\");\n            return;\n        }\n        console.log(\"Loading endpoints for user:\", user.uid, user.email);\n        setLoading(true);\n        setError(null);\n        try {\n            const username = await getUsernameFromFirestore();\n            console.log(\"Using username:\", username);\n            const userEndpoints = await (0,_lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__.getUserAPIEndpoints)(username);\n            console.log(\"Loaded endpoints:\", userEndpoints);\n            // Salvar no cache\n            setEndpointsCache({\n                endpoints: userEndpoints,\n                timestamp: Date.now()\n            });\n            setEndpoints(userEndpoints);\n            // Select first available endpoint by default (OpenRouter or DeepSeek)\n            const openRouterEndpoint = userEndpoints.find((ep)=>ep.name === \"OpenRouter\");\n            const deepSeekEndpoint = userEndpoints.find((ep)=>ep.name === \"DeepSeek\");\n            if (openRouterEndpoint) {\n                setSelectedEndpoint(openRouterEndpoint);\n                console.log(\"Selected OpenRouter endpoint:\", openRouterEndpoint);\n            } else if (deepSeekEndpoint) {\n                setSelectedEndpoint(deepSeekEndpoint);\n                console.log(\"Selected DeepSeek endpoint:\", deepSeekEndpoint);\n            } else if (userEndpoints.length > 0) {\n                setSelectedEndpoint(userEndpoints[0]);\n                console.log(\"Selected first endpoint:\", userEndpoints[0]);\n            }\n        } catch (error) {\n            console.error(\"Error loading endpoints:\", error);\n            setError(\"Erro ao carregar endpoints: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadOpenRouterModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from OpenRouter\n            const openRouterModels = await _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.fetchModels();\n            // Load favorite model IDs\n            const username = await getUsernameFromFirestore();\n            const favoriteIds = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);\n            // Mark favorite models\n            const modelsWithFavorites = openRouterModels.map((model)=>({\n                    ...model,\n                    isFavorite: favoriteIds.has(model.id)\n                }));\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: modelsWithFavorites,\n                    timestamp: Date.now()\n                }));\n            setModels(modelsWithFavorites);\n            setFavoriteModelIds(favoriteIds);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading models:\", error);\n            setError(\"Erro ao carregar modelos\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadDeepSeekModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from DeepSeek\n            const deepSeekModels = await _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.fetchModels();\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: deepSeekModels,\n                    timestamp: Date.now()\n                }));\n            setModels(deepSeekModels);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading DeepSeek models:\", error);\n            setError(\"Erro ao carregar modelos DeepSeek\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Função para obter modelos filtrados\n    const getFilteredModels = ()=>{\n        let filtered = [\n            ...models\n        ];\n        // Primeiro, aplicar filtros de categoria base (paid/free/favorites)\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\") {\n            if (filters.category === \"favorites\") {\n                filtered = [];\n            } else {\n                filtered = _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.filterByCategory(filtered, filters.category);\n            }\n        } else {\n            filtered = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.filterByCategory(filtered, filters.category);\n        }\n        // Se há busca ativa, usar resultados da busca avançada (mas ainda respeitando a categoria base)\n        if (hasSearched && searchTerm.trim()) {\n            const searchResultModels = searchResults.map((result)=>result.model);\n            // Filtrar os resultados de busca para manter apenas os que passam pelo filtro de categoria base\n            filtered = searchResultModels.filter((model)=>filtered.some((f)=>f.id === model.id));\n        } else if (selectedCategory) {\n            // Se há categoria inteligente selecionada, aplicar filtro adicional\n            const categoryFiltered = _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getModelsByCategory(filtered, selectedCategory);\n            filtered = categoryFiltered;\n        }\n        // Aplicar ordenação se não há busca ativa\n        if (!hasSearched || !searchTerm.trim()) {\n            const service = (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" ? _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService : _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService;\n            filtered = service.sortModels(filtered, filters.sortBy);\n        }\n        return filtered;\n    };\n    const filteredModels = getFilteredModels();\n    const handleToggleFavorite = async (model)=>{\n        if (!user || !selectedEndpoint) return;\n        // Prevenir múltiplas chamadas simultâneas para o mesmo modelo\n        if (favoriteToggling.has(model.id)) {\n            console.log(\"Already toggling favorite for model:\", model.id);\n            return;\n        }\n        console.log(\"Toggling favorite for model:\", model.id, \"Current status:\", model.isFavorite);\n        try {\n            // Marcar como em processo\n            setFavoriteToggling((prev)=>new Set(prev).add(model.id));\n            const username = await getUsernameFromFirestore();\n            console.log(\"Using username for favorites:\", username);\n            const newFavoriteStatus = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.toggleFavorite(username, selectedEndpoint.id, model.id, model.name);\n            console.log(\"New favorite status:\", newFavoriteStatus);\n            // Update local state\n            const updatedFavoriteIds = new Set(favoriteModelIds);\n            if (newFavoriteStatus) {\n                updatedFavoriteIds.add(model.id);\n            } else {\n                updatedFavoriteIds.delete(model.id);\n            }\n            setFavoriteModelIds(updatedFavoriteIds);\n            // Update models array\n            setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: newFavoriteStatus\n                    } : m));\n            // Update cache with the new favorite status\n            if (selectedEndpoint) {\n                const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n                setModelsCache((prev)=>{\n                    const currentCache = prev.get(cacheKey);\n                    if (currentCache) {\n                        const updatedModels = currentCache.models.map((m)=>m.id === model.id ? {\n                                ...m,\n                                isFavorite: newFavoriteStatus\n                            } : m);\n                        return new Map(prev).set(cacheKey, {\n                            models: updatedModels,\n                            timestamp: currentCache.timestamp\n                        });\n                    }\n                    return prev;\n                });\n            }\n        } catch (error) {\n            console.error(\"Error toggling favorite:\", error);\n        } finally{\n            // Remover do estado de processamento\n            setFavoriteToggling((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(model.id);\n                return newSet;\n            });\n        }\n    };\n    // Function to check if a model is expensive (over $20 per million tokens)\n    const isExpensiveModel = (model)=>{\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\") return false;\n        const totalPrice = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model);\n        return totalPrice > 0.00002; // $20 por 1M tokens = $0.00002 por token\n    };\n    const handleSelectModel = (model)=>{\n        // Rastrear seleção de modelo\n        trackModelSelection(model.id);\n        // Check if it's an expensive OpenRouter model\n        if (isExpensiveModel(model)) {\n            setPendingExpensiveModel(model);\n            setShowExpensiveModelModal(true);\n        } else {\n            onModelSelect(model.id);\n            onClose();\n        }\n    };\n    const handleConfirmExpensiveModel = ()=>{\n        if (pendingExpensiveModel) {\n            // Rastrear seleção de modelo caro\n            trackModelSelection(pendingExpensiveModel.id);\n            onModelSelect(pendingExpensiveModel.id);\n            setShowExpensiveModelModal(false);\n            setPendingExpensiveModel(null);\n            onClose();\n        }\n    };\n    const handleCancelExpensiveModel = ()=>{\n        setShowExpensiveModelModal(false);\n        setPendingExpensiveModel(null);\n    };\n    const handleLoadMoreModels = ()=>{\n        setDisplayedModelsCount((prev)=>prev + MODELS_PER_PAGE);\n    };\n    const handleUseCustomModel = ()=>{\n        if (customModelId.trim()) {\n            onModelSelect(customModelId.trim());\n            onClose();\n        }\n    };\n    // Função para forçar refresh dos modelos\n    const handleRefreshModels = ()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>{\n                const newCache = new Map(prev);\n                newCache.delete(cacheKey);\n                return newCache;\n            });\n            console.log(\"Forcing refresh for endpoint:\", selectedEndpoint.name);\n            if (selectedEndpoint.name === \"OpenRouter\") {\n                loadOpenRouterModels();\n            } else if (selectedEndpoint.name === \"DeepSeek\") {\n                loadDeepSeekModels();\n            }\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl rounded-2xl border border-blue-600/30 shadow-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none rounded-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-blue-700/30 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-blue-100\",\n                                                children: \"Selecionar Modelo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"p-2 rounded-xl hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-blue-200\",\n                                                children: \"Endpoint\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleRefreshModels,\n                                                disabled: loading || !selectedEndpoint,\n                                                className: \"p-2 rounded-lg hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                title: \"Atualizar modelos\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 \".concat(loading ? \"animate-spin\" : \"\"),\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.id) || \"\",\n                                        onChange: (e)=>{\n                                            const endpoint = endpoints.find((ep)=>ep.id === e.target.value);\n                                            setSelectedEndpoint(endpoint || null);\n                                        },\n                                        className: \"w-full bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Selecione um endpoint\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            endpoints.map((endpoint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: endpoint.id,\n                                                    className: \"bg-blue-900 text-blue-100\",\n                                                    children: endpoint.name\n                                                }, endpoint.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 17\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 9\n                    }, undefined),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-blue-700/30 space-y-6 relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-blue-900/30 backdrop-blur-sm rounded-xl border border-blue-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-blue-200 mb-3 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-blue-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Modelo Customizado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"openai/gpt-4-1\",\n                                                        value: customModelId,\n                                                        onChange: (e)=>setCustomModelId(e.target.value),\n                                                        onKeyDown: (e)=>e.key === \"Enter\" && handleUseCustomModel(),\n                                                        className: \"flex-1 bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleUseCustomModel,\n                                                        disabled: !customModelId.trim(),\n                                                        className: \"px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 disabled:from-blue-800 disabled:to-blue-800 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 disabled:hover:scale-100\",\n                                                        children: \"Usar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-300/70 mt-3\",\n                                                children: \"Digite o ID completo do modelo (ex: openai/gpt-4, anthropic/claude-3-sonnet)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 bg-blue-900/30 backdrop-blur-sm rounded-xl p-1 border border-blue-600/20\",\n                                        children: [\n                                            \"paid\",\n                                            \"free\",\n                                            \"favorites\"\n                                        ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            category\n                                                        })),\n                                                className: \"flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-all duration-200 \".concat(filters.category === category ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg\" : \"text-blue-300 hover:text-blue-200 hover:bg-blue-800/30\"),\n                                                children: category === \"paid\" ? \"Pagos\" : category === \"free\" ? \"Gr\\xe1tis\" : \"Favoritos\"\n                                            }, category, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            value: searchTerm,\n                                                            onChange: setSearchTerm,\n                                                            suggestions: [],\n                                                            isSearching: isSearching,\n                                                            placeholder: \"Buscar modelos... (ex: 'gpt-4', 'vision', 'cheap')\",\n                                                            showSuggestions: false\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: filters.sortBy,\n                                                        onChange: (e)=>setFilters((prev)=>({\n                                                                    ...prev,\n                                                                    sortBy: e.target.value\n                                                                })),\n                                                        className: \"bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                                        disabled: hasSearched && searchTerm.trim().length > 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"newest\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Mais recentes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"price_low\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Menor pre\\xe7o\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"price_high\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Maior pre\\xe7o\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"context_high\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Maior contexto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            !hasSearched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedCategory(null),\n                                                        className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 \".concat(!selectedCategory ? \"bg-blue-600 text-white\" : \"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200\"),\n                                                        children: \"Todos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    smartCategories.slice(0, 6).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setSelectedCategory(category.id),\n                                                            className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-1 \".concat(selectedCategory === category.id ? \"bg-blue-600 text-white\" : \"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200\"),\n                                                            title: category.description,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: category.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 604,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, category.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            hasSearched && searchTerm.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300\",\n                                                        children: [\n                                                            filteredModels.length,\n                                                            \" resultado\",\n                                                            filteredModels.length !== 1 ? \"s\" : \"\",\n                                                            ' para \"',\n                                                            searchTerm,\n                                                            '\"'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: clearSearch,\n                                                        className: \"text-blue-400 hover:text-blue-300 transition-colors duration-200\",\n                                                        children: \"Limpar busca\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-scroll p-6 max-h-[32rem] relative z-10\",\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-200 text-sm font-medium\",\n                                                    children: \"Carregando modelos...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-red-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-300 font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-blue-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-300 font-medium\",\n                                                children: hasSearched && searchTerm.trim() ? 'Nenhum resultado para \"'.concat(searchTerm, '\"') : selectedCategory ? \"Nenhum modelo na categoria selecionada\" : \"Nenhum modelo encontrado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-blue-400/70 text-sm mt-2 space-y-1\",\n                                                children: hasSearched && searchTerm.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Tente:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"list-disc list-inside space-y-1 text-left max-w-xs mx-auto\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Verificar a ortografia\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 672,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Usar termos mais gen\\xe9ricos\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Explorar as categorias\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Limpar filtros ativos\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Tente ajustar os filtros ou usar as categorias\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: filteredModels.slice(0, displayedModelsCount).map((model)=>{\n                                            // Encontrar o resultado da busca para este modelo (se houver)\n                                            const searchResult = hasSearched ? searchResults.find((r)=>r.model.id === model.id) : null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModelCard, {\n                                                model: model,\n                                                isSelected: currentModel === model.id,\n                                                onSelect: ()=>handleSelectModel(model),\n                                                onToggleFavorite: ()=>handleToggleFavorite(model),\n                                                isToggling: favoriteToggling.has(model.id),\n                                                service: _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService,\n                                                searchTerm: hasSearched ? searchTerm : \"\",\n                                                searchResult: searchResult\n                                            }, model.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length > displayedModelsCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLoadMoreModels,\n                                            className: \"px-6 py-3 bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm border border-blue-600/30 hover:border-blue-500/50 rounded-xl text-blue-200 hover:text-blue-100 transition-all duration-200 flex items-center space-x-2 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Carregar mais modelos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 9l-7 7-7-7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 710,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-4 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-400/70\",\n                                                children: [\n                                                    \"Mostrando \",\n                                                    Math.min(displayedModelsCount, filteredModels.length),\n                                                    \" de \",\n                                                    filteredModels.length,\n                                                    \" modelos\",\n                                                    models.length !== filteredModels.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-blue-300\",\n                                                        children: [\n                                                            \"(\",\n                                                            models.length,\n                                                            \" total)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 726,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            hasSearched && searchTerm.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-4 text-xs text-blue-400/60\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"\\uD83D\\uDD0D Busca: \",\n                                                            searchTerm\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"⚡ \",\n                                                            searchResults.length,\n                                                            \" resultados\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 740,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-slate-700/30 space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-slate-100 mb-2\",\n                                            children: \"Modelos DeepSeek\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-slate-400\",\n                                            children: \"Escolha entre nossos modelos especializados\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 756,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 753,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-6\",\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-200 text-sm font-medium\",\n                                                    children: \"Carregando modelos...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-red-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-300 font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 774,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && models.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-blue-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 790,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 789,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-300 font-medium\",\n                                                children: \"Nenhum modelo encontrado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 793,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-400/70 text-sm mt-1\",\n                                                children: \"Tente ajustar os filtros de busca\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 787,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && models.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeepSeekModelCard, {\n                                                model: model,\n                                                isSelected: currentModel === model.id,\n                                                onSelect: ()=>handleSelectModel(model)\n                                            }, model.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 801,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 763,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\" && (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"DeepSeek\" && selectedEndpoint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8 text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-blue-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 818,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-300 font-medium\",\n                                children: \"Sele\\xe7\\xe3o de modelos dispon\\xedvel para OpenRouter e DeepSeek\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 821,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-400/70 text-sm mt-1\",\n                                children: \"Selecione um desses endpoints para ver os modelos dispon\\xedveis\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 822,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 815,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 445,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: showExpensiveModelModal,\n                model: pendingExpensiveModel,\n                onConfirm: handleConfirmExpensiveModel,\n                onCancel: handleCancelExpensiveModel\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 828,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 444,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModelSelectionModal, \"aZQkJ8e05K/tmXoFd2ZCR4cCD7g=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch\n    ];\n});\n_c = ModelSelectionModal;\nconst ModelCard = (param)=>{\n    let { model, isSelected, onSelect, onToggleFavorite, isToggling = false, service = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService, searchTerm = \"\", searchResult } = param;\n    const isExpensive = service === _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService && _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model) > 0.00002; // $20 por 1M tokens\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-5 rounded-xl border transition-all duration-200 backdrop-blur-sm hover:scale-[1.02] relative \".concat(isSelected ? \"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20\" : \"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40\"),\n        children: [\n            isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-2 -right-2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 rounded-full p-1.5 shadow-lg border-2 border-slate-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 873,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 872,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 871,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 870,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-blue-100 truncate\",\n                                                        children: (searchResult === null || searchResult === void 0 ? void 0 : searchResult.highlightedName) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: searchResult.highlightedName\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 886,\n                                                            columnNumber: 21\n                                                        }, undefined) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                                            text: model.name,\n                                                            highlight: searchTerm\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 888,\n                                                            columnNumber: 21\n                                                        }, undefined) : model.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-amber-500/20 text-amber-300 px-2 py-0.5 rounded-full border border-amber-500/30 font-medium\",\n                                                        children: \"CARO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 894,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    searchResult && searchResult.matchedFields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-1\",\n                                                        children: searchResult.matchedFields.slice(0, 3).map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs bg-green-500/20 text-green-300 px-1.5 py-0.5 rounded border border-green-500/30\",\n                                                                title: \"Encontrado em: \".concat(field),\n                                                                children: field === \"name\" ? \"\\uD83D\\uDCDD\" : field === \"description\" ? \"\\uD83D\\uDCC4\" : field === \"provider\" ? \"\\uD83C\\uDFE2\" : field === \"tags\" ? \"\\uD83C\\uDFF7️\" : \"\\uD83D\\uDD0D\"\n                                                            }, field, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 901,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 899,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 883,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-300/70 truncate mt-1 font-mono\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                                    text: model.id,\n                                                    highlight: searchTerm\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 913,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 882,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    service.isFreeModel(model) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-green-600/30 text-green-300 text-xs rounded-full border border-green-500/30 font-medium\",\n                                        children: \"Gr\\xe1tis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 917,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 881,\n                                columnNumber: 11\n                            }, undefined),\n                            model.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-300/80 line-clamp-2\",\n                                    children: (searchResult === null || searchResult === void 0 ? void 0 : searchResult.highlightedDescription) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: searchResult.highlightedDescription\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 928,\n                                        columnNumber: 19\n                                    }, undefined) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                        text: model.description,\n                                        highlight: searchTerm\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 19\n                                    }, undefined) : model.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 926,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 925,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Contexto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 940,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: service.formatContextLength(model.context_length)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 939,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Input\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 944,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.prompt),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 945,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 943,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Output\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 948,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.completion),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 949,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 947,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 938,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 880,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 ml-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggleFavorite,\n                                disabled: isToggling,\n                                className: \"p-2.5 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed \".concat(model.isFavorite ? \"text-yellow-400 hover:text-yellow-300 bg-yellow-500/20 border border-yellow-500/30\" : \"text-blue-300 hover:text-yellow-400 bg-blue-800/30 border border-blue-600/20 hover:bg-yellow-500/20 hover:border-yellow-500/30\"),\n                                title: isToggling ? \"Processando...\" : model.isFavorite ? \"Remover dos favoritos\" : \"Adicionar aos favoritos\",\n                                children: isToggling ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-current\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 966,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: model.isFavorite ? \"currentColor\" : \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 969,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 968,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 955,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSelect,\n                                className: \"px-6 py-2.5 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 shadow-lg hover:shadow-blue-500/30\",\n                                children: \"Selecionar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 974,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 954,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 879,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 863,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ModelCard;\nconst DeepSeekModelCard = (param)=>{\n    let { model, isSelected, onSelect } = param;\n    const getModelIcon = (modelId)=>{\n        if (modelId === \"deepseek-chat\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-blue-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 998,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 997,\n                columnNumber: 9\n            }, undefined);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-cyan-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 1004,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1003,\n                columnNumber: 9\n            }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative p-6 rounded-2xl border transition-all duration-300 cursor-pointer group backdrop-blur-sm hover:scale-[1.02] \".concat(isSelected ? \"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20\" : \"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40\"),\n        onClick: onSelect,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-br from-blue-600/10 to-cyan-600/10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1018,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                children: getModelIcon(model.id)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1024,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-blue-100 text-lg\",\n                                        children: model.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1028,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-300/70 mt-1\",\n                                        children: model.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1027,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1023,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Contexto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1036,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatContextLength(model.context_length)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1037,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1035,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1042,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: [\n                                            _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatPrice(model.pricing.prompt),\n                                            \"/1M\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1043,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1041,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1048,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: [\n                                            _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatPrice(model.pricing.completion),\n                                            \"/1M\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1049,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1047,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1034,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            onSelect();\n                        },\n                        className: \"w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 hover:scale-105 \".concat(isSelected ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg shadow-blue-500/30\" : \"bg-blue-800/40 text-blue-200 hover:bg-gradient-to-r hover:from-blue-600 hover:to-cyan-600 hover:text-white border border-blue-600/30\"),\n                        children: isSelected ? \"Selecionado\" : \"Selecionar Modelo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1056,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1021,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 1011,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = DeepSeekModelCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ModelSelectionModal);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ModelSelectionModal\");\n$RefreshReg$(_c1, \"ModelCard\");\n$RefreshReg$(_c2, \"DeepSeekModelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\n"));

/***/ })

});