"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/attachmentService.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/attachmentService.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachmentService: function() { return /* binding */ attachmentService; }\n/* harmony export */ });\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\nclass AttachmentService {\n    /**\n   * Valida se o arquivo é suportado\n   */ validateFile(file) {\n        // Verificar tamanho\n        if (file.size > this.MAX_FILE_SIZE) {\n            return {\n                isValid: false,\n                error: \"Arquivo muito grande. M\\xe1ximo permitido: 10MB\"\n            };\n        }\n        // Verificar tipo\n        const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n        const isPdf = file.type === this.SUPPORTED_PDF_TYPE;\n        if (!isImage && !isPdf) {\n            return {\n                isValid: false,\n                error: \"Tipo de arquivo n\\xe3o suportado. Use PNG, JPEG, WebP ou PDF\"\n            };\n        }\n        return {\n            isValid: true\n        };\n    }\n    /**\n   * Converte arquivo para base64\n   */ async fileToBase64(file) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>{\n                const result = reader.result;\n                // Remover o prefixo data:type;base64,\n                const base64 = result.split(\",\")[1];\n                resolve(base64);\n            };\n            reader.onerror = reject;\n            reader.readAsDataURL(file);\n        });\n    }\n    /**\n   * Gera ID único para anexo\n   */ generateAttachmentId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Faz upload do arquivo para Firebase Storage\n   */ async uploadAttachment(params) {\n        const { file, username, chatId } = params;\n        console.log(\"=== DEBUG: ATTACHMENT SERVICE - UPLOAD INICIADO ===\");\n        console.log(\"Arquivo:\", file.name);\n        console.log(\"Username:\", username);\n        console.log(\"ChatId:\", chatId);\n        // Validar arquivo\n        const validation = this.validateFile(file);\n        console.log(\"=== DEBUG: VALIDA\\xc7\\xc3O DO ARQUIVO ===\");\n        console.log(\"Valida\\xe7\\xe3o:\", validation);\n        if (!validation.isValid) {\n            console.error(\"=== DEBUG: ARQUIVO INV\\xc1LIDO ===\");\n            console.error(\"Erro:\", validation.error);\n            throw new Error(validation.error);\n        }\n        try {\n            // Gerar ID único para o anexo\n            const attachmentId = this.generateAttachmentId();\n            console.log(\"=== DEBUG: ID DO ANEXO GERADO ===\");\n            console.log(\"AttachmentId:\", attachmentId);\n            // Determinar tipo\n            const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n            const type = isImage ? \"image\" : \"pdf\";\n            console.log(\"=== DEBUG: TIPO DO ARQUIVO ===\");\n            console.log(\"Tipo determinado:\", type);\n            console.log(\"\\xc9 imagem:\", isImage);\n            // Criar caminho no Storage\n            const storagePath = \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/anexos/\").concat(attachmentId, \"_\").concat(file.name);\n            console.log(\"=== DEBUG: CAMINHO NO STORAGE ===\");\n            console.log(\"Storage path:\", storagePath);\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.storage, storagePath);\n            console.log(\"=== DEBUG: REFER\\xcaNCIA DO STORAGE CRIADA ===\");\n            // Upload do arquivo\n            console.log(\"=== DEBUG: INICIANDO UPLOAD PARA FIREBASE STORAGE ===\");\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.uploadBytes)(storageRef, file);\n            console.log(\"=== DEBUG: UPLOAD PARA FIREBASE STORAGE CONCLU\\xcdDO ===\");\n            // Obter URL de download\n            console.log(\"=== DEBUG: OBTENDO URL DE DOWNLOAD ===\");\n            const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.getDownloadURL)(storageRef);\n            console.log(\"=== DEBUG: URL DE DOWNLOAD OBTIDA ===\");\n            console.log(\"Download URL:\", downloadURL);\n            // Preparar metadados\n            const metadata = {\n                id: attachmentId,\n                type,\n                filename: file.name,\n                url: downloadURL,\n                size: file.size,\n                uploadedAt: Date.now(),\n                storagePath,\n                isActive: true // Por padrão, anexos são ativos\n            };\n            console.log(\"=== DEBUG: METADADOS PREPARADOS ===\");\n            console.log(\"Metadata:\", metadata);\n            // Para PDFs, também converter para base64 para envio ao OpenRouter\n            let base64Data;\n            if (type === \"pdf\") {\n                console.log(\"=== DEBUG: CONVERTENDO PDF PARA BASE64 ===\");\n                base64Data = await this.fileToBase64(file);\n                metadata.base64Data = base64Data;\n                console.log(\"=== DEBUG: PDF CONVERTIDO PARA BASE64 ===\");\n                console.log(\"Base64 length:\", base64Data === null || base64Data === void 0 ? void 0 : base64Data.length);\n            }\n            const result = {\n                metadata,\n                base64Data\n            };\n            console.log(\"=== DEBUG: UPLOAD ATTACHMENT CONCLU\\xcdDO COM SUCESSO ===\");\n            console.log(\"Resultado:\", result);\n            return result;\n        } catch (error) {\n            console.error(\"=== DEBUG: ERRO NO UPLOAD ATTACHMENT ===\");\n            console.error(\"Erro:\", error);\n            throw new Error(\"Falha no upload do arquivo. Tente novamente.\");\n        }\n    }\n    /**\n   * Processa múltiplos arquivos\n   */ async uploadMultipleAttachments(files, username, chatId) {\n        console.log(\"=== DEBUG: UPLOAD M\\xdaLTIPLOS ANEXOS INICIADO ===\");\n        console.log(\"N\\xfamero de arquivos:\", files.length);\n        console.log(\"Username:\", username);\n        console.log(\"ChatId:\", chatId);\n        const results = [];\n        for(let i = 0; i < files.length; i++){\n            const file = files[i];\n            console.log(\"=== DEBUG: PROCESSANDO ARQUIVO \".concat(i + 1, \"/\").concat(files.length, \" ===\"));\n            console.log(\"Nome do arquivo:\", file.name);\n            try {\n                const result = await this.uploadAttachment({\n                    file,\n                    username,\n                    chatId\n                });\n                results.push(result);\n                console.log(\"=== DEBUG: ARQUIVO \".concat(i + 1, \" PROCESSADO COM SUCESSO ===\"));\n            } catch (error) {\n                console.error(\"=== DEBUG: ERRO AO PROCESSAR ARQUIVO \".concat(file.name, \" ===\"));\n                console.error(\"Erro:\", error);\n            // Continuar com os outros arquivos\n            }\n        }\n        console.log(\"=== DEBUG: UPLOAD M\\xdaLTIPLOS ANEXOS CONCLU\\xcdDO ===\");\n        console.log(\"Resultados:\", results.length, \"de\", files.length, \"arquivos processados\");\n        return results;\n    }\n    /**\n   * Prepara anexos para envio ao OpenRouter\n   */ prepareAttachmentsForOpenRouter(attachments) {\n        const openRouterContent = [];\n        for (const attachment of attachments){\n            if (attachment.type === \"image\") {\n                // Para imagens, usar URL\n                openRouterContent.push({\n                    type: \"image_url\",\n                    image_url: {\n                        url: attachment.url\n                    }\n                });\n            } else if (attachment.type === \"pdf\" && attachment.base64Data) {\n                // Para PDFs, usar base64 com formato file\n                const dataUrl = \"data:application/pdf;base64,\".concat(attachment.base64Data);\n                openRouterContent.push({\n                    type: \"file\",\n                    file: {\n                        filename: attachment.filename,\n                        file_data: dataUrl\n                    }\n                });\n            }\n        }\n        return openRouterContent;\n    }\n    /**\n   * Prepara plugins para PDFs (engine mistral-ocr)\n   */ preparePDFPlugins(attachments) {\n        const hasPDF = attachments.some((att)=>att.type === \"pdf\");\n        if (!hasPDF) {\n            return [];\n        }\n        return [\n            {\n                id: \"file-parser\",\n                pdf: {\n                    engine: \"mistral-ocr\"\n                }\n            }\n        ];\n    }\n    /**\n   * Limpa anexos temporários (se necessário)\n   */ async cleanupTemporaryAttachments(attachments) {\n        // Implementar limpeza se necessário\n        // Por enquanto, mantemos os arquivos no Storage\n        console.log(\"Cleanup de anexos tempor\\xe1rios:\", attachments.length);\n    }\n    constructor(){\n        this.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB\n        ;\n        this.SUPPORTED_IMAGE_TYPES = [\n            \"image/png\",\n            \"image/jpeg\",\n            \"image/webp\"\n        ];\n        this.SUPPORTED_PDF_TYPE = \"application/pdf\";\n    }\n}\n// Exportar instância singleton\nconst attachmentService = new AttachmentService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (attachmentService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/attachmentService.ts\n"));

/***/ })

});