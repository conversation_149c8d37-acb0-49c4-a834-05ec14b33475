"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _StatisticsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./StatisticsModal */ \"(app-pages-browser)/./src/components/dashboard/StatisticsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated, onUpdateOpenRouterBalance } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStatisticsModalOpen, setIsStatisticsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado para um chat específico\n    const saveLastUsedModelForChat = async (modelId, chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            // Atualizar o lastUsedModel no documento do chat\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                lastUsedModel: modelId,\n                lastModelUpdateAt: Date.now()\n            });\n            console.log(\"Last used model saved for chat:\", {\n                chatId,\n                modelId\n            });\n        } catch (error) {\n            console.error(\"Error saving last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado de um chat específico\n    const loadLastUsedModelForChat = async (chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(chatRef);\n            if (chatDoc.exists()) {\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    // Verificar se o modelo salvo ainda é válido\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (isValid) {\n                        setSelectedModel(data.lastUsedModel);\n                        console.log(\"Loaded last used model for chat:\", {\n                            chatId,\n                            model: data.lastUsedModel\n                        });\n                    } else {\n                        console.log(\"Invalid model detected in chat, clearing and using default:\", data.lastUsedModel);\n                        // Limpar o modelo inválido do chat\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                            lastUsedModel: \"\"\n                        });\n                        // Carregar o modelo padrão do endpoint ativo\n                        loadDefaultModelFromActiveEndpoint();\n                    }\n                } else {\n                    // Se o chat não tem modelo salvo, carregar o modelo padrão do endpoint ativo\n                    loadDefaultModelFromActiveEndpoint();\n                    console.log(\"No saved model for chat, loading default from active endpoint:\", chatId);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o modelo padrão do endpoint ativo\n    const loadDefaultModelFromActiveEndpoint = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                // Primeiro, tentar carregar o último modelo usado globalmente\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded global last used model:\", data.lastUsedModel);\n                    return;\n                }\n                // Se não há último modelo usado, buscar o modelo padrão do endpoint ativo\n                if (data.endpoints) {\n                    const activeEndpoint = Object.values(data.endpoints).find((endpoint)=>endpoint.ativo);\n                    if (activeEndpoint && activeEndpoint.modeloPadrao) {\n                        setSelectedModel(activeEndpoint.modeloPadrao);\n                        console.log(\"Loaded default model from active endpoint:\", activeEndpoint.modeloPadrao);\n                        return;\n                    }\n                }\n            }\n            // Fallback para o modelo padrão hardcoded\n            setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n            console.log(\"Using fallback default model\");\n        } catch (error) {\n            console.error(\"Error loading default model from active endpoint:\", error);\n            // Fallback para o modelo padrão hardcoded em caso de erro\n            setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n        }\n    };\n    // Função para validar se um modelo ainda existe/é válido\n    const isValidModel = async (modelId)=>{\n        // Lista de modelos conhecidos como inválidos ou removidos\n        const invalidModels = [\n            \"qwen/qwen3-235b-a22b-thinking-2507\"\n        ];\n        return !invalidModels.includes(modelId);\n    };\n    // Função para limpar modelos inválidos de todos os chats do usuário\n    const cleanupInvalidModelsFromAllChats = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\");\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(chatsRef);\n            const updatePromises = [];\n            for (const chatDoc of chatsSnapshot.docs){\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (!isValid) {\n                        console.log(\"Cleaning invalid model from chat:\", chatDoc.id, data.lastUsedModel);\n                        updatePromises.push((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatDoc.id), {\n                            lastUsedModel: \"\"\n                        }));\n                    }\n                }\n            }\n            if (updatePromises.length > 0) {\n                await Promise.all(updatePromises);\n                console.log(\"Cleaned invalid models from\", updatePromises.length, \"chats\");\n            }\n        } catch (error) {\n            console.error(\"Error cleaning invalid models from chats:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado globalmente (fallback)\n    const loadGlobalLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    // Verificar se o modelo salvo ainda é válido\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (isValid) {\n                        setSelectedModel(data.lastUsedModel);\n                        console.log(\"Loaded global last used model:\", data.lastUsedModel);\n                    } else {\n                        console.log(\"Invalid model detected, clearing and using default:\", data.lastUsedModel);\n                        // Limpar o modelo inválido das configurações\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                            lastUsedModel: null\n                        });\n                        // Carregar o modelo padrão do endpoint ativo\n                        loadDefaultModelFromActiveEndpoint();\n                    }\n                } else {\n                    // Se não há último modelo usado, carregar o modelo padrão do endpoint ativo\n                    loadDefaultModelFromActiveEndpoint();\n                }\n            } else {\n                // Se não há configurações, carregar o modelo padrão do endpoint ativo\n                loadDefaultModelFromActiveEndpoint();\n            }\n        } catch (error) {\n            console.error(\"Error loading global last used model:\", error);\n            // Fallback para carregar o modelo padrão do endpoint ativo\n            loadDefaultModelFromActiveEndpoint();\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        // Salvar no chat específico se houver um chat ativo\n        if (actualChatId) {\n            saveLastUsedModelForChat(modelId, actualChatId);\n        }\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            // Definir o nome do chat imediatamente após criação\n            setChatName(finalChatName);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments, webSearchEnabled)=>{\n        // Obter anexos históricos ativos\n        const historicalAttachments = getAllChatAttachments().filter((att)=>att.isActive !== false);\n        // Combinar anexos novos com anexos históricos ativos\n        const allAttachmentsToSend = [\n            ...attachments || [],\n            ...historicalAttachments // Anexos históricos ativos\n        ];\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachmentsToSend.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        console.log(\"\\uD83D\\uDE80 RESULTADO FINAL - ANEXOS QUE SER\\xc3O ENVIADOS PARA A IA:\", uniqueAttachments.length);\n        uniqueAttachments.forEach((att)=>{\n            console.log(\"\\uD83D\\uDE80 Enviando para IA: \".concat(att.filename, \" (ID: \").concat(att.id, \", isActive: \").concat(att.isActive, \")\"));\n        });\n        if (uniqueAttachments.length === 0) {\n            console.log(\"❌ NENHUM ANEXO SER\\xc1 ENVIADO PARA A IA\");\n        }\n        // Debug: verificar mensagens atuais no estado\n        console.log(\"=== DEBUG: MENSAGENS ATUAIS NO ESTADO ===\");\n        console.log(\"Total de mensagens no estado:\", messages.length);\n        const messagesWithAttachments = messages.filter((msg)=>msg.attachments && msg.attachments.length > 0);\n        console.log(\"Mensagens com anexos no estado:\", messagesWithAttachments.length);\n        messagesWithAttachments.forEach((msg, index)=>{\n            console.log(\"Mensagem \".concat(index + 1, \" com anexos:\"), {\n                id: msg.id,\n                sender: msg.sender,\n                attachmentsCount: msg.attachments && msg.attachments.length || 0,\n                attachments: msg.attachments\n            });\n        });\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            console.log(\"=== DEBUG: CONDI\\xc7\\xc3O DE RETORNO ===\");\n            console.log(\"Mensagem vazia:\", !message.trim());\n            console.log(\"Sem anexos:\", !attachments || attachments.length === 0);\n            console.log(\"Loading:\", isLoading);\n            console.log(\"Streaming:\", isStreaming);\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // O nome já foi definido na função createAutoChat, mas vamos garantir carregando também\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA (incluindo anexos históricos ativos + anexos novos)\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: uniqueAttachments,\n            webSearchEnabled: webSearchEnabled\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString(),\n                        hasWebSearch: webSearchEnabled\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            // Salvar o modelo usado no chat específico\n            if (chatIdToUse) {\n                saveLastUsedModelForChat(selectedModel, chatIdToUse);\n            }\n            // Atualizar saldo do OpenRouter após a resposta com delay de 5 segundos\n            if (onUpdateOpenRouterBalance) {\n                setTimeout(()=>{\n                    onUpdateOpenRouterBalance();\n                }, 5000);\n            }\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                const chatName = chatData.name || \"Conversa sem nome\";\n                setChatName(chatName);\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, chatId);\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(chatMessages);\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"❌ Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado globalmente quando o componente montar (apenas se não há chat)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && !currentChat) {\n            // Limpar modelos inválidos uma vez quando o usuário faz login\n            cleanupInvalidModelsFromAllChats();\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        user,\n        currentChat\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n            // Carregar o modelo específico do chat\n            loadLastUsedModelForChat(currentChat);\n        } else if (!currentChat && actualChatId) {\n            // Só resetar se realmente não há chat e havia um chat antes\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n            // Carregar modelo global quando não há chat específico\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // ✅ CORREÇÃO: Recarregar mensagens do Firebase Storage para garantir estado atualizado\n        console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o para garantir estado atualizado...\");\n        try {\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                setIsLoading(false);\n                setIsStreaming(false);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // Deletar apenas as mensagens POSTERIORES do Firebase Storage (não a mensagem atual)\n            console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n            for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                const msgToDelete = convertedFreshMessages[i];\n                console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos\n                if (onUpdateOpenRouterBalance) {\n                    setTimeout(()=>{\n                        onUpdateOpenRouterBalance();\n                    }, 5000);\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return false;\n        console.log(\"✏️ Iniciando edi\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            console.log(\"\\uD83D\\uDCE4 Enviando atualiza\\xe7\\xe3o para o servidor...\");\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                console.error(\"❌ Falha ao atualizar mensagem no servidor\");\n                loadChatMessages(actualChatId);\n                return false;\n            } else {\n                console.log(\"✅ Mensagem editada e salva com sucesso no Firebase Storage:\", {\n                    messageId,\n                    timestamp: new Date().toISOString()\n                });\n                return true;\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            console.error(\"❌ Erro ao atualizar mensagem:\", error);\n            loadChatMessages(actualChatId);\n            return false;\n        }\n    };\n    const handleEditAndRegenerate = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        console.log(\"✏️\\uD83D\\uDD04 Iniciando edi\\xe7\\xe3o e regenera\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        try {\n            // 1. Primeiro, salvar a edição\n            const editSuccess = await handleEditMessage(messageId, newContent);\n            if (!editSuccess) {\n                console.error(\"❌ Falha ao editar mensagem, cancelando regenera\\xe7\\xe3o\");\n                return;\n            }\n            console.log(\"✅ Mensagem editada com sucesso, iniciando regenera\\xe7\\xe3o...\");\n            // 2. Aguardar um pouco para garantir que a edição foi salva\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            // 3. Recarregar mensagens do Firebase Storage para garantir estado atualizado\n            console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o...\");\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // 4. Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // 5. Verificar se há mensagens após esta mensagem\n            const hasMessagesAfter = messageIndex < convertedFreshMessages.length - 1;\n            console.log(\"\\uD83D\\uDCCA Mensagens ap\\xf3s esta: \".concat(convertedFreshMessages.length - messageIndex - 1));\n            // 6. Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // 7. Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // 8. Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // 9. Deletar apenas as mensagens POSTERIORES do Firebase Storage (se houver)\n            if (hasMessagesAfter) {\n                console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n                for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                    const msgToDelete = convertedFreshMessages[i];\n                    console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                    await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n                }\n            }\n            // 10. Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos\n                if (onUpdateOpenRouterBalance) {\n                    setTimeout(()=>{\n                        onUpdateOpenRouterBalance();\n                    }, 5000);\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao editar e regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: scrollContainer.scrollHeight,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = ()=>{\n        setIsAttachmentsModalOpen(true);\n    };\n    // Função para abrir o modal de estatísticas\n    const handleStatisticsModal = ()=>{\n        setIsStatisticsModalOpen(true);\n    };\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para salvar o estado dos anexos no Firebase Storage\n    const saveAttachmentStates = async (updatedMessages)=>{\n        if (!currentUsername || !actualChatId) return;\n        try {\n            // Preparar dados do chat para salvar\n            const chatData = {\n                id: actualChatId,\n                name: chatName || \"Chat\",\n                messages: updatedMessages.map((msg)=>({\n                        id: msg.id,\n                        content: msg.content,\n                        role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                        timestamp: msg.timestamp,\n                        isFavorite: msg.isFavorite,\n                        attachments: msg.attachments\n                    })),\n                createdAt: new Date().toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            // Salvar no Firebase Storage\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(currentUsername, \"/conversas/\").concat(actualChatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(chatJsonRef, chatJsonBlob);\n            console.log(\"✅ Estado dos anexos salvo no Firebase Storage\");\n            console.log(\"\\uD83D\\uDCC1 Dados salvos:\", {\n                chatId: actualChatId,\n                totalMessages: chatData.messages.length,\n                messagesWithAttachments: chatData.messages.filter((msg)=>msg.attachments && msg.attachments.length > 0).length\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao salvar estado dos anexos:\", error);\n        }\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setMessages((prevMessages)=>{\n            const updatedMessages = prevMessages.map((message)=>{\n                if (message.attachments && message.attachments.length > 0) {\n                    const updatedAttachments = message.attachments.map((attachment)=>{\n                        if (attachment.id === attachmentId) {\n                            // Se isActive não está definido, considerar como true (ativo por padrão)\n                            const currentState = attachment.isActive !== false;\n                            return {\n                                ...attachment,\n                                isActive: !currentState\n                            };\n                        }\n                        return attachment;\n                    });\n                    return {\n                        ...message,\n                        attachments: updatedAttachments\n                    };\n                }\n                return message;\n            });\n            // Salvar o estado atualizado no Firebase Storage\n            saveAttachmentStates(updatedMessages);\n            return updatedMessages;\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        // Para anexos novos (sem isActive definido), incluir por padrão\n        // Para anexos existentes, verificar se isActive não é false\n        return attachments.filter((attachment)=>{\n            // Se isActive não está definido (anexo novo), incluir\n            if (attachment.isActive === undefined) return true;\n            // Se isActive está definido, incluir apenas se não for false\n            return attachment.isActive !== false;\n        });\n    };\n    // Função para obter IDs dos anexos ativos\n    const getActiveAttachmentIds = ()=>{\n        const allAttachments = getAllChatAttachments();\n        return allAttachments.filter((att)=>att.isActive !== false).map((att)=>att.id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                onDownload: handleDownloadModal,\n                onAttachments: handleAttachmentsModal,\n                onStatistics: handleStatisticsModal,\n                isLoading: isLoading,\n                attachmentsCount: getAllChatAttachments().length,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    isStreaming: isStreaming,\n                    streamingMessageId: streamingMessageId || undefined,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onEditAndRegenerate: handleEditAndRegenerate,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1161,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined,\n                activeAttachmentsCount: getActiveAttachmentIds().length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1176,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: getActiveAttachmentIds(),\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatisticsModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: isStatisticsModalOpen,\n                onClose: ()=>setIsStatisticsModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1219,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 1143,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"Hf7a+h7SnkMddq+yigBa/N2m+7g=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9DaGF0QXJlYS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRW9EO0FBQ21EO0FBQ25EO0FBQ1A7QUFDSTtBQUNmO0FBQ1U7QUFDVjtBQUVVO0FBQ1k7QUFDTjtBQUNGO0FBRUM7QUE0QmxDLFNBQVN3QixTQUFTLEtBQXdFO1FBQXhFLEVBQUVDLFdBQVcsRUFBRUMsYUFBYSxFQUFFQyx5QkFBeUIsRUFBaUIsR0FBeEU7O0lBQy9CLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUdiLDhEQUFPQTtJQUN4QixNQUFNLENBQUNjLFNBQVNDLFdBQVcsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQytCLFdBQVdDLGFBQWEsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2lDLFVBQVVDLFlBQVksR0FBR2xDLCtDQUFRQSxDQUFZLEVBQUU7SUFDdEQsTUFBTSxDQUFDbUMsZUFBZUMsaUJBQWlCLEdBQUdwQywrQ0FBUUEsQ0FBQztJQUVuRCxNQUFNLENBQUNxQyxjQUFjQyxnQkFBZ0IsR0FBR3RDLCtDQUFRQSxDQUFnQnlCO0lBQ2hFLE1BQU0sQ0FBQ2MscUJBQXFCQyx1QkFBdUIsR0FBR3hDLCtDQUFRQSxDQUFDO0lBQy9ELE1BQU0sQ0FBQ3lDLGtCQUFrQkMsb0JBQW9CLEdBQUcxQywrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUMyQyx3QkFBd0JDLDBCQUEwQixHQUFHNUMsK0NBQVFBLENBQUM7SUFDckUsTUFBTSxDQUFDNkMsdUJBQXVCQyx5QkFBeUIsR0FBRzlDLCtDQUFRQSxDQUFDO0lBQ25FLE1BQU0sQ0FBQytDLGFBQWFDLGVBQWUsR0FBR2hELCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2lELG9CQUFvQkMsc0JBQXNCLEdBQUdsRCwrQ0FBUUEsQ0FBZ0I7SUFDNUUsTUFBTSxDQUFDbUQsVUFBVUMsWUFBWSxHQUFHcEQsK0NBQVFBLENBQVM7SUFHakQsTUFBTSxDQUFDcUQsZUFBZUMsaUJBQWlCLEdBQUd0RCwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUN1RCxpQkFBaUJDLG1CQUFtQixHQUFHeEQsK0NBQVFBLENBQXFCeUQ7SUFDM0UsTUFBTUMsbUJBQW1CeEQsNkNBQU1BLENBQWlCO0lBRWhELHdEQUF3RDtJQUN4REQsZ0RBQVNBLENBQUM7UUFDUixNQUFNMEQsZUFBZTtZQUNuQixJQUFJL0IsaUJBQUFBLDJCQUFBQSxLQUFNZ0MsS0FBSyxFQUFFO2dCQUNmLE1BQU1DLFdBQVcsTUFBTUM7Z0JBQ3ZCTixtQkFBbUJLO1lBQ3JCO1FBQ0Y7UUFDQUY7SUFDRixHQUFHO1FBQUMvQixpQkFBQUEsMkJBQUFBLEtBQU1nQyxLQUFLO0tBQUM7SUFFaEIsaURBQWlEO0lBQ2pELE1BQU1FLDJCQUEyQjtRQUMvQixJQUFJLEVBQUNsQyxpQkFBQUEsMkJBQUFBLEtBQU1nQyxLQUFLLEdBQUUsT0FBTztRQUV6QixJQUFJO1lBQ0YsTUFBTUcsY0FBYzFELDhEQUFVQSxDQUFDUSw2Q0FBRUEsRUFBRTtZQUNuQyxNQUFNbUQsSUFBSXhELHlEQUFLQSxDQUFDdUQsYUFBYXRELHlEQUFLQSxDQUFDLFNBQVMsTUFBTW1CLEtBQUtnQyxLQUFLO1lBQzVELE1BQU1LLGdCQUFnQixNQUFNdkQsMkRBQU9BLENBQUNzRDtZQUVwQyxJQUFJLENBQUNDLGNBQWNDLEtBQUssRUFBRTtnQkFDeEIsTUFBTUMsVUFBVUYsY0FBY0csSUFBSSxDQUFDLEVBQUU7Z0JBQ3JDLE1BQU1DLFdBQVdGLFFBQVFHLElBQUk7Z0JBQzdCLE9BQU9ELFNBQVNSLFFBQVEsSUFBSWpDLEtBQUtnQyxLQUFLLENBQUNXLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUN0RDtZQUVBLE9BQU8zQyxLQUFLZ0MsS0FBSyxDQUFDVyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsRUFBRSxXQUFXO1FBQzlDLEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQyxPQUFPNUMsS0FBS2dDLEtBQUssQ0FBQ1csS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUUsV0FBVztRQUM5QztJQUNGO0lBRUEsbUVBQW1FO0lBQ25FLE1BQU1HLDJCQUEyQixPQUFPQyxTQUFpQkM7UUFDdkQsSUFBSSxDQUFDaEQsUUFBUSxDQUFDZ0QsUUFBUTtRQUV0QixJQUFJO1lBQ0YsTUFBTWYsV0FBVyxNQUFNQztZQUN2QixNQUFNZSxVQUFVMUUsdURBQUdBLENBQUNVLDZDQUFFQSxFQUFFLFlBQVlnRCxVQUFVLGFBQWFlO1lBRTNELGlEQUFpRDtZQUNqRCxNQUFNckUsNkRBQVNBLENBQUNzRSxTQUFTO2dCQUN2QkMsZUFBZUg7Z0JBQ2ZJLG1CQUFtQkMsS0FBS0MsR0FBRztZQUM3QjtZQUVBUixRQUFRUyxHQUFHLENBQUMsbUNBQW1DO2dCQUFFTjtnQkFBUUQ7WUFBUTtRQUNuRSxFQUFFLE9BQU9ILE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDBDQUEwQ0E7UUFDMUQ7SUFDRjtJQUVBLG1FQUFtRTtJQUNuRSxNQUFNVywyQkFBMkIsT0FBT1A7UUFDdEMsSUFBSSxDQUFDaEQsUUFBUSxDQUFDZ0QsUUFBUTtRQUV0QixJQUFJO1lBQ0YsTUFBTWYsV0FBVyxNQUFNQztZQUN2QixNQUFNZSxVQUFVMUUsdURBQUdBLENBQUNVLDZDQUFFQSxFQUFFLFlBQVlnRCxVQUFVLGFBQWFlO1lBQzNELE1BQU1RLFVBQVUsTUFBTTlFLDBEQUFNQSxDQUFDdUU7WUFFN0IsSUFBSU8sUUFBUUMsTUFBTSxJQUFJO2dCQUNwQixNQUFNZixPQUFPYyxRQUFRZCxJQUFJO2dCQUN6QixJQUFJQSxLQUFLUSxhQUFhLEVBQUU7b0JBQ3RCLDZDQUE2QztvQkFDN0MsTUFBTVEsVUFBVSxNQUFNQyxhQUFhakIsS0FBS1EsYUFBYTtvQkFDckQsSUFBSVEsU0FBUzt3QkFDWGxELGlCQUFpQmtDLEtBQUtRLGFBQWE7d0JBQ25DTCxRQUFRUyxHQUFHLENBQUMsb0NBQW9DOzRCQUFFTjs0QkFBUVksT0FBT2xCLEtBQUtRLGFBQWE7d0JBQUM7b0JBQ3RGLE9BQU87d0JBQ0xMLFFBQVFTLEdBQUcsQ0FBQywrREFBK0RaLEtBQUtRLGFBQWE7d0JBQzdGLG1DQUFtQzt3QkFDbkMsTUFBTXZFLDZEQUFTQSxDQUFDc0UsU0FBUzs0QkFDdkJDLGVBQWU7d0JBQ2pCO3dCQUNBLDZDQUE2Qzt3QkFDN0NXO29CQUNGO2dCQUNGLE9BQU87b0JBQ0wsNkVBQTZFO29CQUM3RUE7b0JBQ0FoQixRQUFRUyxHQUFHLENBQUMsa0VBQWtFTjtnQkFDaEY7WUFDRjtRQUNGLEVBQUUsT0FBT0osT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMkNBQTJDQTtRQUMzRDtJQUNGO0lBRUEseURBQXlEO0lBQ3pELE1BQU1pQixxQ0FBcUM7UUFDekMsSUFBSSxDQUFDN0QsTUFBTTtRQUVYLElBQUk7WUFDRixNQUFNaUMsV0FBVyxNQUFNQztZQUN2QixNQUFNNEIsVUFBVXZGLHVEQUFHQSxDQUFDVSw2Q0FBRUEsRUFBRSxZQUFZZ0QsVUFBVSxpQkFBaUI7WUFDL0QsTUFBTU0sVUFBVSxNQUFNN0QsMERBQU1BLENBQUNvRjtZQUU3QixJQUFJdkIsUUFBUWtCLE1BQU0sSUFBSTtnQkFDcEIsTUFBTWYsT0FBT0gsUUFBUUcsSUFBSTtnQkFFekIsOERBQThEO2dCQUM5RCxJQUFJQSxLQUFLUSxhQUFhLEVBQUU7b0JBQ3RCMUMsaUJBQWlCa0MsS0FBS1EsYUFBYTtvQkFDbkNMLFFBQVFTLEdBQUcsQ0FBQyxrQ0FBa0NaLEtBQUtRLGFBQWE7b0JBQ2hFO2dCQUNGO2dCQUVBLDBFQUEwRTtnQkFDMUUsSUFBSVIsS0FBS3FCLFNBQVMsRUFBRTtvQkFDbEIsTUFBTUMsaUJBQWlCQyxPQUFPQyxNQUFNLENBQUN4QixLQUFLcUIsU0FBUyxFQUFFSSxJQUFJLENBQUMsQ0FBQ0MsV0FBa0JBLFNBQVNDLEtBQUs7b0JBQzNGLElBQUlMLGtCQUFrQixlQUF3Qk0sWUFBWSxFQUFFO3dCQUMxRDlELGlCQUFpQixlQUF3QjhELFlBQVk7d0JBQ3JEekIsUUFBUVMsR0FBRyxDQUFDLDhDQUE4QyxlQUF3QmdCLFlBQVk7d0JBQzlGO29CQUNGO2dCQUNGO1lBQ0Y7WUFFQSwwQ0FBMEM7WUFDMUM5RCxpQkFBaUI7WUFDakJxQyxRQUFRUyxHQUFHLENBQUM7UUFDZCxFQUFFLE9BQU9WLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHFEQUFxREE7WUFDbkUsMERBQTBEO1lBQzFEcEMsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSx5REFBeUQ7SUFDekQsTUFBTW1ELGVBQWUsT0FBT1o7UUFDMUIsMERBQTBEO1FBQzFELE1BQU13QixnQkFBZ0I7WUFDcEI7U0FFRDtRQUVELE9BQU8sQ0FBQ0EsY0FBY0MsUUFBUSxDQUFDekI7SUFDakM7SUFFQSxvRUFBb0U7SUFDcEUsTUFBTTBCLG1DQUFtQztRQUN2QyxJQUFJLENBQUN6RSxNQUFNO1FBRVgsSUFBSTtZQUNGLE1BQU1pQyxXQUFXLE1BQU1DO1lBQ3ZCLE1BQU13QyxXQUFXakcsOERBQVVBLENBQUNRLDZDQUFFQSxFQUFFLFlBQVlnRCxVQUFVO1lBQ3RELE1BQU0wQyxnQkFBZ0IsTUFBTTdGLDJEQUFPQSxDQUFDNEY7WUFFcEMsTUFBTUUsaUJBQWtDLEVBQUU7WUFFMUMsS0FBSyxNQUFNcEIsV0FBV21CLGNBQWNuQyxJQUFJLENBQUU7Z0JBQ3hDLE1BQU1FLE9BQU9jLFFBQVFkLElBQUk7Z0JBQ3pCLElBQUlBLEtBQUtRLGFBQWEsRUFBRTtvQkFDdEIsTUFBTVEsVUFBVSxNQUFNQyxhQUFhakIsS0FBS1EsYUFBYTtvQkFDckQsSUFBSSxDQUFDUSxTQUFTO3dCQUNaYixRQUFRUyxHQUFHLENBQUMscUNBQXFDRSxRQUFRcUIsRUFBRSxFQUFFbkMsS0FBS1EsYUFBYTt3QkFDL0UwQixlQUFlRSxJQUFJLENBQ2pCbkcsNkRBQVNBLENBQUNKLHVEQUFHQSxDQUFDVSw2Q0FBRUEsRUFBRSxZQUFZZ0QsVUFBVSxhQUFhdUIsUUFBUXFCLEVBQUUsR0FBRzs0QkFDaEUzQixlQUFlO3dCQUNqQjtvQkFFSjtnQkFDRjtZQUNGO1lBRUEsSUFBSTBCLGVBQWVHLE1BQU0sR0FBRyxHQUFHO2dCQUM3QixNQUFNQyxRQUFRQyxHQUFHLENBQUNMO2dCQUNsQi9CLFFBQVFTLEdBQUcsQ0FBQywrQkFBK0JzQixlQUFlRyxNQUFNLEVBQUU7WUFDcEU7UUFDRixFQUFFLE9BQU9uQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw2Q0FBNkNBO1FBQzdEO0lBQ0Y7SUFFQSxvRUFBb0U7SUFDcEUsTUFBTXNDLDBCQUEwQjtRQUM5QixJQUFJLENBQUNsRixNQUFNO1FBRVgsSUFBSTtZQUNGLE1BQU1pQyxXQUFXLE1BQU1DO1lBQ3ZCLE1BQU00QixVQUFVdkYsdURBQUdBLENBQUNVLDZDQUFFQSxFQUFFLFlBQVlnRCxVQUFVLGlCQUFpQjtZQUMvRCxNQUFNTSxVQUFVLE1BQU03RCwwREFBTUEsQ0FBQ29GO1lBRTdCLElBQUl2QixRQUFRa0IsTUFBTSxJQUFJO2dCQUNwQixNQUFNZixPQUFPSCxRQUFRRyxJQUFJO2dCQUN6QixJQUFJQSxLQUFLUSxhQUFhLEVBQUU7b0JBQ3RCLDZDQUE2QztvQkFDN0MsTUFBTVEsVUFBVSxNQUFNQyxhQUFhakIsS0FBS1EsYUFBYTtvQkFDckQsSUFBSVEsU0FBUzt3QkFDWGxELGlCQUFpQmtDLEtBQUtRLGFBQWE7d0JBQ25DTCxRQUFRUyxHQUFHLENBQUMsa0NBQWtDWixLQUFLUSxhQUFhO29CQUNsRSxPQUFPO3dCQUNMTCxRQUFRUyxHQUFHLENBQUMsdURBQXVEWixLQUFLUSxhQUFhO3dCQUNyRiw2Q0FBNkM7d0JBQzdDLE1BQU12RSw2REFBU0EsQ0FBQ21GLFNBQVM7NEJBQ3ZCWixlQUFlO3dCQUNqQjt3QkFDQSw2Q0FBNkM7d0JBQzdDVztvQkFDRjtnQkFDRixPQUFPO29CQUNMLDRFQUE0RTtvQkFDNUVBO2dCQUNGO1lBQ0YsT0FBTztnQkFDTCxzRUFBc0U7Z0JBQ3RFQTtZQUNGO1FBQ0YsRUFBRSxPQUFPakIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMseUNBQXlDQTtZQUN2RCwyREFBMkQ7WUFDM0RpQjtRQUNGO0lBQ0Y7SUFFQSxxRUFBcUU7SUFDckUsTUFBTXNCLG9CQUFvQixDQUFDcEM7UUFDekJ2QyxpQkFBaUJ1QztRQUVqQixvREFBb0Q7UUFDcEQsSUFBSXRDLGNBQWM7WUFDaEJxQyx5QkFBeUJDLFNBQVN0QztRQUNwQztJQUNGO0lBRUEsNENBQTRDO0lBQzVDLE1BQU0yRSxpQkFBaUIsT0FBT0M7UUFDNUIsSUFBSSxFQUFDckYsaUJBQUFBLDJCQUFBQSxLQUFNZ0MsS0FBSyxHQUFFLE9BQU87UUFFekIsSUFBSTtZQUNGLDZCQUE2QjtZQUM3QixNQUFNRyxjQUFjMUQsOERBQVVBLENBQUNRLDZDQUFFQSxFQUFFO1lBQ25DLE1BQU1tRCxJQUFJeEQseURBQUtBLENBQUN1RCxhQUFhdEQseURBQUtBLENBQUMsU0FBUyxNQUFNbUIsS0FBS2dDLEtBQUs7WUFDNUQsTUFBTUssZ0JBQWdCLE1BQU12RCwyREFBT0EsQ0FBQ3NEO1lBRXBDLElBQUlDLGNBQWNDLEtBQUssRUFBRSxPQUFPO1lBRWhDLE1BQU1DLFVBQVVGLGNBQWNHLElBQUksQ0FBQyxFQUFFO1lBQ3JDLE1BQU1DLFdBQVdGLFFBQVFHLElBQUk7WUFDN0IsTUFBTVQsV0FBV1EsU0FBU1IsUUFBUTtZQUVsQyw2QkFBNkI7WUFDN0IsTUFBTXFELFlBQVlsQyxLQUFLQyxHQUFHO1lBQzFCLE1BQU1rQyxTQUFTQyxLQUFLRCxNQUFNLEdBQUdFLFFBQVEsQ0FBQyxJQUFJQyxTQUFTLENBQUMsR0FBRztZQUN2RCxNQUFNMUMsU0FBUyxRQUFxQnVDLE9BQWJELFdBQVUsS0FBVSxPQUFQQztZQUNwQyxNQUFNbEMsTUFBTSxJQUFJRCxPQUFPdUMsV0FBVztZQUVsQywyRUFBMkU7WUFDM0UsSUFBSUMsZ0JBQWdCO1lBQ3BCLElBQUlQLGFBQWFRLElBQUksR0FBR2QsTUFBTSxHQUFHLEdBQUc7Z0JBQ2xDLE1BQU1lLFFBQVFULGFBQWFRLElBQUksR0FBR2xELEtBQUssQ0FBQztnQkFDeEMsTUFBTXBCLFdBQVd1RSxNQUFNQyxLQUFLLENBQUMsR0FBR1AsS0FBS1EsR0FBRyxDQUFDLEdBQUdGLE1BQU1mLE1BQU0sR0FBR2tCLElBQUksQ0FBQztnQkFDaEVMLGdCQUFnQnJFLFNBQVN3RCxNQUFNLEdBQUcsS0FBS3hELFNBQVNtRSxTQUFTLENBQUMsR0FBRyxNQUFNLFFBQVFuRTtZQUM3RTtZQUVBLHlCQUF5QjtZQUN6QixNQUFNMkUsZ0JBQWdCO2dCQUNwQkMsU0FBUztnQkFDVEMsV0FBVy9DO2dCQUNYZ0QsVUFBVTtnQkFDVkMsa0JBQWtCO2dCQUNsQkMsU0FBUztnQkFDVEMsZUFBZW5EO2dCQUNmSCxlQUFlM0M7Z0JBQ2ZrRyxtQkFBbUI7Z0JBQ25CQyxXQUFXO2dCQUNYQyxNQUFNZjtnQkFDTmdCLFVBQVU7Z0JBQ1ZDLG1CQUFtQjtnQkFDbkJDLGFBQWE7b0JBQ1hDLGtCQUFrQjFEO29CQUNsQjJELGFBQWEzRDtvQkFDYjRELFdBQVc7Z0JBQ2I7Z0JBQ0FDLGNBQWM7Z0JBQ2RDLGFBQWE7Z0JBQ2JDLGdCQUFnQi9CLGdCQUFnQjtnQkFDaENnQyxrQkFBa0JoRTtnQkFDbEJpRSxXQUFXakU7WUFDYjtZQUVBLCtCQUErQjtZQUMvQixNQUFNN0UsMERBQU1BLENBQUNELHVEQUFHQSxDQUFDVSw2Q0FBRUEsRUFBRSxZQUFZZ0QsVUFBVSxhQUFhZSxTQUFTa0Q7WUFFakUscUNBQXFDO1lBQ3JDLE1BQU1xQixlQUFlO2dCQUNuQjFDLElBQUk3QjtnQkFDSjJELE1BQU1mO2dCQUNOdkYsVUFBVSxFQUFFO2dCQUNaK0YsV0FBVy9DO2dCQUNYMkQsYUFBYTNEO1lBQ2Y7WUFFQSxNQUFNbUUsZUFBZSxJQUFJQyxLQUFLO2dCQUFDQyxLQUFLQyxTQUFTLENBQUNKLGNBQWMsTUFBTTthQUFHLEVBQUU7Z0JBQ3JFSyxNQUFNO1lBQ1I7WUFFQSxNQUFNQyxhQUFhOUkscURBQUdBLENBQUNHLGtEQUFPQSxFQUFFLFlBQWtDOEQsT0FBdEJmLFVBQVMsZUFBb0IsT0FBUGUsUUFBTztZQUN6RSxNQUFNaEUsNkRBQVdBLENBQUM2SSxZQUFZTDtZQUU5QjNFLFFBQVFTLEdBQUcsQ0FBQyxnQ0FBZ0NOO1lBQzVDLG9EQUFvRDtZQUNwRHhCLFlBQVlvRTtZQUNaLE9BQU81QztRQUVULEVBQUUsT0FBT0osT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsdUNBQXVDQTtZQUNyRCxPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU1rRixvQkFBb0IsT0FBT0MsYUFBK0RDO1FBRTlGLGlDQUFpQztRQUNqQyxNQUFNQyx3QkFBd0JDLHdCQUF3QkMsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxRQUFRLEtBQUs7UUFFckYscURBQXFEO1FBQ3JELE1BQU1DLHVCQUF1QjtlQUN2QlAsZUFBZSxFQUFFO2VBQ2xCRSxzQkFBc0IsMkJBQTJCO1NBQ3JEO1FBRUQsbUNBQW1DO1FBQ25DLE1BQU1NLG9CQUFvQkQscUJBQXFCSCxNQUFNLENBQUMsQ0FBQ0ssWUFBWUMsT0FBT0MsT0FDeEVELFVBQVVDLEtBQUtDLFNBQVMsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRS9ELEVBQUUsS0FBSzJELFdBQVczRCxFQUFFO1FBR3REaEMsUUFBUVMsR0FBRyxDQUFDLDBFQUE2RGlGLGtCQUFrQnhELE1BQU07UUFDakd3RCxrQkFBa0JNLE9BQU8sQ0FBQ1QsQ0FBQUE7WUFDeEJ2RixRQUFRUyxHQUFHLENBQUMsa0NBQTZDOEUsT0FBckJBLElBQUlVLFFBQVEsRUFBQyxVQUE2QlYsT0FBckJBLElBQUl2RCxFQUFFLEVBQUMsZ0JBQTJCLE9BQWJ1RCxJQUFJQyxRQUFRLEVBQUM7UUFDN0Y7UUFFQSxJQUFJRSxrQkFBa0J4RCxNQUFNLEtBQUssR0FBRztZQUNsQ2xDLFFBQVFTLEdBQUcsQ0FBQztRQUNkO1FBRUEsOENBQThDO1FBQzlDVCxRQUFRUyxHQUFHLENBQUM7UUFDWlQsUUFBUVMsR0FBRyxDQUFDLGlDQUFpQ2pELFNBQVMwRSxNQUFNO1FBQzVELE1BQU1nRSwwQkFBMEIxSSxTQUFTOEgsTUFBTSxDQUFDYSxDQUFBQSxNQUFPQSxJQUFJakIsV0FBVyxJQUFJaUIsSUFBSWpCLFdBQVcsQ0FBQ2hELE1BQU0sR0FBRztRQUNuR2xDLFFBQVFTLEdBQUcsQ0FBQyxtQ0FBbUN5Rix3QkFBd0JoRSxNQUFNO1FBQzdFZ0Usd0JBQXdCRixPQUFPLENBQUMsQ0FBQ0csS0FBS1A7WUFDcEM1RixRQUFRUyxHQUFHLENBQUMsWUFBc0IsT0FBVm1GLFFBQVEsR0FBRSxpQkFBZTtnQkFDL0M1RCxJQUFJbUUsSUFBSW5FLEVBQUU7Z0JBQ1ZvRSxRQUFRRCxJQUFJQyxNQUFNO2dCQUNsQkMsa0JBQWtCLElBQUtuQixXQUFXLElBQUlpQixJQUFJakIsV0FBVyxDQUFDaEQsTUFBTSxJQUFLO2dCQUNqRWdELGFBQWFpQixJQUFJakIsV0FBVztZQUM5QjtRQUNGO1FBRUEsSUFBSSxDQUFFOUgsUUFBUTRGLElBQUksTUFBTyxFQUFDa0MsZUFBZUEsWUFBWWhELE1BQU0sS0FBSyxNQUFPNUUsYUFBYWdCLGFBQWE7WUFDL0YwQixRQUFRUyxHQUFHLENBQUM7WUFDWlQsUUFBUVMsR0FBRyxDQUFDLG1CQUFtQixDQUFDckQsUUFBUTRGLElBQUk7WUFDNUNoRCxRQUFRUyxHQUFHLENBQUMsZUFBZSxDQUFDeUUsZUFBZUEsWUFBWWhELE1BQU0sS0FBSztZQUNsRWxDLFFBQVFTLEdBQUcsQ0FBQyxZQUFZbkQ7WUFDeEIwQyxRQUFRUyxHQUFHLENBQUMsY0FBY25DO1lBQzFCO1FBQ0Y7UUFDQSxJQUFJLEVBQUNuQixpQkFBQUEsMkJBQUFBLEtBQU1nQyxLQUFLLEdBQUU7UUFFbEIsTUFBTW1ILGNBQXVCO1lBQzNCdEUsSUFBSWxGLGdFQUFTQSxDQUFDeUosaUJBQWlCO1lBQy9CQyxTQUFTcEosUUFBUTRGLElBQUk7WUFDckJvRCxRQUFRO1lBQ1IzRCxXQUFXLElBQUlsQyxPQUFPdUMsV0FBVztZQUNqQ29DLGFBQWFBLGVBQWUsRUFBRTtRQUNoQztRQUVBLGlEQUFpRDtRQUNqRCxJQUFJdUIsY0FBYzdJO1FBQ2xCLElBQUksQ0FBQzZJLGFBQWE7WUFDaEIsTUFBTUMsaUJBQWlCdEosUUFBUTRGLElBQUksTUFBT2tDLENBQUFBLGVBQWVBLFlBQVloRCxNQUFNLEdBQUcsSUFBSSxrQkFBa0IsZUFBYztZQUNsSHVFLGNBQWMsTUFBTWxFLGVBQWVtRTtZQUNuQyxJQUFJRCxhQUFhO2dCQUNmNUksZ0JBQWdCNEk7Z0JBQ2hCLHdGQUF3RjtnQkFDeEZFLGFBQWFGO2dCQUNieEosMEJBQUFBLG9DQUFBQSxjQUFnQndKO1lBQ2xCO1FBQ0Y7UUFFQSxJQUFJLENBQUNBLGFBQWE7WUFDaEJ6RyxRQUFRRCxLQUFLLENBQUM7WUFDZDtRQUNGO1FBRUEsZ0NBQWdDO1FBQ2hDdEMsWUFBWW1KLENBQUFBLE9BQVE7bUJBQUlBO2dCQUFNTjthQUFZO1FBQzFDLE1BQU1PLGlCQUFpQnpKLFFBQVE0RixJQUFJLE1BQU0sSUFBSSwyQ0FBMkM7UUFDeEYzRixXQUFXO1FBQ1hFLGFBQWE7UUFDYmdCLGVBQWU7UUFFZix3RUFBd0U7UUFDeEUsTUFBTXVJLGNBQWNoSyxnRUFBU0EsQ0FBQ3lKLGlCQUFpQjtRQUMvQzlILHNCQUFzQnFJO1FBRXRCLHFDQUFxQztRQUNyQyxNQUFNMUgsV0FBVyxNQUFNQztRQUV2Qix1RUFBdUU7UUFDdkUsTUFBTXZDLGdFQUFTQSxDQUFDaUssZUFBZSxDQUM3QjtZQUNFM0gsVUFBVUE7WUFDVmUsUUFBUXNHO1lBQ1JySixTQUFTeUo7WUFDVDlGLE9BQU9yRDtZQUNQd0gsYUFBYVE7WUFDYlAsa0JBQWtCQTtRQUNwQixHQUNBLHdFQUF3RTtRQUN4RSxDQUFDNkI7WUFDQ3ZKLFlBQVltSixDQUFBQTtnQkFDViwwQ0FBMEM7Z0JBQzFDLE1BQU1LLHVCQUF1QkwsS0FBS2QsU0FBUyxDQUFDSyxDQUFBQSxNQUFPQSxJQUFJbkUsRUFBRSxLQUFLOEU7Z0JBRTlELElBQUlHLHlCQUF5QixDQUFDLEdBQUc7b0JBQy9CLCtCQUErQjtvQkFDL0IsT0FBT0wsS0FBS00sR0FBRyxDQUFDZixDQUFBQSxNQUNkQSxJQUFJbkUsRUFBRSxLQUFLOEUsY0FDUDs0QkFBRSxHQUFHWCxHQUFHOzRCQUFFSyxTQUFTTCxJQUFJSyxPQUFPLEdBQUdRO3dCQUFNLElBQ3ZDYjtnQkFFUixPQUFPO29CQUNMLDhDQUE4QztvQkFDOUMsbUVBQW1FO29CQUNuRTVJLGFBQWE7b0JBRWIsTUFBTTRKLFlBQXFCO3dCQUN6Qm5GLElBQUk4RTt3QkFDSk4sU0FBU1E7d0JBQ1RaLFFBQVE7d0JBQ1IzRCxXQUFXLElBQUlsQyxPQUFPdUMsV0FBVzt3QkFDakNzRSxjQUFjakM7b0JBQ2hCO29CQUNBLE9BQU87MkJBQUl5Qjt3QkFBTU87cUJBQVU7Z0JBQzdCO1lBQ0Y7UUFDRixHQUNBLG1DQUFtQztRQUNuQyxDQUFDRTtZQUNDNUosWUFBWW1KLENBQUFBLE9BQVFBLEtBQUtNLEdBQUcsQ0FBQ2YsQ0FBQUEsTUFDM0JBLElBQUluRSxFQUFFLEtBQUs4RSxjQUNQO3dCQUFFLEdBQUdYLEdBQUc7d0JBQUVLLFNBQVNhO29CQUFhLElBQ2hDbEI7WUFFTjVJLGFBQWE7WUFDYmdCLGVBQWU7WUFDZkUsc0JBQXNCO1lBRXRCLDJDQUEyQztZQUMzQyxJQUFJZ0ksYUFBYTtnQkFDZnhHLHlCQUF5QnZDLGVBQWUrSTtZQUMxQztZQUVBLHdFQUF3RTtZQUN4RSxJQUFJdkosMkJBQTJCO2dCQUM3Qm9LLFdBQVc7b0JBQ1RwSztnQkFDRixHQUFHO1lBQ0w7UUFDRixHQUNBLHlCQUF5QjtRQUN6QixDQUFDNkM7WUFDQ0MsUUFBUUQsS0FBSyxDQUFDLGVBQWVBO1lBQzdCdEMsWUFBWW1KLENBQUFBLE9BQVFBLEtBQUtNLEdBQUcsQ0FBQ2YsQ0FBQUEsTUFDM0JBLElBQUluRSxFQUFFLEtBQUs4RSxjQUNQO3dCQUFFLEdBQUdYLEdBQUc7d0JBQUVLLFNBQVMsV0FBaUIsT0FBTnpHO29CQUFRLElBQ3RDb0c7WUFFTjVJLGFBQWE7WUFDYmdCLGVBQWU7WUFDZkUsc0JBQXNCO1FBQ3hCO0lBRUo7SUFFQSxpQ0FBaUM7SUFDakMsTUFBTThJLHdCQUF3QjtRQUM1QnpLLGdFQUFTQSxDQUFDMEssYUFBYTtRQUN2QmpLLGFBQWE7UUFDYmdCLGVBQWU7UUFDZkUsc0JBQXNCO0lBQ3hCO0lBRUEsbURBQW1EO0lBQ25ELE1BQU1rSSxlQUFlLE9BQU94RztRQUMxQixJQUFJLEVBQUNoRCxpQkFBQUEsMkJBQUFBLEtBQU1nQyxLQUFLLEdBQUU7UUFFbEIsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTUM7WUFDdkIsTUFBTXNCLFVBQVUsTUFBTTlFLDBEQUFNQSxDQUFDSCx1REFBR0EsQ0FBQ1UsNkNBQUVBLEVBQUUsWUFBWWdELFVBQVUsYUFBYWU7WUFFeEUsSUFBSVEsUUFBUUMsTUFBTSxJQUFJO2dCQUNwQixNQUFNNkcsV0FBVzlHLFFBQVFkLElBQUk7Z0JBQzdCLE1BQU1uQixXQUFXK0ksU0FBUzNELElBQUksSUFBSTtnQkFDbENuRixZQUFZRDtZQUNkLE9BQU87Z0JBQ0xDLFlBQVk7WUFDZDtRQUNGLEVBQUUsT0FBT29CLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGtDQUFrQ0E7WUFDaERwQixZQUFZO1FBQ2Q7SUFDRjtJQUVBLG9EQUFvRDtJQUNwRCxNQUFNK0ksbUJBQW1CLE9BQU92SDtRQUM5QixJQUFJLEVBQUNoRCxpQkFBQUEsMkJBQUFBLEtBQU1nQyxLQUFLLEdBQUU7UUFFbEJOLGlCQUFpQjtRQUNqQixJQUFJO1lBQ0YsTUFBTU8sV0FBVyxNQUFNQztZQUN2QixNQUFNc0ksZUFBZSxNQUFNN0ssZ0VBQVNBLENBQUM0SyxnQkFBZ0IsQ0FBQ3RJLFVBQVVlO1lBQ2hFLE1BQU15SCxvQkFBb0I5SyxnRUFBU0EsQ0FBQytLLG1CQUFtQixDQUFDRjtZQUN4RGxLLFlBQVltSztRQUNkLEVBQUUsT0FBTzdILE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHlDQUF5Q0E7WUFDdkR0QyxZQUFZLEVBQUU7UUFDaEIsU0FBVTtZQUNSb0IsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSw4RkFBOEY7SUFDOUZyRCxnREFBU0EsQ0FBQztRQUNSLElBQUkyQixRQUFRLENBQUNILGFBQWE7WUFDeEIsOERBQThEO1lBQzlENEU7WUFDQVM7UUFDRjtJQUNGLEdBQUc7UUFBQ2xGO1FBQU1IO0tBQVk7SUFFdEIsK0NBQStDO0lBQy9DeEIsZ0RBQVNBLENBQUM7UUFDUixJQUFJd0IsZUFBZUEsZ0JBQWdCWSxjQUFjO1lBQy9DQyxnQkFBZ0JiO1lBQ2hCNkIsaUJBQWlCO1lBQ2pCLHVFQUF1RTtZQUN2RXBCLFlBQVksRUFBRTtZQUNkaUssaUJBQWlCMUs7WUFDakIySixhQUFhM0o7WUFDYix1Q0FBdUM7WUFDdkMwRCx5QkFBeUIxRDtRQUMzQixPQUFPLElBQUksQ0FBQ0EsZUFBZVksY0FBYztZQUN2Qyw0REFBNEQ7WUFDNURDLGdCQUFnQjtZQUNoQkosWUFBWSxFQUFFO1lBQ2RrQixZQUFZO1lBQ1pFLGlCQUFpQjtZQUNqQix1REFBdUQ7WUFDdkR3RDtRQUNGO0lBQ0YsR0FBRztRQUFDckY7UUFBYUcsaUJBQUFBLDJCQUFBQSxLQUFNZ0MsS0FBSztLQUFDO0lBRTdCLG1DQUFtQztJQUNuQyxNQUFNMkksc0JBQXNCLE9BQU9DO1FBQ2pDLElBQUksQ0FBQ25LLGdCQUFnQixFQUFDVCxpQkFBQUEsMkJBQUFBLEtBQU1nQyxLQUFLLEdBQUU7UUFFbkMsOENBQThDO1FBQzlDMUIsWUFBWW1KLENBQUFBLE9BQVFBLEtBQUt0QixNQUFNLENBQUNhLENBQUFBLE1BQU9BLElBQUluRSxFQUFFLEtBQUsrRjtRQUVsRCxJQUFJO1lBQ0YsTUFBTTNJLFdBQVcsTUFBTUM7WUFDdkIsTUFBTTJJLFVBQVUsTUFBTWxMLGdFQUFTQSxDQUFDbUwsYUFBYSxDQUFDN0ksVUFBVXhCLGNBQWNtSztZQUV0RSxJQUFJLENBQUNDLFNBQVM7Z0JBQ1osa0NBQWtDO2dCQUNsQ04saUJBQWlCOUo7Z0JBQ2pCb0MsUUFBUUQsS0FBSyxDQUFDO1lBQ2hCO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2Qsa0NBQWtDO1lBQ2xDMkgsaUJBQWlCOUo7WUFDakJvQyxRQUFRRCxLQUFLLENBQUMsNkJBQTZCQTtRQUM3QztJQUNGO0lBRUEsTUFBTW1JLDBCQUEwQixPQUFPSDtRQUNyQyxJQUFJLENBQUNuSyxnQkFBZ0IsRUFBQ1QsaUJBQUFBLDJCQUFBQSxLQUFNZ0MsS0FBSyxHQUFFO1FBRW5DLHVGQUF1RjtRQUN2RmEsUUFBUVMsR0FBRyxDQUFDO1FBRVosSUFBSTtZQUNGLE1BQU1yQixXQUFXLE1BQU1DO1lBRXZCLHFEQUFxRDtZQUNyRCxNQUFNOEksZ0JBQWdCLE1BQU1yTCxnRUFBU0EsQ0FBQzRLLGdCQUFnQixDQUFDdEksVUFBVXhCO1lBQ2pFLE1BQU13Syx5QkFBeUJ0TCxnRUFBU0EsQ0FBQytLLG1CQUFtQixDQUFDTTtZQUU3RG5JLFFBQVFTLEdBQUcsQ0FBQyxtREFBeUMySCx1QkFBdUJsRyxNQUFNO1lBRWxGLG9EQUFvRDtZQUNwRCxNQUFNbUcsZUFBZUQsdUJBQXVCdEMsU0FBUyxDQUFDSyxDQUFBQSxNQUFPQSxJQUFJbkUsRUFBRSxLQUFLK0Y7WUFDeEUsSUFBSU0saUJBQWlCLENBQUMsR0FBRztnQkFDdkJySSxRQUFRRCxLQUFLLENBQUMsb0RBQThDZ0k7Z0JBQzVEeEssYUFBYTtnQkFDYmdCLGVBQWU7Z0JBQ2Y7WUFDRjtZQUVBLE1BQU0rSixzQkFBc0JGLHNCQUFzQixDQUFDQyxhQUFhO1lBQ2hFckksUUFBUVMsR0FBRyxDQUFDLGlEQUFvQztnQkFDOUN1QixJQUFJc0csb0JBQW9CdEcsRUFBRTtnQkFDMUJ3RSxTQUFTOEIsb0JBQW9COUIsT0FBTyxDQUFDM0QsU0FBUyxDQUFDLEdBQUcsT0FBTztnQkFDekQrQyxPQUFPeUM7WUFDVDtZQUVBLDhFQUE4RTtZQUM5RSxNQUFNRSw2QkFBNkJILHVCQUF1QmxGLEtBQUssQ0FBQyxHQUFHbUYsZUFBZTtZQUNsRjVLLFlBQVk4SztZQUVaLGlEQUFpRDtZQUNqRGxMLFdBQVdpTCxvQkFBb0I5QixPQUFPO1lBQ3RDakosYUFBYTtZQUNiZ0IsZUFBZTtZQUVmLDZFQUE2RTtZQUM3RSxNQUFNdUksY0FBY2hLLGdFQUFTQSxDQUFDeUosaUJBQWlCO1lBQy9DOUgsc0JBQXNCcUk7WUFFdEIscUZBQXFGO1lBQ3JGOUcsUUFBUVMsR0FBRyxDQUFDLDJCQUFrRSxPQUFqRDJILHVCQUF1QmxHLE1BQU0sR0FBR21HLGVBQWUsR0FBRTtZQUM5RSxJQUFLLElBQUlHLElBQUlILGVBQWUsR0FBR0csSUFBSUosdUJBQXVCbEcsTUFBTSxFQUFFc0csSUFBSztnQkFDckUsTUFBTUMsY0FBY0wsc0JBQXNCLENBQUNJLEVBQUU7Z0JBQzdDeEksUUFBUVMsR0FBRyxDQUFDLHFDQUEyQmdJLFlBQVl6RyxFQUFFO2dCQUNyRCxNQUFNbEYsZ0VBQVNBLENBQUNtTCxhQUFhLENBQUM3SSxVQUFVeEIsY0FBYzZLLFlBQVl6RyxFQUFFO1lBQ3RFO1lBRUEsa0NBQWtDO1lBQ2xDLE1BQU1sRixnRUFBU0EsQ0FBQ2lLLGVBQWUsQ0FDN0I7Z0JBQ0UzSCxVQUFVQTtnQkFDVmUsUUFBUXZDO2dCQUNSUixTQUFTa0wsb0JBQW9COUIsT0FBTztnQkFDcEN6RixPQUFPckQ7Z0JBQ1BnTCxnQkFBZ0I7WUFDbEIsR0FDQSx3RUFBd0U7WUFDeEUsQ0FBQzFCO2dCQUNDdkosWUFBWW1KLENBQUFBO29CQUNWLDBDQUEwQztvQkFDMUMsTUFBTUssdUJBQXVCTCxLQUFLZCxTQUFTLENBQUNLLENBQUFBLE1BQU9BLElBQUluRSxFQUFFLEtBQUs4RTtvQkFFOUQsSUFBSUcseUJBQXlCLENBQUMsR0FBRzt3QkFDL0IsK0JBQStCO3dCQUMvQixPQUFPTCxLQUFLTSxHQUFHLENBQUNmLENBQUFBLE1BQ2RBLElBQUluRSxFQUFFLEtBQUs4RSxjQUNQO2dDQUFFLEdBQUdYLEdBQUc7Z0NBQUVLLFNBQVNMLElBQUlLLE9BQU8sR0FBR1E7NEJBQU0sSUFDdkNiO29CQUVSLE9BQU87d0JBQ0wsOENBQThDO3dCQUM5QyxtRUFBbUU7d0JBQ25FNUksYUFBYTt3QkFFYixNQUFNNEosWUFBcUI7NEJBQ3pCbkYsSUFBSThFOzRCQUNKTixTQUFTUTs0QkFDVFosUUFBUTs0QkFDUjNELFdBQVcsSUFBSWxDLE9BQU91QyxXQUFXOzRCQUNqQ3NFLGNBQWM7d0JBQ2hCO3dCQUNBLE9BQU87K0JBQUlSOzRCQUFNTzt5QkFBVTtvQkFDN0I7Z0JBQ0Y7WUFDRixHQUNBLG1DQUFtQztZQUNuQyxDQUFDRTtnQkFDQzVKLFlBQVltSixDQUFBQSxPQUFRQSxLQUFLTSxHQUFHLENBQUNmLENBQUFBLE1BQzNCQSxJQUFJbkUsRUFBRSxLQUFLOEUsY0FDUDs0QkFBRSxHQUFHWCxHQUFHOzRCQUFFSyxTQUFTYTt3QkFBYSxJQUNoQ2xCO2dCQUVONUksYUFBYTtnQkFDYmdCLGVBQWU7Z0JBQ2ZFLHNCQUFzQjtnQkFDdEJwQixXQUFXLEtBQUssMEJBQTBCO2dCQUUxQywyRUFBMkU7Z0JBQzNFLElBQUlILDJCQUEyQjtvQkFDN0JvSyxXQUFXO3dCQUNUcEs7b0JBQ0YsR0FBRztnQkFDTDtZQUNGLEdBQ0EseUJBQXlCO1lBQ3pCLENBQUM2QztnQkFDQ0MsUUFBUUQsS0FBSyxDQUFDLDhCQUF3QkE7Z0JBQ3RDdEMsWUFBWW1KLENBQUFBLE9BQVFBLEtBQUtNLEdBQUcsQ0FBQ2YsQ0FBQUEsTUFDM0JBLElBQUluRSxFQUFFLEtBQUs4RSxjQUNQOzRCQUFFLEdBQUdYLEdBQUc7NEJBQUVLLFNBQVMsZ0NBQWdDLE9BQU56Rzt3QkFBUSxJQUNyRG9HO2dCQUVONUksYUFBYTtnQkFDYmdCLGVBQWU7Z0JBQ2ZFLHNCQUFzQjtnQkFDdEJwQixXQUFXLEtBQUssMEJBQTBCO1lBQzVDO1FBR0osRUFBRSxPQUFPMEMsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQ3hDLGFBQWE7WUFDYmdCLGVBQWU7WUFDZkUsc0JBQXNCO1lBQ3RCcEIsV0FBVyxLQUFLLDBCQUEwQjtZQUUxQyx1Q0FBdUM7WUFDdkNxSyxpQkFBaUI5SjtRQUNuQjtJQUNGO0lBSUEsTUFBTStLLG9CQUFvQixPQUFPWixXQUFtQmE7UUFDbEQsSUFBSSxDQUFDaEwsZ0JBQWdCLEVBQUNULGlCQUFBQSwyQkFBQUEsS0FBTWdDLEtBQUssR0FBRSxPQUFPO1FBRTFDYSxRQUFRUyxHQUFHLENBQUMsMENBQW9DO1lBQzlDc0g7WUFDQTVILFFBQVF2QztZQUNSaUwsa0JBQWtCRCxXQUFXMUcsTUFBTTtZQUNuQzRHLG1CQUFtQkYsV0FBVy9GLFNBQVMsQ0FBQyxHQUFHLE9BQU87UUFDcEQ7UUFFQSxnREFBZ0Q7UUFDaERwRixZQUFZbUosQ0FBQUEsT0FBUUEsS0FBS00sR0FBRyxDQUFDZixDQUFBQSxNQUMzQkEsSUFBSW5FLEVBQUUsS0FBSytGLFlBQVk7b0JBQUUsR0FBRzVCLEdBQUc7b0JBQUVLLFNBQVNvQztnQkFBVyxJQUFJekM7UUFHM0QsSUFBSTtZQUNGLE1BQU0vRyxXQUFXLE1BQU1DO1lBQ3ZCVyxRQUFRUyxHQUFHLENBQUM7WUFFWixNQUFNdUgsVUFBVSxNQUFNbEwsZ0VBQVNBLENBQUNpTSxhQUFhLENBQUMzSixVQUFVeEIsY0FBY21LLFdBQVdhO1lBRWpGLElBQUksQ0FBQ1osU0FBUztnQkFDWiwyQ0FBMkM7Z0JBQzNDaEksUUFBUUQsS0FBSyxDQUFDO2dCQUNkMkgsaUJBQWlCOUo7Z0JBQ2pCLE9BQU87WUFDVCxPQUFPO2dCQUNMb0MsUUFBUVMsR0FBRyxDQUFDLCtEQUErRDtvQkFDekVzSDtvQkFDQXRGLFdBQVcsSUFBSWxDLE9BQU91QyxXQUFXO2dCQUNuQztnQkFDQSxPQUFPO1lBQ1Q7UUFDRixFQUFFLE9BQU8vQyxPQUFPO1lBQ2QsMkNBQTJDO1lBQzNDQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQzJILGlCQUFpQjlKO1lBQ2pCLE9BQU87UUFDVDtJQUNGO0lBRUEsTUFBTW9MLDBCQUEwQixPQUFPakIsV0FBbUJhO1FBQ3hELElBQUksQ0FBQ2hMLGdCQUFnQixFQUFDVCxpQkFBQUEsMkJBQUFBLEtBQU1nQyxLQUFLLEdBQUU7UUFFbkNhLFFBQVFTLEdBQUcsQ0FBQywwRUFBb0Q7WUFDOURzSDtZQUNBNUgsUUFBUXZDO1lBQ1JpTCxrQkFBa0JELFdBQVcxRyxNQUFNO1lBQ25DNEcsbUJBQW1CRixXQUFXL0YsU0FBUyxDQUFDLEdBQUcsT0FBTztRQUNwRDtRQUVBLElBQUk7WUFDRiwrQkFBK0I7WUFDL0IsTUFBTW9HLGNBQWMsTUFBTU4sa0JBQWtCWixXQUFXYTtZQUN2RCxJQUFJLENBQUNLLGFBQWE7Z0JBQ2hCakosUUFBUUQsS0FBSyxDQUFDO2dCQUNkO1lBQ0Y7WUFFQUMsUUFBUVMsR0FBRyxDQUFDO1lBRVosNERBQTREO1lBQzVELE1BQU0sSUFBSTBCLFFBQVErRyxDQUFBQSxVQUFXNUIsV0FBVzRCLFNBQVM7WUFFakQsOEVBQThFO1lBQzlFbEosUUFBUVMsR0FBRyxDQUFDO1lBRVosTUFBTXJCLFdBQVcsTUFBTUM7WUFFdkIscURBQXFEO1lBQ3JELE1BQU04SSxnQkFBZ0IsTUFBTXJMLGdFQUFTQSxDQUFDNEssZ0JBQWdCLENBQUN0SSxVQUFVeEI7WUFDakUsTUFBTXdLLHlCQUF5QnRMLGdFQUFTQSxDQUFDK0ssbUJBQW1CLENBQUNNO1lBRTdEbkksUUFBUVMsR0FBRyxDQUFDLG1EQUF5QzJILHVCQUF1QmxHLE1BQU07WUFFbEYsdURBQXVEO1lBQ3ZELE1BQU1tRyxlQUFlRCx1QkFBdUJ0QyxTQUFTLENBQUNLLENBQUFBLE1BQU9BLElBQUluRSxFQUFFLEtBQUsrRjtZQUN4RSxJQUFJTSxpQkFBaUIsQ0FBQyxHQUFHO2dCQUN2QnJJLFFBQVFELEtBQUssQ0FBQyxvREFBOENnSTtnQkFDNUQ7WUFDRjtZQUVBLE1BQU1PLHNCQUFzQkYsc0JBQXNCLENBQUNDLGFBQWE7WUFDaEVySSxRQUFRUyxHQUFHLENBQUMsaURBQW9DO2dCQUM5Q3VCLElBQUlzRyxvQkFBb0J0RyxFQUFFO2dCQUMxQndFLFNBQVM4QixvQkFBb0I5QixPQUFPLENBQUMzRCxTQUFTLENBQUMsR0FBRyxPQUFPO2dCQUN6RCtDLE9BQU95QztZQUNUO1lBRUEsa0RBQWtEO1lBQ2xELE1BQU1jLG1CQUFtQmQsZUFBZUQsdUJBQXVCbEcsTUFBTSxHQUFHO1lBQ3hFbEMsUUFBUVMsR0FBRyxDQUFDLHdDQUE0RSxPQUFqRDJILHVCQUF1QmxHLE1BQU0sR0FBR21HLGVBQWU7WUFFdEYsaUZBQWlGO1lBQ2pGLE1BQU1FLDZCQUE2QkgsdUJBQXVCbEYsS0FBSyxDQUFDLEdBQUdtRixlQUFlO1lBQ2xGNUssWUFBWThLO1lBRVosb0RBQW9EO1lBQ3BEbEwsV0FBV2lMLG9CQUFvQjlCLE9BQU87WUFDdENqSixhQUFhO1lBQ2JnQixlQUFlO1lBRWYsZ0ZBQWdGO1lBQ2hGLE1BQU11SSxjQUFjaEssZ0VBQVNBLENBQUN5SixpQkFBaUI7WUFDL0M5SCxzQkFBc0JxSTtZQUV0Qiw2RUFBNkU7WUFDN0UsSUFBSXFDLGtCQUFrQjtnQkFDcEJuSixRQUFRUyxHQUFHLENBQUMsMkJBQWtFLE9BQWpEMkgsdUJBQXVCbEcsTUFBTSxHQUFHbUcsZUFBZSxHQUFFO2dCQUM5RSxJQUFLLElBQUlHLElBQUlILGVBQWUsR0FBR0csSUFBSUosdUJBQXVCbEcsTUFBTSxFQUFFc0csSUFBSztvQkFDckUsTUFBTUMsY0FBY0wsc0JBQXNCLENBQUNJLEVBQUU7b0JBQzdDeEksUUFBUVMsR0FBRyxDQUFDLHFDQUEyQmdJLFlBQVl6RyxFQUFFO29CQUNyRCxNQUFNbEYsZ0VBQVNBLENBQUNtTCxhQUFhLENBQUM3SSxVQUFVeEIsY0FBYzZLLFlBQVl6RyxFQUFFO2dCQUN0RTtZQUNGO1lBRUEsc0NBQXNDO1lBQ3RDLE1BQU1sRixnRUFBU0EsQ0FBQ2lLLGVBQWUsQ0FDN0I7Z0JBQ0UzSCxVQUFVQTtnQkFDVmUsUUFBUXZDO2dCQUNSUixTQUFTa0wsb0JBQW9COUIsT0FBTztnQkFDcEN6RixPQUFPckQ7Z0JBQ1BnTCxnQkFBZ0I7WUFDbEIsR0FDQSx3RUFBd0U7WUFDeEUsQ0FBQzFCO2dCQUNDdkosWUFBWW1KLENBQUFBO29CQUNWLDBDQUEwQztvQkFDMUMsTUFBTUssdUJBQXVCTCxLQUFLZCxTQUFTLENBQUNLLENBQUFBLE1BQU9BLElBQUluRSxFQUFFLEtBQUs4RTtvQkFFOUQsSUFBSUcseUJBQXlCLENBQUMsR0FBRzt3QkFDL0IsK0JBQStCO3dCQUMvQixPQUFPTCxLQUFLTSxHQUFHLENBQUNmLENBQUFBLE1BQ2RBLElBQUluRSxFQUFFLEtBQUs4RSxjQUNQO2dDQUFFLEdBQUdYLEdBQUc7Z0NBQUVLLFNBQVNMLElBQUlLLE9BQU8sR0FBR1E7NEJBQU0sSUFDdkNiO29CQUVSLE9BQU87d0JBQ0wsOENBQThDO3dCQUM5QyxtRUFBbUU7d0JBQ25FNUksYUFBYTt3QkFFYixNQUFNNEosWUFBcUI7NEJBQ3pCbkYsSUFBSThFOzRCQUNKTixTQUFTUTs0QkFDVFosUUFBUTs0QkFDUjNELFdBQVcsSUFBSWxDLE9BQU91QyxXQUFXOzRCQUNqQ3NFLGNBQWM7d0JBQ2hCO3dCQUNBLE9BQU87K0JBQUlSOzRCQUFNTzt5QkFBVTtvQkFDN0I7Z0JBQ0Y7WUFDRixHQUNBLG1DQUFtQztZQUNuQyxDQUFDRTtnQkFDQzVKLFlBQVltSixDQUFBQSxPQUFRQSxLQUFLTSxHQUFHLENBQUNmLENBQUFBLE1BQzNCQSxJQUFJbkUsRUFBRSxLQUFLOEUsY0FDUDs0QkFBRSxHQUFHWCxHQUFHOzRCQUFFSyxTQUFTYTt3QkFBYSxJQUNoQ2xCO2dCQUVONUksYUFBYTtnQkFDYmdCLGVBQWU7Z0JBQ2ZFLHNCQUFzQjtnQkFDdEJwQixXQUFXLEtBQUssMEJBQTBCO2dCQUUxQywyRUFBMkU7Z0JBQzNFLElBQUlILDJCQUEyQjtvQkFDN0JvSyxXQUFXO3dCQUNUcEs7b0JBQ0YsR0FBRztnQkFDTDtZQUNGLEdBQ0EseUJBQXlCO1lBQ3pCLENBQUM2QztnQkFDQ0MsUUFBUUQsS0FBSyxDQUFDLDhCQUF3QkE7Z0JBQ3RDdEMsWUFBWW1KLENBQUFBLE9BQVFBLEtBQUtNLEdBQUcsQ0FBQ2YsQ0FBQUEsTUFDM0JBLElBQUluRSxFQUFFLEtBQUs4RSxjQUNQOzRCQUFFLEdBQUdYLEdBQUc7NEJBQUVLLFNBQVMsZ0NBQWdDLE9BQU56Rzt3QkFBUSxJQUNyRG9HO2dCQUVONUksYUFBYTtnQkFDYmdCLGVBQWU7Z0JBQ2ZFLHNCQUFzQjtnQkFDdEJwQixXQUFXLEtBQUssMEJBQTBCO1lBQzVDO1FBR0osRUFBRSxPQUFPMEMsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMENBQTBDQTtZQUN4RHhDLGFBQWE7WUFDYmdCLGVBQWU7WUFDZkUsc0JBQXNCO1lBQ3RCcEIsV0FBVyxLQUFLLDBCQUEwQjtZQUUxQyx1Q0FBdUM7WUFDdkNxSyxpQkFBaUI5SjtRQUNuQjtJQUNGO0lBRUEsTUFBTXdMLG9CQUFvQixDQUFDNUM7UUFDekI2QyxVQUFVQyxTQUFTLENBQUNDLFNBQVMsQ0FBQy9DLFNBQVNnRCxJQUFJLENBQUM7WUFDMUN4SixRQUFRUyxHQUFHLENBQUM7UUFDZDtJQUNGO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU1nSixvQkFBb0I7WUFDQXhLO1FBQXhCLE1BQU15SyxtQkFBa0J6Syw0QkFBQUEsaUJBQWlCMEssT0FBTyxjQUF4QjFLLGdEQUFBQSwwQkFBMEIySyxhQUFhLENBQUM7UUFDaEUsSUFBSUYsaUJBQWlCO1lBQ25CQSxnQkFBZ0JHLFFBQVEsQ0FBQztnQkFBRUMsS0FBSztnQkFBR0MsVUFBVTtZQUFTO1FBQ3hEO0lBQ0Y7SUFFQSxNQUFNQyx1QkFBdUI7WUFDSC9LO1FBQXhCLE1BQU15SyxtQkFBa0J6Syw0QkFBQUEsaUJBQWlCMEssT0FBTyxjQUF4QjFLLGdEQUFBQSwwQkFBMEIySyxhQUFhLENBQUM7UUFDaEUsSUFBSUYsaUJBQWlCO1lBQ25CQSxnQkFBZ0JHLFFBQVEsQ0FBQztnQkFDdkJDLEtBQUtKLGdCQUFnQk8sWUFBWTtnQkFDakNGLFVBQVU7WUFDWjtRQUNGO0lBQ0Y7SUFJQSw2REFBNkQ7SUFDN0QsTUFBTUcsd0JBQXdCLENBQUMxTTtRQUM3QixPQUFPQSxTQUFTMEosR0FBRyxDQUFDZixDQUFBQSxNQUFRO2dCQUMxQm5FLElBQUltRSxJQUFJbkUsRUFBRTtnQkFDVndFLFNBQVNMLElBQUlLLE9BQU87Z0JBQ3BCMkQsTUFBTWhFLElBQUlDLE1BQU0sS0FBSyxTQUFTLFNBQVM7Z0JBQ3ZDM0QsV0FBVyxJQUFJbEMsS0FBSzRGLElBQUkxRCxTQUFTLEVBQUUySCxPQUFPO2dCQUMxQ0MsWUFBWWxFLElBQUlrRSxVQUFVLElBQUk7Z0JBQzlCbkYsYUFBYWlCLElBQUlqQixXQUFXLElBQUksRUFBRTtZQUNwQztJQUNGO0lBRUEsd0NBQXdDO0lBQ3hDLE1BQU1vRixzQkFBc0I7UUFDMUJ2TSx1QkFBdUI7SUFDekI7SUFFQSxzQ0FBc0M7SUFDdEMsTUFBTXdNLHlCQUF5QjtRQUM3QnBNLDBCQUEwQjtJQUM1QjtJQUVBLDRDQUE0QztJQUM1QyxNQUFNcU0sd0JBQXdCO1FBQzVCbk0seUJBQXlCO0lBQzNCO0lBRUEsNENBQTRDO0lBQzVDLE1BQU1nSCx3QkFBd0I7UUFDNUIsTUFBTW9GLGlCQUFrRSxFQUFFO1FBRTFFak4sU0FBU3dJLE9BQU8sQ0FBQzVJLENBQUFBO1lBQ2YsSUFBSUEsUUFBUThILFdBQVcsSUFBSTlILFFBQVE4SCxXQUFXLENBQUNoRCxNQUFNLEdBQUcsR0FBRztnQkFDekR1SSxlQUFleEksSUFBSSxJQUFJN0UsUUFBUThILFdBQVc7WUFDNUM7UUFDRjtRQUVBLG1DQUFtQztRQUNuQyxNQUFNUSxvQkFBb0IrRSxlQUFlbkYsTUFBTSxDQUFDLENBQUNLLFlBQVlDLE9BQU9DLE9BQ2xFRCxVQUFVQyxLQUFLQyxTQUFTLENBQUNDLENBQUFBLElBQUtBLEVBQUUvRCxFQUFFLEtBQUsyRCxXQUFXM0QsRUFBRTtRQUd0RCxPQUFPMEQ7SUFDVDtJQUVBLDZEQUE2RDtJQUM3RCxNQUFNZ0YsdUJBQXVCLE9BQU9DO1FBQ2xDLElBQUksQ0FBQzdMLG1CQUFtQixDQUFDbEIsY0FBYztRQUV2QyxJQUFJO1lBQ0YscUNBQXFDO1lBQ3JDLE1BQU02SixXQUFXO2dCQUNmekYsSUFBSXBFO2dCQUNKa0csTUFBTXBGLFlBQVk7Z0JBQ2xCbEIsVUFBVW1OLGdCQUFnQnpELEdBQUcsQ0FBQ2YsQ0FBQUEsTUFBUTt3QkFDcENuRSxJQUFJbUUsSUFBSW5FLEVBQUU7d0JBQ1Z3RSxTQUFTTCxJQUFJSyxPQUFPO3dCQUNwQjJELE1BQU1oRSxJQUFJQyxNQUFNLEtBQUssU0FBUyxTQUFTO3dCQUN2QzNELFdBQVcwRCxJQUFJMUQsU0FBUzt3QkFDeEI0SCxZQUFZbEUsSUFBSWtFLFVBQVU7d0JBQzFCbkYsYUFBYWlCLElBQUlqQixXQUFXO29CQUM5QjtnQkFDQTNCLFdBQVcsSUFBSWhELE9BQU91QyxXQUFXO2dCQUNqQ3FCLGFBQWEsSUFBSTVELE9BQU91QyxXQUFXO1lBQ3JDO1lBRUEsNkJBQTZCO1lBQzdCLE1BQU02QixlQUFlLElBQUlDLEtBQUs7Z0JBQUNDLEtBQUtDLFNBQVMsQ0FBQzJDLFVBQVUsTUFBTTthQUFHLEVBQUU7Z0JBQ2pFMUMsTUFBTTtZQUNSO1lBRUEsTUFBTTZGLGNBQWMxTyxxREFBR0EsQ0FBQ0csa0RBQU9BLEVBQUUsWUFBeUN1QixPQUE3QmtCLGlCQUFnQixlQUEwQixPQUFibEIsY0FBYTtZQUN2RixNQUFNekIsNkRBQVdBLENBQUN5TyxhQUFhakc7WUFFL0IzRSxRQUFRUyxHQUFHLENBQUM7WUFDWlQsUUFBUVMsR0FBRyxDQUFDLDhCQUFvQjtnQkFDOUJOLFFBQVF2QztnQkFDUmlOLGVBQWVwRCxTQUFTakssUUFBUSxDQUFDMEUsTUFBTTtnQkFDdkNnRSx5QkFBeUJ1QixTQUFTakssUUFBUSxDQUFDOEgsTUFBTSxDQUFDYSxDQUFBQSxNQUFPQSxJQUFJakIsV0FBVyxJQUFJaUIsSUFBSWpCLFdBQVcsQ0FBQ2hELE1BQU0sR0FBRyxHQUFHQSxNQUFNO1lBQ2hIO1FBQ0YsRUFBRSxPQUFPbkMsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsdUNBQXVDQTtRQUN2RDtJQUNGO0lBRUEsa0RBQWtEO0lBQ2xELE1BQU0rSyx5QkFBeUIsQ0FBQ0M7UUFDOUJ0TixZQUFZdU4sQ0FBQUE7WUFDVixNQUFNTCxrQkFBa0JLLGFBQWE5RCxHQUFHLENBQUM5SixDQUFBQTtnQkFDdkMsSUFBSUEsUUFBUThILFdBQVcsSUFBSTlILFFBQVE4SCxXQUFXLENBQUNoRCxNQUFNLEdBQUcsR0FBRztvQkFDekQsTUFBTStJLHFCQUFxQjdOLFFBQVE4SCxXQUFXLENBQUNnQyxHQUFHLENBQUN2QixDQUFBQTt3QkFDakQsSUFBSUEsV0FBVzNELEVBQUUsS0FBSytJLGNBQWM7NEJBQ2xDLHlFQUF5RTs0QkFDekUsTUFBTUcsZUFBZXZGLFdBQVdILFFBQVEsS0FBSzs0QkFDN0MsT0FBTztnQ0FBRSxHQUFHRyxVQUFVO2dDQUFFSCxVQUFVLENBQUMwRjs0QkFBYTt3QkFDbEQ7d0JBQ0EsT0FBT3ZGO29CQUNUO29CQUNBLE9BQU87d0JBQUUsR0FBR3ZJLE9BQU87d0JBQUU4SCxhQUFhK0Y7b0JBQW1CO2dCQUN2RDtnQkFDQSxPQUFPN047WUFDVDtZQUVBLGlEQUFpRDtZQUNqRHNOLHFCQUFxQkM7WUFFckIsT0FBT0E7UUFDVDtJQUNGO0lBRUEsb0RBQW9EO0lBQ3BELE1BQU1RLHVCQUF1QixDQUFDakc7UUFDNUIsSUFBSSxDQUFDQSxhQUFhLE9BQU8sRUFBRTtRQUUzQixnRUFBZ0U7UUFDaEUsNERBQTREO1FBQzVELE9BQU9BLFlBQVlJLE1BQU0sQ0FBQ0ssQ0FBQUE7WUFDeEIsc0RBQXNEO1lBQ3RELElBQUlBLFdBQVdILFFBQVEsS0FBS3hHLFdBQVcsT0FBTztZQUM5Qyw2REFBNkQ7WUFDN0QsT0FBTzJHLFdBQVdILFFBQVEsS0FBSztRQUNqQztJQUNGO0lBSUEsMENBQTBDO0lBQzFDLE1BQU00Rix5QkFBeUI7UUFDN0IsTUFBTVgsaUJBQWlCcEY7UUFDdkIsT0FBT29GLGVBQWVuRixNQUFNLENBQUNDLENBQUFBLE1BQU9BLElBQUlDLFFBQVEsS0FBSyxPQUFPMEIsR0FBRyxDQUFDM0IsQ0FBQUEsTUFBT0EsSUFBSXZELEVBQUU7SUFDL0U7SUFFQSxxQkFDRSw4REFBQ3FKO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDL08saURBQVFBO2dCQUNQUyxhQUFhQTtnQkFDYjBCLFVBQVVBO2dCQUNWNk0sU0FBUzdOO2dCQUNUOE4sWUFBWWxCO2dCQUNabUIsZUFBZWxCO2dCQUNmbUIsY0FBY2xCO2dCQUNkbE4sV0FBV0E7Z0JBQ1grSSxrQkFBa0JoQix3QkFBd0JuRCxNQUFNO2dCQUNoRHlKLFlBQVk7b0JBQ1ZDLFNBQVM7Z0JBQ1g7Ozs7OzswQkFJRiw4REFBQ1A7Z0JBQUluUCxLQUFLK0M7Z0JBQWtCcU0sV0FBVTtnQkFBaUJPLE9BQU87b0JBQUVDLFFBQVE7Z0JBQXNCOzBCQUM1Riw0RUFBQ3RQLHNEQUFhQTtvQkFDWmdCLFVBQVVBO29CQUNWRixXQUFXQTtvQkFDWHNCLGVBQWVBO29CQUNmTixhQUFhQTtvQkFDYkUsb0JBQW9CQSxzQkFBc0JRO29CQUMxQytNLGlCQUFpQmpFO29CQUNqQmtFLHFCQUFxQjlEO29CQUNyQitELGVBQWV0RDtvQkFDZnVELHFCQUFxQmxEO29CQUNyQm1ELGVBQWUvQzs7Ozs7Ozs7Ozs7MEJBS25CLDhEQUFDM00saURBQVFBO2dCQUNQVyxTQUFTQTtnQkFDVEMsWUFBWUE7Z0JBQ1orTyxlQUFlbkg7Z0JBQ2YzSCxXQUFXQTtnQkFDWEksZUFBZUE7Z0JBQ2YyTyxlQUFlL0o7Z0JBQ2ZnSyxlQUFlN0M7Z0JBQ2Y4QyxrQkFBa0J2QztnQkFDbEIxTCxhQUFhQTtnQkFDYmtPLG1CQUFtQmpGO2dCQUNuQmtGLGtCQUFrQixJQUFNeE8sb0JBQW9CO2dCQUM1Q21CLFVBQVVOO2dCQUNWcUIsUUFBUXZDLGdCQUFnQm9CO2dCQUN4QjBOLHdCQUF3QnRCLHlCQUF5QmxKLE1BQU07Ozs7OzswQkFJekQsOERBQUN4RixzREFBYUE7Z0JBQ1ppUSxRQUFRN087Z0JBQ1I4TyxTQUFTLElBQU03Tyx1QkFBdUI7Z0JBQ3RDUCxVQUFVME0sc0JBQXNCMU07Z0JBQ2hDa0IsVUFBVUE7Ozs7OzswQkFJWiw4REFBQy9CLDZEQUFtQkE7Z0JBQ2xCZ1EsUUFBUTNPO2dCQUNSNE8sU0FBUyxJQUFNM08sb0JBQW9CO2dCQUNuQzRPLGNBQWNuUDtnQkFDZG9QLGVBQWV4Szs7Ozs7OzBCQUlqQiw4REFBQzFGLDBEQUFnQkE7Z0JBQ2YrUCxRQUFRek87Z0JBQ1IwTyxTQUFTLElBQU16TywwQkFBMEI7Z0JBQ3pDK0csYUFBYUc7Z0JBQ2IwSCxtQkFBbUIzQjtnQkFDbkI0QixvQkFBb0JsQzs7Ozs7OzBCQUl0Qiw4REFBQ2pPLHlEQUFlQTtnQkFDZDhQLFFBQVF2TztnQkFDUndPLFNBQVMsSUFBTXZPLHlCQUF5QjtnQkFDeENiLFVBQVUwTSxzQkFBc0IxTTtnQkFDaENrQixVQUFVQTs7Ozs7Ozs7Ozs7O0FBSWxCO0dBOXBDd0IzQjs7UUFDTFQsMERBQU9BOzs7S0FERlMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvZGFzaGJvYXJkL0NoYXRBcmVhLnRzeD9jNjZkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZG9jLCBzZXREb2MsIGNvbGxlY3Rpb24sIGdldERvYywgdXBkYXRlRG9jLCBxdWVyeSwgd2hlcmUsIGdldERvY3MgfSBmcm9tICdmaXJlYmFzZS9maXJlc3RvcmUnO1xuaW1wb3J0IHsgcmVmLCB1cGxvYWRCeXRlcyB9IGZyb20gJ2ZpcmViYXNlL3N0b3JhZ2UnO1xuaW1wb3J0IHsgZGIsIHN0b3JhZ2UgfSBmcm9tICdAL2xpYi9maXJlYmFzZSc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgVXBwZXJiYXIgZnJvbSAnLi9VcHBlcmJhcic7XG5pbXBvcnQgQ2hhdEludGVyZmFjZSBmcm9tICcuL0NoYXRJbnRlcmZhY2UnO1xuaW1wb3J0IElucHV0QmFyIGZyb20gJy4vSW5wdXRCYXInO1xuaW1wb3J0IEF0dGFjaG1lbnREaXNwbGF5IGZyb20gJy4vQXR0YWNobWVudERpc3BsYXknO1xuaW1wb3J0IERvd25sb2FkTW9kYWwgZnJvbSAnLi9Eb3dubG9hZE1vZGFsJztcbmltcG9ydCBNb2RlbFNlbGVjdGlvbk1vZGFsIGZyb20gJy4vTW9kZWxTZWxlY3Rpb25Nb2RhbCc7XG5pbXBvcnQgQXR0YWNobWVudHNNb2RhbCBmcm9tICcuL0F0dGFjaG1lbnRzTW9kYWwnO1xuaW1wb3J0IFN0YXRpc3RpY3NNb2RhbCBmcm9tICcuL1N0YXRpc3RpY3NNb2RhbCc7XG5pbXBvcnQgeyBDaGF0TWVzc2FnZSB9IGZyb20gJ0AvbGliL3R5cGVzL2NoYXQnO1xuaW1wb3J0IGFpU2VydmljZSBmcm9tICdAL2xpYi9zZXJ2aWNlcy9haVNlcnZpY2UnO1xuXG5pbnRlcmZhY2UgV2ViU2VhcmNoQW5ub3RhdGlvbiB7XG4gIHR5cGU6IFwidXJsX2NpdGF0aW9uXCI7XG4gIHVybDogc3RyaW5nO1xuICB0aXRsZTogc3RyaW5nO1xuICBjb250ZW50Pzogc3RyaW5nO1xuICBzdGFydF9pbmRleDogbnVtYmVyO1xuICBlbmRfaW5kZXg6IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIE1lc3NhZ2Uge1xuICBpZDogc3RyaW5nO1xuICBjb250ZW50OiBzdHJpbmc7XG4gIHNlbmRlcjogJ3VzZXInIHwgJ2FpJztcbiAgdGltZXN0YW1wOiBzdHJpbmc7XG4gIGlzRmF2b3JpdGU/OiBib29sZWFuO1xuICBhdHRhY2htZW50cz86IGltcG9ydCgnQC9saWIvdHlwZXMvY2hhdCcpLkF0dGFjaG1lbnRNZXRhZGF0YVtdO1xuICBoYXNXZWJTZWFyY2g/OiBib29sZWFuO1xuICB3ZWJTZWFyY2hBbm5vdGF0aW9ucz86IFdlYlNlYXJjaEFubm90YXRpb25bXTtcbn1cblxuaW50ZXJmYWNlIENoYXRBcmVhUHJvcHMge1xuICBjdXJyZW50Q2hhdDogc3RyaW5nIHwgbnVsbDtcbiAgb25DaGF0Q3JlYXRlZD86IChjaGF0SWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgb25VcGRhdGVPcGVuUm91dGVyQmFsYW5jZT86ICgpID0+IHZvaWQ7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENoYXRBcmVhKHsgY3VycmVudENoYXQsIG9uQ2hhdENyZWF0ZWQsIG9uVXBkYXRlT3BlblJvdXRlckJhbGFuY2UgfTogQ2hhdEFyZWFQcm9wcykge1xuICBjb25zdCB7IHVzZXIgfSA9IHVzZUF1dGgoKTtcbiAgY29uc3QgW21lc3NhZ2UsIHNldE1lc3NhZ2VdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbbWVzc2FnZXMsIHNldE1lc3NhZ2VzXSA9IHVzZVN0YXRlPE1lc3NhZ2VbXT4oW10pO1xuICBjb25zdCBbc2VsZWN0ZWRNb2RlbCwgc2V0U2VsZWN0ZWRNb2RlbF0gPSB1c2VTdGF0ZSgnbWV0YS1sbGFtYS9sbGFtYS0zLjEtOGItaW5zdHJ1Y3Q6ZnJlZScpO1xuXG4gIGNvbnN0IFthY3R1YWxDaGF0SWQsIHNldEFjdHVhbENoYXRJZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihjdXJyZW50Q2hhdCk7XG4gIGNvbnN0IFtpc0Rvd25sb2FkTW9kYWxPcGVuLCBzZXRJc0Rvd25sb2FkTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzTW9kZWxNb2RhbE9wZW4sIHNldElzTW9kZWxNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNBdHRhY2htZW50c01vZGFsT3Blbiwgc2V0SXNBdHRhY2htZW50c01vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc1N0YXRpc3RpY3NNb2RhbE9wZW4sIHNldElzU3RhdGlzdGljc01vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc1N0cmVhbWluZywgc2V0SXNTdHJlYW1pbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc3RyZWFtaW5nTWVzc2FnZUlkLCBzZXRTdHJlYW1pbmdNZXNzYWdlSWRdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtjaGF0TmFtZSwgc2V0Q2hhdE5hbWVdID0gdXNlU3RhdGU8c3RyaW5nPignTm92YSBDb252ZXJzYScpO1xuXG5cbiAgY29uc3QgW2lzTG9hZGluZ0NoYXQsIHNldElzTG9hZGluZ0NoYXRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbY3VycmVudFVzZXJuYW1lLCBzZXRDdXJyZW50VXNlcm5hbWVdID0gdXNlU3RhdGU8c3RyaW5nIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuICBjb25zdCBjaGF0SW50ZXJmYWNlUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcblxuICAvLyBDYXJyZWdhciB1c2VybmFtZSBxdWFuZG8gbyB1c3XDoXJpbyBlc3RpdmVyIGRpc3BvbsOtdmVsXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgbG9hZFVzZXJuYW1lID0gYXN5bmMgKCkgPT4ge1xuICAgICAgaWYgKHVzZXI/LmVtYWlsKSB7XG4gICAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICAgIHNldEN1cnJlbnRVc2VybmFtZSh1c2VybmFtZSk7XG4gICAgICB9XG4gICAgfTtcbiAgICBsb2FkVXNlcm5hbWUoKTtcbiAgfSwgW3VzZXI/LmVtYWlsXSk7XG5cbiAgLy8gRnVuw6fDo28gdXRpbGl0w6FyaWEgcGFyYSBidXNjYXIgdXNlcm5hbWUgY29ycmV0b1xuICBjb25zdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUgPSBhc3luYyAoKTogUHJvbWlzZTxzdHJpbmc+ID0+IHtcbiAgICBpZiAoIXVzZXI/LmVtYWlsKSByZXR1cm4gJ3Vua25vd24nO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzdWFyaW9zUmVmID0gY29sbGVjdGlvbihkYiwgJ3VzdWFyaW9zJyk7XG4gICAgICBjb25zdCBxID0gcXVlcnkodXN1YXJpb3NSZWYsIHdoZXJlKCdlbWFpbCcsICc9PScsIHVzZXIuZW1haWwpKTtcbiAgICAgIGNvbnN0IHF1ZXJ5U25hcHNob3QgPSBhd2FpdCBnZXREb2NzKHEpO1xuXG4gICAgICBpZiAoIXF1ZXJ5U25hcHNob3QuZW1wdHkpIHtcbiAgICAgICAgY29uc3QgdXNlckRvYyA9IHF1ZXJ5U25hcHNob3QuZG9jc1swXTtcbiAgICAgICAgY29uc3QgdXNlckRhdGEgPSB1c2VyRG9jLmRhdGEoKTtcbiAgICAgICAgcmV0dXJuIHVzZXJEYXRhLnVzZXJuYW1lIHx8IHVzZXIuZW1haWwuc3BsaXQoJ0AnKVswXTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHVzZXIuZW1haWwuc3BsaXQoJ0AnKVswXTsgLy8gZmFsbGJhY2tcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBidXNjYXIgdXNlcm5hbWU6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIHVzZXIuZW1haWwuc3BsaXQoJ0AnKVswXTsgLy8gZmFsbGJhY2tcbiAgICB9XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBzYWx2YXIgbyDDumx0aW1vIG1vZGVsbyB1c2FkbyBwYXJhIHVtIGNoYXQgZXNwZWPDrWZpY29cbiAgY29uc3Qgc2F2ZUxhc3RVc2VkTW9kZWxGb3JDaGF0ID0gYXN5bmMgKG1vZGVsSWQ6IHN0cmluZywgY2hhdElkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXVzZXIgfHwgIWNoYXRJZCkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICBjb25zdCBjaGF0UmVmID0gZG9jKGRiLCAndXN1YXJpb3MnLCB1c2VybmFtZSwgJ2NvbnZlcnNhcycsIGNoYXRJZCk7XG5cbiAgICAgIC8vIEF0dWFsaXphciBvIGxhc3RVc2VkTW9kZWwgbm8gZG9jdW1lbnRvIGRvIGNoYXRcbiAgICAgIGF3YWl0IHVwZGF0ZURvYyhjaGF0UmVmLCB7XG4gICAgICAgIGxhc3RVc2VkTW9kZWw6IG1vZGVsSWQsXG4gICAgICAgIGxhc3RNb2RlbFVwZGF0ZUF0OiBEYXRlLm5vdygpXG4gICAgICB9KTtcblxuICAgICAgY29uc29sZS5sb2coJ0xhc3QgdXNlZCBtb2RlbCBzYXZlZCBmb3IgY2hhdDonLCB7IGNoYXRJZCwgbW9kZWxJZCB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2F2aW5nIGxhc3QgdXNlZCBtb2RlbCBmb3IgY2hhdDonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgY2FycmVnYXIgbyDDumx0aW1vIG1vZGVsbyB1c2FkbyBkZSB1bSBjaGF0IGVzcGVjw61maWNvXG4gIGNvbnN0IGxvYWRMYXN0VXNlZE1vZGVsRm9yQ2hhdCA9IGFzeW5jIChjaGF0SWQ6IHN0cmluZykgPT4ge1xuICAgIGlmICghdXNlciB8fCAhY2hhdElkKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcbiAgICAgIGNvbnN0IGNoYXRSZWYgPSBkb2MoZGIsICd1c3VhcmlvcycsIHVzZXJuYW1lLCAnY29udmVyc2FzJywgY2hhdElkKTtcbiAgICAgIGNvbnN0IGNoYXREb2MgPSBhd2FpdCBnZXREb2MoY2hhdFJlZik7XG5cbiAgICAgIGlmIChjaGF0RG9jLmV4aXN0cygpKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBjaGF0RG9jLmRhdGEoKTtcbiAgICAgICAgaWYgKGRhdGEubGFzdFVzZWRNb2RlbCkge1xuICAgICAgICAgIC8vIFZlcmlmaWNhciBzZSBvIG1vZGVsbyBzYWx2byBhaW5kYSDDqSB2w6FsaWRvXG4gICAgICAgICAgY29uc3QgaXNWYWxpZCA9IGF3YWl0IGlzVmFsaWRNb2RlbChkYXRhLmxhc3RVc2VkTW9kZWwpO1xuICAgICAgICAgIGlmIChpc1ZhbGlkKSB7XG4gICAgICAgICAgICBzZXRTZWxlY3RlZE1vZGVsKGRhdGEubGFzdFVzZWRNb2RlbCk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygnTG9hZGVkIGxhc3QgdXNlZCBtb2RlbCBmb3IgY2hhdDonLCB7IGNoYXRJZCwgbW9kZWw6IGRhdGEubGFzdFVzZWRNb2RlbCB9KTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ0ludmFsaWQgbW9kZWwgZGV0ZWN0ZWQgaW4gY2hhdCwgY2xlYXJpbmcgYW5kIHVzaW5nIGRlZmF1bHQ6JywgZGF0YS5sYXN0VXNlZE1vZGVsKTtcbiAgICAgICAgICAgIC8vIExpbXBhciBvIG1vZGVsbyBpbnbDoWxpZG8gZG8gY2hhdFxuICAgICAgICAgICAgYXdhaXQgdXBkYXRlRG9jKGNoYXRSZWYsIHtcbiAgICAgICAgICAgICAgbGFzdFVzZWRNb2RlbDogJydcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgLy8gQ2FycmVnYXIgbyBtb2RlbG8gcGFkcsOjbyBkbyBlbmRwb2ludCBhdGl2b1xuICAgICAgICAgICAgbG9hZERlZmF1bHRNb2RlbEZyb21BY3RpdmVFbmRwb2ludCgpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBTZSBvIGNoYXQgbsOjbyB0ZW0gbW9kZWxvIHNhbHZvLCBjYXJyZWdhciBvIG1vZGVsbyBwYWRyw6NvIGRvIGVuZHBvaW50IGF0aXZvXG4gICAgICAgICAgbG9hZERlZmF1bHRNb2RlbEZyb21BY3RpdmVFbmRwb2ludCgpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdObyBzYXZlZCBtb2RlbCBmb3IgY2hhdCwgbG9hZGluZyBkZWZhdWx0IGZyb20gYWN0aXZlIGVuZHBvaW50OicsIGNoYXRJZCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBsYXN0IHVzZWQgbW9kZWwgZm9yIGNoYXQ6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGNhcnJlZ2FyIG8gbW9kZWxvIHBhZHLDo28gZG8gZW5kcG9pbnQgYXRpdm9cbiAgY29uc3QgbG9hZERlZmF1bHRNb2RlbEZyb21BY3RpdmVFbmRwb2ludCA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXVzZXIpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB1c2VybmFtZSA9IGF3YWl0IGdldFVzZXJuYW1lRnJvbUZpcmVzdG9yZSgpO1xuICAgICAgY29uc3QgdXNlclJlZiA9IGRvYyhkYiwgJ3VzdWFyaW9zJywgdXNlcm5hbWUsICdjb25maWd1cmFjb2VzJywgJ3NldHRpbmdzJyk7XG4gICAgICBjb25zdCB1c2VyRG9jID0gYXdhaXQgZ2V0RG9jKHVzZXJSZWYpO1xuXG4gICAgICBpZiAodXNlckRvYy5leGlzdHMoKSkge1xuICAgICAgICBjb25zdCBkYXRhID0gdXNlckRvYy5kYXRhKCk7XG5cbiAgICAgICAgLy8gUHJpbWVpcm8sIHRlbnRhciBjYXJyZWdhciBvIMO6bHRpbW8gbW9kZWxvIHVzYWRvIGdsb2JhbG1lbnRlXG4gICAgICAgIGlmIChkYXRhLmxhc3RVc2VkTW9kZWwpIHtcbiAgICAgICAgICBzZXRTZWxlY3RlZE1vZGVsKGRhdGEubGFzdFVzZWRNb2RlbCk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0xvYWRlZCBnbG9iYWwgbGFzdCB1c2VkIG1vZGVsOicsIGRhdGEubGFzdFVzZWRNb2RlbCk7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gU2UgbsOjbyBow6Egw7psdGltbyBtb2RlbG8gdXNhZG8sIGJ1c2NhciBvIG1vZGVsbyBwYWRyw6NvIGRvIGVuZHBvaW50IGF0aXZvXG4gICAgICAgIGlmIChkYXRhLmVuZHBvaW50cykge1xuICAgICAgICAgIGNvbnN0IGFjdGl2ZUVuZHBvaW50ID0gT2JqZWN0LnZhbHVlcyhkYXRhLmVuZHBvaW50cykuZmluZCgoZW5kcG9pbnQ6IGFueSkgPT4gZW5kcG9pbnQuYXRpdm8pO1xuICAgICAgICAgIGlmIChhY3RpdmVFbmRwb2ludCAmJiAoYWN0aXZlRW5kcG9pbnQgYXMgYW55KS5tb2RlbG9QYWRyYW8pIHtcbiAgICAgICAgICAgIHNldFNlbGVjdGVkTW9kZWwoKGFjdGl2ZUVuZHBvaW50IGFzIGFueSkubW9kZWxvUGFkcmFvKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdMb2FkZWQgZGVmYXVsdCBtb2RlbCBmcm9tIGFjdGl2ZSBlbmRwb2ludDonLCAoYWN0aXZlRW5kcG9pbnQgYXMgYW55KS5tb2RlbG9QYWRyYW8pO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBGYWxsYmFjayBwYXJhIG8gbW9kZWxvIHBhZHLDo28gaGFyZGNvZGVkXG4gICAgICBzZXRTZWxlY3RlZE1vZGVsKCdtZXRhLWxsYW1hL2xsYW1hLTMuMS04Yi1pbnN0cnVjdDpmcmVlJyk7XG4gICAgICBjb25zb2xlLmxvZygnVXNpbmcgZmFsbGJhY2sgZGVmYXVsdCBtb2RlbCcpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGRlZmF1bHQgbW9kZWwgZnJvbSBhY3RpdmUgZW5kcG9pbnQ6JywgZXJyb3IpO1xuICAgICAgLy8gRmFsbGJhY2sgcGFyYSBvIG1vZGVsbyBwYWRyw6NvIGhhcmRjb2RlZCBlbSBjYXNvIGRlIGVycm9cbiAgICAgIHNldFNlbGVjdGVkTW9kZWwoJ21ldGEtbGxhbWEvbGxhbWEtMy4xLThiLWluc3RydWN0OmZyZWUnKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSB2YWxpZGFyIHNlIHVtIG1vZGVsbyBhaW5kYSBleGlzdGUvw6kgdsOhbGlkb1xuICBjb25zdCBpc1ZhbGlkTW9kZWwgPSBhc3luYyAobW9kZWxJZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gICAgLy8gTGlzdGEgZGUgbW9kZWxvcyBjb25oZWNpZG9zIGNvbW8gaW52w6FsaWRvcyBvdSByZW1vdmlkb3NcbiAgICBjb25zdCBpbnZhbGlkTW9kZWxzID0gW1xuICAgICAgJ3F3ZW4vcXdlbjMtMjM1Yi1hMjJiLXRoaW5raW5nLTI1MDcnLFxuICAgICAgLy8gQWRpY2lvbmUgb3V0cm9zIG1vZGVsb3MgaW52w6FsaWRvcyBhcXVpIGNvbmZvcm1lIG5lY2Vzc8OhcmlvXG4gICAgXTtcblxuICAgIHJldHVybiAhaW52YWxpZE1vZGVscy5pbmNsdWRlcyhtb2RlbElkKTtcbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGxpbXBhciBtb2RlbG9zIGludsOhbGlkb3MgZGUgdG9kb3Mgb3MgY2hhdHMgZG8gdXN1w6FyaW9cbiAgY29uc3QgY2xlYW51cEludmFsaWRNb2RlbHNGcm9tQWxsQ2hhdHMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF1c2VyKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcbiAgICAgIGNvbnN0IGNoYXRzUmVmID0gY29sbGVjdGlvbihkYiwgJ3VzdWFyaW9zJywgdXNlcm5hbWUsICdjb252ZXJzYXMnKTtcbiAgICAgIGNvbnN0IGNoYXRzU25hcHNob3QgPSBhd2FpdCBnZXREb2NzKGNoYXRzUmVmKTtcblxuICAgICAgY29uc3QgdXBkYXRlUHJvbWlzZXM6IFByb21pc2U8dm9pZD5bXSA9IFtdO1xuXG4gICAgICBmb3IgKGNvbnN0IGNoYXREb2Mgb2YgY2hhdHNTbmFwc2hvdC5kb2NzKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBjaGF0RG9jLmRhdGEoKTtcbiAgICAgICAgaWYgKGRhdGEubGFzdFVzZWRNb2RlbCkge1xuICAgICAgICAgIGNvbnN0IGlzVmFsaWQgPSBhd2FpdCBpc1ZhbGlkTW9kZWwoZGF0YS5sYXN0VXNlZE1vZGVsKTtcbiAgICAgICAgICBpZiAoIWlzVmFsaWQpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdDbGVhbmluZyBpbnZhbGlkIG1vZGVsIGZyb20gY2hhdDonLCBjaGF0RG9jLmlkLCBkYXRhLmxhc3RVc2VkTW9kZWwpO1xuICAgICAgICAgICAgdXBkYXRlUHJvbWlzZXMucHVzaChcbiAgICAgICAgICAgICAgdXBkYXRlRG9jKGRvYyhkYiwgJ3VzdWFyaW9zJywgdXNlcm5hbWUsICdjb252ZXJzYXMnLCBjaGF0RG9jLmlkKSwge1xuICAgICAgICAgICAgICAgIGxhc3RVc2VkTW9kZWw6ICcnXG4gICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICApO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpZiAodXBkYXRlUHJvbWlzZXMubGVuZ3RoID4gMCkge1xuICAgICAgICBhd2FpdCBQcm9taXNlLmFsbCh1cGRhdGVQcm9taXNlcyk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdDbGVhbmVkIGludmFsaWQgbW9kZWxzIGZyb20nLCB1cGRhdGVQcm9taXNlcy5sZW5ndGgsICdjaGF0cycpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjbGVhbmluZyBpbnZhbGlkIG1vZGVscyBmcm9tIGNoYXRzOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBjYXJyZWdhciBvIMO6bHRpbW8gbW9kZWxvIHVzYWRvIGdsb2JhbG1lbnRlIChmYWxsYmFjaylcbiAgY29uc3QgbG9hZEdsb2JhbExhc3RVc2VkTW9kZWwgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF1c2VyKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcbiAgICAgIGNvbnN0IHVzZXJSZWYgPSBkb2MoZGIsICd1c3VhcmlvcycsIHVzZXJuYW1lLCAnY29uZmlndXJhY29lcycsICdzZXR0aW5ncycpO1xuICAgICAgY29uc3QgdXNlckRvYyA9IGF3YWl0IGdldERvYyh1c2VyUmVmKTtcblxuICAgICAgaWYgKHVzZXJEb2MuZXhpc3RzKCkpIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IHVzZXJEb2MuZGF0YSgpO1xuICAgICAgICBpZiAoZGF0YS5sYXN0VXNlZE1vZGVsKSB7XG4gICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIG8gbW9kZWxvIHNhbHZvIGFpbmRhIMOpIHbDoWxpZG9cbiAgICAgICAgICBjb25zdCBpc1ZhbGlkID0gYXdhaXQgaXNWYWxpZE1vZGVsKGRhdGEubGFzdFVzZWRNb2RlbCk7XG4gICAgICAgICAgaWYgKGlzVmFsaWQpIHtcbiAgICAgICAgICAgIHNldFNlbGVjdGVkTW9kZWwoZGF0YS5sYXN0VXNlZE1vZGVsKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdMb2FkZWQgZ2xvYmFsIGxhc3QgdXNlZCBtb2RlbDonLCBkYXRhLmxhc3RVc2VkTW9kZWwpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygnSW52YWxpZCBtb2RlbCBkZXRlY3RlZCwgY2xlYXJpbmcgYW5kIHVzaW5nIGRlZmF1bHQ6JywgZGF0YS5sYXN0VXNlZE1vZGVsKTtcbiAgICAgICAgICAgIC8vIExpbXBhciBvIG1vZGVsbyBpbnbDoWxpZG8gZGFzIGNvbmZpZ3VyYcOnw7Vlc1xuICAgICAgICAgICAgYXdhaXQgdXBkYXRlRG9jKHVzZXJSZWYsIHtcbiAgICAgICAgICAgICAgbGFzdFVzZWRNb2RlbDogbnVsbFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAvLyBDYXJyZWdhciBvIG1vZGVsbyBwYWRyw6NvIGRvIGVuZHBvaW50IGF0aXZvXG4gICAgICAgICAgICBsb2FkRGVmYXVsdE1vZGVsRnJvbUFjdGl2ZUVuZHBvaW50KCk7XG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIFNlIG7Do28gaMOhIMO6bHRpbW8gbW9kZWxvIHVzYWRvLCBjYXJyZWdhciBvIG1vZGVsbyBwYWRyw6NvIGRvIGVuZHBvaW50IGF0aXZvXG4gICAgICAgICAgbG9hZERlZmF1bHRNb2RlbEZyb21BY3RpdmVFbmRwb2ludCgpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBTZSBuw6NvIGjDoSBjb25maWd1cmHDp8O1ZXMsIGNhcnJlZ2FyIG8gbW9kZWxvIHBhZHLDo28gZG8gZW5kcG9pbnQgYXRpdm9cbiAgICAgICAgbG9hZERlZmF1bHRNb2RlbEZyb21BY3RpdmVFbmRwb2ludCgpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGdsb2JhbCBsYXN0IHVzZWQgbW9kZWw6JywgZXJyb3IpO1xuICAgICAgLy8gRmFsbGJhY2sgcGFyYSBjYXJyZWdhciBvIG1vZGVsbyBwYWRyw6NvIGRvIGVuZHBvaW50IGF0aXZvXG4gICAgICBsb2FkRGVmYXVsdE1vZGVsRnJvbUFjdGl2ZUVuZHBvaW50KCk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHdyYXBwZXIgcGFyYSBzZXRTZWxlY3RlZE1vZGVsIHF1ZSB0YW1iw6ltIHNhbHZhIG5vIEZpcmVzdG9yZVxuICBjb25zdCBoYW5kbGVNb2RlbENoYW5nZSA9IChtb2RlbElkOiBzdHJpbmcpID0+IHtcbiAgICBzZXRTZWxlY3RlZE1vZGVsKG1vZGVsSWQpO1xuXG4gICAgLy8gU2FsdmFyIG5vIGNoYXQgZXNwZWPDrWZpY28gc2UgaG91dmVyIHVtIGNoYXQgYXRpdm9cbiAgICBpZiAoYWN0dWFsQ2hhdElkKSB7XG4gICAgICBzYXZlTGFzdFVzZWRNb2RlbEZvckNoYXQobW9kZWxJZCwgYWN0dWFsQ2hhdElkKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBjcmlhciB1bSBjaGF0IGF1dG9tYXRpY2FtZW50ZVxuICBjb25zdCBjcmVhdGVBdXRvQ2hhdCA9IGFzeW5jIChmaXJzdE1lc3NhZ2U6IHN0cmluZyk6IFByb21pc2U8c3RyaW5nIHwgbnVsbD4gPT4ge1xuICAgIGlmICghdXNlcj8uZW1haWwpIHJldHVybiBudWxsO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIEJ1c2NhciB1c2VybmFtZSBkbyB1c3XDoXJpb1xuICAgICAgY29uc3QgdXN1YXJpb3NSZWYgPSBjb2xsZWN0aW9uKGRiLCAndXN1YXJpb3MnKTtcbiAgICAgIGNvbnN0IHEgPSBxdWVyeSh1c3Vhcmlvc1JlZiwgd2hlcmUoJ2VtYWlsJywgJz09JywgdXNlci5lbWFpbCkpO1xuICAgICAgY29uc3QgcXVlcnlTbmFwc2hvdCA9IGF3YWl0IGdldERvY3MocSk7XG5cbiAgICAgIGlmIChxdWVyeVNuYXBzaG90LmVtcHR5KSByZXR1cm4gbnVsbDtcblxuICAgICAgY29uc3QgdXNlckRvYyA9IHF1ZXJ5U25hcHNob3QuZG9jc1swXTtcbiAgICAgIGNvbnN0IHVzZXJEYXRhID0gdXNlckRvYy5kYXRhKCk7XG4gICAgICBjb25zdCB1c2VybmFtZSA9IHVzZXJEYXRhLnVzZXJuYW1lO1xuXG4gICAgICAvLyBHZXJhciBJRCDDum5pY28gcGFyYSBvIGNoYXRcbiAgICAgIGNvbnN0IHRpbWVzdGFtcCA9IERhdGUubm93KCk7XG4gICAgICBjb25zdCByYW5kb20gPSBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwgOCk7XG4gICAgICBjb25zdCBjaGF0SWQgPSBgY2hhdF8ke3RpbWVzdGFtcH1fJHtyYW5kb219YDtcbiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKTtcblxuICAgICAgLy8gR2VyYXIgbm9tZSBkbyBjaGF0IGJhc2VhZG8gbmEgcHJpbWVpcmEgbWVuc2FnZW0gKHByaW1laXJhcyAzLTQgcGFsYXZyYXMpXG4gICAgICBsZXQgZmluYWxDaGF0TmFtZSA9ICdOb3ZhIENvbnZlcnNhJztcbiAgICAgIGlmIChmaXJzdE1lc3NhZ2UudHJpbSgpLmxlbmd0aCA+IDApIHtcbiAgICAgICAgY29uc3Qgd29yZHMgPSBmaXJzdE1lc3NhZ2UudHJpbSgpLnNwbGl0KCcgJyk7XG4gICAgICAgIGNvbnN0IGNoYXROYW1lID0gd29yZHMuc2xpY2UoMCwgTWF0aC5taW4oNCwgd29yZHMubGVuZ3RoKSkuam9pbignICcpO1xuICAgICAgICBmaW5hbENoYXROYW1lID0gY2hhdE5hbWUubGVuZ3RoID4gMzAgPyBjaGF0TmFtZS5zdWJzdHJpbmcoMCwgMzApICsgJy4uLicgOiBjaGF0TmFtZTtcbiAgICAgIH1cblxuICAgICAgLy8gRGFkb3MgcGFyYSBvIEZpcmVzdG9yZVxuICAgICAgY29uc3QgZmlyZXN0b3JlRGF0YSA9IHtcbiAgICAgICAgY29udGV4dDogJycsXG4gICAgICAgIGNyZWF0ZWRBdDogbm93LFxuICAgICAgICBmb2xkZXJJZDogbnVsbCxcbiAgICAgICAgZnJlcXVlbmN5UGVuYWx0eTogMS4wLFxuICAgICAgICBpc0ZpeGVkOiBmYWxzZSxcbiAgICAgICAgbGFzdFVwZGF0ZWRBdDogbm93LFxuICAgICAgICBsYXN0VXNlZE1vZGVsOiBzZWxlY3RlZE1vZGVsLFxuICAgICAgICBsYXRleEluc3RydWN0aW9uczogZmFsc2UsXG4gICAgICAgIG1heFRva2VuczogMjA0OCxcbiAgICAgICAgbmFtZTogZmluYWxDaGF0TmFtZSxcbiAgICAgICAgcGFzc3dvcmQ6ICcnLFxuICAgICAgICByZXBldGl0aW9uUGVuYWx0eTogMS4wLFxuICAgICAgICBzZXNzaW9uVGltZToge1xuICAgICAgICAgIGxhc3RTZXNzaW9uU3RhcnQ6IG5vdyxcbiAgICAgICAgICBsYXN0VXBkYXRlZDogbm93LFxuICAgICAgICAgIHRvdGFsVGltZTogMFxuICAgICAgICB9LFxuICAgICAgICBzeXN0ZW1Qcm9tcHQ6ICcnLFxuICAgICAgICB0ZW1wZXJhdHVyZTogMS4wLFxuICAgICAgICB1bHRpbWFNZW5zYWdlbTogZmlyc3RNZXNzYWdlIHx8ICdBbmV4byBlbnZpYWRvJyxcbiAgICAgICAgdWx0aW1hTWVuc2FnZW1FbTogbm93LFxuICAgICAgICB1cGRhdGVkQXQ6IG5vd1xuICAgICAgfTtcblxuICAgICAgLy8gQ3JpYXIgZG9jdW1lbnRvIG5vIEZpcmVzdG9yZVxuICAgICAgYXdhaXQgc2V0RG9jKGRvYyhkYiwgJ3VzdWFyaW9zJywgdXNlcm5hbWUsICdjb252ZXJzYXMnLCBjaGF0SWQpLCBmaXJlc3RvcmVEYXRhKTtcblxuICAgICAgLy8gQ3JpYXIgYXJxdWl2byBjaGF0Lmpzb24gbm8gU3RvcmFnZVxuICAgICAgY29uc3QgY2hhdEpzb25EYXRhID0ge1xuICAgICAgICBpZDogY2hhdElkLFxuICAgICAgICBuYW1lOiBmaW5hbENoYXROYW1lLFxuICAgICAgICBtZXNzYWdlczogW10sXG4gICAgICAgIGNyZWF0ZWRBdDogbm93LFxuICAgICAgICBsYXN0VXBkYXRlZDogbm93XG4gICAgICB9O1xuXG4gICAgICBjb25zdCBjaGF0SnNvbkJsb2IgPSBuZXcgQmxvYihbSlNPTi5zdHJpbmdpZnkoY2hhdEpzb25EYXRhLCBudWxsLCAyKV0sIHtcbiAgICAgICAgdHlwZTogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICB9KTtcblxuICAgICAgY29uc3Qgc3RvcmFnZVJlZiA9IHJlZihzdG9yYWdlLCBgdXN1YXJpb3MvJHt1c2VybmFtZX0vY29udmVyc2FzLyR7Y2hhdElkfS9jaGF0Lmpzb25gKTtcbiAgICAgIGF3YWl0IHVwbG9hZEJ5dGVzKHN0b3JhZ2VSZWYsIGNoYXRKc29uQmxvYik7XG5cbiAgICAgIGNvbnNvbGUubG9nKCdDaGF0IGNyaWFkbyBhdXRvbWF0aWNhbWVudGU6JywgY2hhdElkKTtcbiAgICAgIC8vIERlZmluaXIgbyBub21lIGRvIGNoYXQgaW1lZGlhdGFtZW50ZSBhcMOzcyBjcmlhw6fDo29cbiAgICAgIHNldENoYXROYW1lKGZpbmFsQ2hhdE5hbWUpO1xuICAgICAgcmV0dXJuIGNoYXRJZDtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGNyaWFyIGNoYXQgYXV0b21hdGljYW1lbnRlOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVTZW5kTWVzc2FnZSA9IGFzeW5jIChhdHRhY2htZW50cz86IGltcG9ydCgnQC9saWIvdHlwZXMvY2hhdCcpLkF0dGFjaG1lbnRNZXRhZGF0YVtdLCB3ZWJTZWFyY2hFbmFibGVkPzogYm9vbGVhbikgPT4ge1xuXG4gICAgLy8gT2J0ZXIgYW5leG9zIGhpc3TDs3JpY29zIGF0aXZvc1xuICAgIGNvbnN0IGhpc3RvcmljYWxBdHRhY2htZW50cyA9IGdldEFsbENoYXRBdHRhY2htZW50cygpLmZpbHRlcihhdHQgPT4gYXR0LmlzQWN0aXZlICE9PSBmYWxzZSk7XG5cbiAgICAvLyBDb21iaW5hciBhbmV4b3Mgbm92b3MgY29tIGFuZXhvcyBoaXN0w7NyaWNvcyBhdGl2b3NcbiAgICBjb25zdCBhbGxBdHRhY2htZW50c1RvU2VuZCA9IFtcbiAgICAgIC4uLihhdHRhY2htZW50cyB8fCBbXSksIC8vIEFuZXhvcyBub3ZvcyAoc2VtcHJlIGluY2x1w61kb3MpXG4gICAgICAuLi5oaXN0b3JpY2FsQXR0YWNobWVudHMgLy8gQW5leG9zIGhpc3TDs3JpY29zIGF0aXZvc1xuICAgIF07XG5cbiAgICAvLyBSZW1vdmVyIGR1cGxpY2F0YXMgYmFzZWFkbyBubyBJRFxuICAgIGNvbnN0IHVuaXF1ZUF0dGFjaG1lbnRzID0gYWxsQXR0YWNobWVudHNUb1NlbmQuZmlsdGVyKChhdHRhY2htZW50LCBpbmRleCwgc2VsZikgPT5cbiAgICAgIGluZGV4ID09PSBzZWxmLmZpbmRJbmRleChhID0+IGEuaWQgPT09IGF0dGFjaG1lbnQuaWQpXG4gICAgKTtcblxuICAgIGNvbnNvbGUubG9nKCfwn5qAIFJFU1VMVEFETyBGSU5BTCAtIEFORVhPUyBRVUUgU0VSw4NPIEVOVklBRE9TIFBBUkEgQSBJQTonLCB1bmlxdWVBdHRhY2htZW50cy5sZW5ndGgpO1xuICAgIHVuaXF1ZUF0dGFjaG1lbnRzLmZvckVhY2goYXR0ID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKGDwn5qAIEVudmlhbmRvIHBhcmEgSUE6ICR7YXR0LmZpbGVuYW1lfSAoSUQ6ICR7YXR0LmlkfSwgaXNBY3RpdmU6ICR7YXR0LmlzQWN0aXZlfSlgKTtcbiAgICB9KTtcblxuICAgIGlmICh1bmlxdWVBdHRhY2htZW50cy5sZW5ndGggPT09IDApIHtcbiAgICAgIGNvbnNvbGUubG9nKCfinYwgTkVOSFVNIEFORVhPIFNFUsOBIEVOVklBRE8gUEFSQSBBIElBJyk7XG4gICAgfVxuXG4gICAgLy8gRGVidWc6IHZlcmlmaWNhciBtZW5zYWdlbnMgYXR1YWlzIG5vIGVzdGFkb1xuICAgIGNvbnNvbGUubG9nKCc9PT0gREVCVUc6IE1FTlNBR0VOUyBBVFVBSVMgTk8gRVNUQURPID09PScpO1xuICAgIGNvbnNvbGUubG9nKCdUb3RhbCBkZSBtZW5zYWdlbnMgbm8gZXN0YWRvOicsIG1lc3NhZ2VzLmxlbmd0aCk7XG4gICAgY29uc3QgbWVzc2FnZXNXaXRoQXR0YWNobWVudHMgPSBtZXNzYWdlcy5maWx0ZXIobXNnID0+IG1zZy5hdHRhY2htZW50cyAmJiBtc2cuYXR0YWNobWVudHMubGVuZ3RoID4gMCk7XG4gICAgY29uc29sZS5sb2coJ01lbnNhZ2VucyBjb20gYW5leG9zIG5vIGVzdGFkbzonLCBtZXNzYWdlc1dpdGhBdHRhY2htZW50cy5sZW5ndGgpO1xuICAgIG1lc3NhZ2VzV2l0aEF0dGFjaG1lbnRzLmZvckVhY2goKG1zZywgaW5kZXgpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKGBNZW5zYWdlbSAke2luZGV4ICsgMX0gY29tIGFuZXhvczpgLCB7XG4gICAgICAgIGlkOiBtc2cuaWQsXG4gICAgICAgIHNlbmRlcjogbXNnLnNlbmRlcixcbiAgICAgICAgYXR0YWNobWVudHNDb3VudDogKG1zZy5hdHRhY2htZW50cyAmJiBtc2cuYXR0YWNobWVudHMubGVuZ3RoKSB8fCAwLFxuICAgICAgICBhdHRhY2htZW50czogbXNnLmF0dGFjaG1lbnRzXG4gICAgICB9KTtcbiAgICB9KTtcblxuICAgIGlmICgoIW1lc3NhZ2UudHJpbSgpICYmICghYXR0YWNobWVudHMgfHwgYXR0YWNobWVudHMubGVuZ3RoID09PSAwKSkgfHwgaXNMb2FkaW5nIHx8IGlzU3RyZWFtaW5nKSB7XG4gICAgICBjb25zb2xlLmxvZygnPT09IERFQlVHOiBDT05EScOHw4NPIERFIFJFVE9STk8gPT09Jyk7XG4gICAgICBjb25zb2xlLmxvZygnTWVuc2FnZW0gdmF6aWE6JywgIW1lc3NhZ2UudHJpbSgpKTtcbiAgICAgIGNvbnNvbGUubG9nKCdTZW0gYW5leG9zOicsICFhdHRhY2htZW50cyB8fCBhdHRhY2htZW50cy5sZW5ndGggPT09IDApO1xuICAgICAgY29uc29sZS5sb2coJ0xvYWRpbmc6JywgaXNMb2FkaW5nKTtcbiAgICAgIGNvbnNvbGUubG9nKCdTdHJlYW1pbmc6JywgaXNTdHJlYW1pbmcpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBpZiAoIXVzZXI/LmVtYWlsKSByZXR1cm47XG5cbiAgICBjb25zdCB1c2VyTWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgIGlkOiBhaVNlcnZpY2UuZ2VuZXJhdGVNZXNzYWdlSWQoKSxcbiAgICAgIGNvbnRlbnQ6IG1lc3NhZ2UudHJpbSgpLFxuICAgICAgc2VuZGVyOiAndXNlcicsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIGF0dGFjaG1lbnRzOiBhdHRhY2htZW50cyB8fCBbXSwgLy8gU2FsdmFyIGFwZW5hcyBhbmV4b3Mgbm92b3MgbmEgbWVuc2FnZW1cbiAgICB9O1xuXG4gICAgLy8gU2UgbsOjbyBow6EgY2hhdCBhdHVhbCwgY3JpYXIgdW0gYXV0b21hdGljYW1lbnRlXG4gICAgbGV0IGNoYXRJZFRvVXNlID0gYWN0dWFsQ2hhdElkO1xuICAgIGlmICghY2hhdElkVG9Vc2UpIHtcbiAgICAgIGNvbnN0IG1lc3NhZ2VGb3JDaGF0ID0gbWVzc2FnZS50cmltKCkgfHwgKGF0dGFjaG1lbnRzICYmIGF0dGFjaG1lbnRzLmxlbmd0aCA+IDAgPyAnQW5leG8gZW52aWFkbycgOiAnTm92YSBjb252ZXJzYScpO1xuICAgICAgY2hhdElkVG9Vc2UgPSBhd2FpdCBjcmVhdGVBdXRvQ2hhdChtZXNzYWdlRm9yQ2hhdCk7XG4gICAgICBpZiAoY2hhdElkVG9Vc2UpIHtcbiAgICAgICAgc2V0QWN0dWFsQ2hhdElkKGNoYXRJZFRvVXNlKTtcbiAgICAgICAgLy8gTyBub21lIGrDoSBmb2kgZGVmaW5pZG8gbmEgZnVuw6fDo28gY3JlYXRlQXV0b0NoYXQsIG1hcyB2YW1vcyBnYXJhbnRpciBjYXJyZWdhbmRvIHRhbWLDqW1cbiAgICAgICAgbG9hZENoYXROYW1lKGNoYXRJZFRvVXNlKTtcbiAgICAgICAgb25DaGF0Q3JlYXRlZD8uKGNoYXRJZFRvVXNlKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoIWNoYXRJZFRvVXNlKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdOw6NvIGZvaSBwb3Nzw612ZWwgY3JpYXIgb3Ugb2J0ZXIgY2hhdCBJRCcpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIEFkaWNpb25hciBtZW5zYWdlbSBkbyB1c3XDoXJpb1xuICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIHVzZXJNZXNzYWdlXSk7XG4gICAgY29uc3QgY3VycmVudE1lc3NhZ2UgPSBtZXNzYWdlLnRyaW0oKSB8fCBcIlwiOyAvLyBQZXJtaXRpciBtZW5zYWdlbSB2YXppYSBzZSBob3V2ZXIgYW5leG9zXG4gICAgc2V0TWVzc2FnZSgnJyk7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHNldElzU3RyZWFtaW5nKHRydWUpO1xuXG4gICAgLy8gUHJlcGFyYXIgSUQgcGFyYSBhIG1lbnNhZ2VtIGRhIElBIHF1ZSBzZXLDoSBjcmlhZGEgZHVyYW50ZSBvIHN0cmVhbWluZ1xuICAgIGNvbnN0IGFpTWVzc2FnZUlkID0gYWlTZXJ2aWNlLmdlbmVyYXRlTWVzc2FnZUlkKCk7XG4gICAgc2V0U3RyZWFtaW5nTWVzc2FnZUlkKGFpTWVzc2FnZUlkKTtcblxuICAgIC8vIEJ1c2NhciB1c2VybmFtZSBjb3JyZXRvIGRvIHVzdcOhcmlvXG4gICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcblxuICAgIC8vIEVudmlhciBwYXJhIGEgSUEgKGluY2x1aW5kbyBhbmV4b3MgaGlzdMOzcmljb3MgYXRpdm9zICsgYW5leG9zIG5vdm9zKVxuICAgIGF3YWl0IGFpU2VydmljZS5zZW5kTWVzc2FnZVNhZmUoXG4gICAgICB7XG4gICAgICAgIHVzZXJuYW1lOiB1c2VybmFtZSxcbiAgICAgICAgY2hhdElkOiBjaGF0SWRUb1VzZSxcbiAgICAgICAgbWVzc2FnZTogY3VycmVudE1lc3NhZ2UsXG4gICAgICAgIG1vZGVsOiBzZWxlY3RlZE1vZGVsLFxuICAgICAgICBhdHRhY2htZW50czogdW5pcXVlQXR0YWNobWVudHMsXG4gICAgICAgIHdlYlNlYXJjaEVuYWJsZWQ6IHdlYlNlYXJjaEVuYWJsZWQsXG4gICAgICB9LFxuICAgICAgLy8gb25DaHVuayAtIGNyaWFyIG1lbnNhZ2VtIG5hIHByaW1laXJhIGNodW5rIGUgYXR1YWxpemFyIGNvbSBjYWRhIGNodW5rXG4gICAgICAoY2h1bms6IHN0cmluZykgPT4ge1xuICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHtcbiAgICAgICAgICAvLyBWZXJpZmljYXIgc2UgYSBtZW5zYWdlbSBkYSBJQSBqw6EgZXhpc3RlXG4gICAgICAgICAgY29uc3QgZXhpc3RpbmdNZXNzYWdlSW5kZXggPSBwcmV2LmZpbmRJbmRleChtc2cgPT4gbXNnLmlkID09PSBhaU1lc3NhZ2VJZCk7XG5cbiAgICAgICAgICBpZiAoZXhpc3RpbmdNZXNzYWdlSW5kZXggIT09IC0xKSB7XG4gICAgICAgICAgICAvLyBBdHVhbGl6YXIgbWVuc2FnZW0gZXhpc3RlbnRlXG4gICAgICAgICAgICByZXR1cm4gcHJldi5tYXAobXNnID0+XG4gICAgICAgICAgICAgIG1zZy5pZCA9PT0gYWlNZXNzYWdlSWRcbiAgICAgICAgICAgICAgICA/IHsgLi4ubXNnLCBjb250ZW50OiBtc2cuY29udGVudCArIGNodW5rIH1cbiAgICAgICAgICAgICAgICA6IG1zZ1xuICAgICAgICAgICAgKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgLy8gQ3JpYXIgbm92YSBtZW5zYWdlbSBkYSBJQSBuYSBwcmltZWlyYSBjaHVua1xuICAgICAgICAgICAgLy8gUmVtb3ZlciBvIGluZGljYWRvciBkZSBsb2FkaW5nIGFzc2ltIHF1ZSBhIHByaW1laXJhIGNodW5rIGNoZWdhclxuICAgICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcblxuICAgICAgICAgICAgY29uc3QgYWlNZXNzYWdlOiBNZXNzYWdlID0ge1xuICAgICAgICAgICAgICBpZDogYWlNZXNzYWdlSWQsXG4gICAgICAgICAgICAgIGNvbnRlbnQ6IGNodW5rLFxuICAgICAgICAgICAgICBzZW5kZXI6ICdhaScsXG4gICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICBoYXNXZWJTZWFyY2g6IHdlYlNlYXJjaEVuYWJsZWQsXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgcmV0dXJuIFsuLi5wcmV2LCBhaU1lc3NhZ2VdO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICB9LFxuICAgICAgLy8gb25Db21wbGV0ZSAtIGZpbmFsaXphciBzdHJlYW1pbmdcbiAgICAgIChmdWxsUmVzcG9uc2U6IHN0cmluZykgPT4ge1xuICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHByZXYubWFwKG1zZyA9PlxuICAgICAgICAgIG1zZy5pZCA9PT0gYWlNZXNzYWdlSWRcbiAgICAgICAgICAgID8geyAuLi5tc2csIGNvbnRlbnQ6IGZ1bGxSZXNwb25zZSB9XG4gICAgICAgICAgICA6IG1zZ1xuICAgICAgICApKTtcbiAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xuICAgICAgICBzZXRTdHJlYW1pbmdNZXNzYWdlSWQobnVsbCk7XG5cbiAgICAgICAgLy8gU2FsdmFyIG8gbW9kZWxvIHVzYWRvIG5vIGNoYXQgZXNwZWPDrWZpY29cbiAgICAgICAgaWYgKGNoYXRJZFRvVXNlKSB7XG4gICAgICAgICAgc2F2ZUxhc3RVc2VkTW9kZWxGb3JDaGF0KHNlbGVjdGVkTW9kZWwsIGNoYXRJZFRvVXNlKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIEF0dWFsaXphciBzYWxkbyBkbyBPcGVuUm91dGVyIGFww7NzIGEgcmVzcG9zdGEgY29tIGRlbGF5IGRlIDUgc2VndW5kb3NcbiAgICAgICAgaWYgKG9uVXBkYXRlT3BlblJvdXRlckJhbGFuY2UpIHtcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgIG9uVXBkYXRlT3BlblJvdXRlckJhbGFuY2UoKTtcbiAgICAgICAgICB9LCA1MDAwKTtcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIC8vIG9uRXJyb3IgLSB0cmF0YXIgZXJyb3NcbiAgICAgIChlcnJvcjogc3RyaW5nKSA9PiB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gbmEgSUE6JywgZXJyb3IpO1xuICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHByZXYubWFwKG1zZyA9PlxuICAgICAgICAgIG1zZy5pZCA9PT0gYWlNZXNzYWdlSWRcbiAgICAgICAgICAgID8geyAuLi5tc2csIGNvbnRlbnQ6IGDinYwgRXJybzogJHtlcnJvcn1gIH1cbiAgICAgICAgICAgIDogbXNnXG4gICAgICAgICkpO1xuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgICBzZXRJc1N0cmVhbWluZyhmYWxzZSk7XG4gICAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2VJZChudWxsKTtcbiAgICAgIH1cbiAgICApO1xuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgY2FuY2VsYXIgc3RyZWFtaW5nXG4gIGNvbnN0IGhhbmRsZUNhbmNlbFN0cmVhbWluZyA9ICgpID0+IHtcbiAgICBhaVNlcnZpY2UuY2FuY2VsUmVxdWVzdCgpO1xuICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xuICAgIHNldFN0cmVhbWluZ01lc3NhZ2VJZChudWxsKTtcbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGNhcnJlZ2FyIG8gbm9tZSBkbyBjaGF0IGRvIEZpcmVzdG9yZVxuICBjb25zdCBsb2FkQ2hhdE5hbWUgPSBhc3luYyAoY2hhdElkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXVzZXI/LmVtYWlsKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcbiAgICAgIGNvbnN0IGNoYXREb2MgPSBhd2FpdCBnZXREb2MoZG9jKGRiLCAndXN1YXJpb3MnLCB1c2VybmFtZSwgJ2NvbnZlcnNhcycsIGNoYXRJZCkpO1xuXG4gICAgICBpZiAoY2hhdERvYy5leGlzdHMoKSkge1xuICAgICAgICBjb25zdCBjaGF0RGF0YSA9IGNoYXREb2MuZGF0YSgpO1xuICAgICAgICBjb25zdCBjaGF0TmFtZSA9IGNoYXREYXRhLm5hbWUgfHwgJ0NvbnZlcnNhIHNlbSBub21lJztcbiAgICAgICAgc2V0Q2hhdE5hbWUoY2hhdE5hbWUpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0Q2hhdE5hbWUoJ0NvbnZlcnNhIG7Do28gZW5jb250cmFkYScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGNhcnJlZ2FyIG5vbWUgZG8gY2hhdDonLCBlcnJvcik7XG4gICAgICBzZXRDaGF0TmFtZSgnRXJybyBhbyBjYXJyZWdhciBub21lJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgY2FycmVnYXIgbWVuc2FnZW5zIGV4aXN0ZW50ZXMgZG8gY2hhdFxuICBjb25zdCBsb2FkQ2hhdE1lc3NhZ2VzID0gYXN5bmMgKGNoYXRJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCF1c2VyPy5lbWFpbCkgcmV0dXJuO1xuXG4gICAgc2V0SXNMb2FkaW5nQ2hhdCh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcbiAgICAgIGNvbnN0IGNoYXRNZXNzYWdlcyA9IGF3YWl0IGFpU2VydmljZS5sb2FkQ2hhdE1lc3NhZ2VzKHVzZXJuYW1lLCBjaGF0SWQpO1xuICAgICAgY29uc3QgY29udmVydGVkTWVzc2FnZXMgPSBhaVNlcnZpY2UuY29udmVydEZyb21BSUZvcm1hdChjaGF0TWVzc2FnZXMpO1xuICAgICAgc2V0TWVzc2FnZXMoY29udmVydGVkTWVzc2FnZXMpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJybyBhbyBjYXJyZWdhciBtZW5zYWdlbnMgZG8gY2hhdDonLCBlcnJvcik7XG4gICAgICBzZXRNZXNzYWdlcyhbXSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZ0NoYXQoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyBDYXJyZWdhciDDumx0aW1vIG1vZGVsbyB1c2FkbyBnbG9iYWxtZW50ZSBxdWFuZG8gbyBjb21wb25lbnRlIG1vbnRhciAoYXBlbmFzIHNlIG7Do28gaMOhIGNoYXQpXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHVzZXIgJiYgIWN1cnJlbnRDaGF0KSB7XG4gICAgICAvLyBMaW1wYXIgbW9kZWxvcyBpbnbDoWxpZG9zIHVtYSB2ZXogcXVhbmRvIG8gdXN1w6FyaW8gZmF6IGxvZ2luXG4gICAgICBjbGVhbnVwSW52YWxpZE1vZGVsc0Zyb21BbGxDaGF0cygpO1xuICAgICAgbG9hZEdsb2JhbExhc3RVc2VkTW9kZWwoKTtcbiAgICB9XG4gIH0sIFt1c2VyLCBjdXJyZW50Q2hhdF0pO1xuXG4gIC8vIENhcnJlZ2FyIG1lbnNhZ2VucyBxdWFuZG8gbyBjaGF0IGF0dWFsIG11ZGFyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGN1cnJlbnRDaGF0ICYmIGN1cnJlbnRDaGF0ICE9PSBhY3R1YWxDaGF0SWQpIHtcbiAgICAgIHNldEFjdHVhbENoYXRJZChjdXJyZW50Q2hhdCk7XG4gICAgICBzZXRJc0xvYWRpbmdDaGF0KHRydWUpO1xuICAgICAgLy8gTGltcGFyIG1lbnNhZ2VucyBpbWVkaWF0YW1lbnRlIHBhcmEgbW9zdHJhciBvIGVzdGFkbyBkZSBjYXJyZWdhbWVudG9cbiAgICAgIHNldE1lc3NhZ2VzKFtdKTtcbiAgICAgIGxvYWRDaGF0TWVzc2FnZXMoY3VycmVudENoYXQpO1xuICAgICAgbG9hZENoYXROYW1lKGN1cnJlbnRDaGF0KTtcbiAgICAgIC8vIENhcnJlZ2FyIG8gbW9kZWxvIGVzcGVjw61maWNvIGRvIGNoYXRcbiAgICAgIGxvYWRMYXN0VXNlZE1vZGVsRm9yQ2hhdChjdXJyZW50Q2hhdCk7XG4gICAgfSBlbHNlIGlmICghY3VycmVudENoYXQgJiYgYWN0dWFsQ2hhdElkKSB7XG4gICAgICAvLyBTw7MgcmVzZXRhciBzZSByZWFsbWVudGUgbsOjbyBow6EgY2hhdCBlIGhhdmlhIHVtIGNoYXQgYW50ZXNcbiAgICAgIHNldEFjdHVhbENoYXRJZChudWxsKTtcbiAgICAgIHNldE1lc3NhZ2VzKFtdKTtcbiAgICAgIHNldENoYXROYW1lKCdOb3ZhIENvbnZlcnNhJyk7XG4gICAgICBzZXRJc0xvYWRpbmdDaGF0KGZhbHNlKTtcbiAgICAgIC8vIENhcnJlZ2FyIG1vZGVsbyBnbG9iYWwgcXVhbmRvIG7Do28gaMOhIGNoYXQgZXNwZWPDrWZpY29cbiAgICAgIGxvYWRHbG9iYWxMYXN0VXNlZE1vZGVsKCk7XG4gICAgfVxuICB9LCBbY3VycmVudENoYXQsIHVzZXI/LmVtYWlsXSk7XG5cbiAgLy8gRnVuw6fDtWVzIHBhcmEgbWFuaXB1bGFyIG1lbnNhZ2Vuc1xuICBjb25zdCBoYW5kbGVEZWxldGVNZXNzYWdlID0gYXN5bmMgKG1lc3NhZ2VJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFhY3R1YWxDaGF0SWQgfHwgIXVzZXI/LmVtYWlsKSByZXR1cm47XG5cbiAgICAvLyBSZW1vdmVyIHZpc3VhbG1lbnRlIHByaW1laXJvIHBhcmEgbWVsaG9yIFVYXG4gICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2LmZpbHRlcihtc2cgPT4gbXNnLmlkICE9PSBtZXNzYWdlSWQpKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB1c2VybmFtZSA9IGF3YWl0IGdldFVzZXJuYW1lRnJvbUZpcmVzdG9yZSgpO1xuICAgICAgY29uc3Qgc3VjY2VzcyA9IGF3YWl0IGFpU2VydmljZS5kZWxldGVNZXNzYWdlKHVzZXJuYW1lLCBhY3R1YWxDaGF0SWQsIG1lc3NhZ2VJZCk7XG5cbiAgICAgIGlmICghc3VjY2Vzcykge1xuICAgICAgICAvLyBTZSBmYWxob3UsIHJlc3RhdXJhciBhIG1lbnNhZ2VtXG4gICAgICAgIGxvYWRDaGF0TWVzc2FnZXMoYWN0dWFsQ2hhdElkKTtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFsaGEgYW8gZGVsZXRhciBtZW5zYWdlbSBubyBzZXJ2aWRvcicpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAvLyBTZSBmYWxob3UsIHJlc3RhdXJhciBhIG1lbnNhZ2VtXG4gICAgICBsb2FkQ2hhdE1lc3NhZ2VzKGFjdHVhbENoYXRJZCk7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGRlbGV0YXIgbWVuc2FnZW06JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVSZWdlbmVyYXRlTWVzc2FnZSA9IGFzeW5jIChtZXNzYWdlSWQ6IHN0cmluZykgPT4ge1xuICAgIGlmICghYWN0dWFsQ2hhdElkIHx8ICF1c2VyPy5lbWFpbCkgcmV0dXJuO1xuXG4gICAgLy8g4pyFIENPUlJFw4fDg086IFJlY2FycmVnYXIgbWVuc2FnZW5zIGRvIEZpcmViYXNlIFN0b3JhZ2UgcGFyYSBnYXJhbnRpciBlc3RhZG8gYXR1YWxpemFkb1xuICAgIGNvbnNvbGUubG9nKCfwn5SEIFJlY2FycmVnYW5kbyBtZW5zYWdlbnMgYW50ZXMgZGEgcmVnZW5lcmHDp8OjbyBwYXJhIGdhcmFudGlyIGVzdGFkbyBhdHVhbGl6YWRvLi4uJyk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcblxuICAgICAgLy8gQ2FycmVnYXIgbWVuc2FnZW5zIGRpcmV0YW1lbnRlIGRvIEZpcmViYXNlIFN0b3JhZ2VcbiAgICAgIGNvbnN0IGZyZXNoTWVzc2FnZXMgPSBhd2FpdCBhaVNlcnZpY2UubG9hZENoYXRNZXNzYWdlcyh1c2VybmFtZSwgYWN0dWFsQ2hhdElkKTtcbiAgICAgIGNvbnN0IGNvbnZlcnRlZEZyZXNoTWVzc2FnZXMgPSBhaVNlcnZpY2UuY29udmVydEZyb21BSUZvcm1hdChmcmVzaE1lc3NhZ2VzKTtcblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6UgTWVuc2FnZW5zIHJlY2FycmVnYWRhcyBkbyBTdG9yYWdlOicsIGNvbnZlcnRlZEZyZXNoTWVzc2FnZXMubGVuZ3RoKTtcblxuICAgICAgLy8gQnVzY2FyIG8gw61uZGljZSBkYSBtZW5zYWdlbSBuYXMgbWVuc2FnZW5zIGZyZXNjYXNcbiAgICAgIGNvbnN0IG1lc3NhZ2VJbmRleCA9IGNvbnZlcnRlZEZyZXNoTWVzc2FnZXMuZmluZEluZGV4KG1zZyA9PiBtc2cuaWQgPT09IG1lc3NhZ2VJZCk7XG4gICAgICBpZiAobWVzc2FnZUluZGV4ID09PSAtMSkge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgTWVuc2FnZW0gbsOjbyBlbmNvbnRyYWRhIGFww7NzIHJlY2FycmVnYXI6JywgbWVzc2FnZUlkKTtcbiAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IG1lc3NhZ2VUb1JlZ2VuZXJhdGUgPSBjb252ZXJ0ZWRGcmVzaE1lc3NhZ2VzW21lc3NhZ2VJbmRleF07XG4gICAgICBjb25zb2xlLmxvZygn8J+TnSBNZW5zYWdlbSBxdWUgc2Vyw6EgcmVnZW5lcmFkYTonLCB7XG4gICAgICAgIGlkOiBtZXNzYWdlVG9SZWdlbmVyYXRlLmlkLFxuICAgICAgICBjb250ZW50OiBtZXNzYWdlVG9SZWdlbmVyYXRlLmNvbnRlbnQuc3Vic3RyaW5nKDAsIDEwMCkgKyAnLi4uJyxcbiAgICAgICAgaW5kZXg6IG1lc3NhZ2VJbmRleFxuICAgICAgfSk7XG5cbiAgICAgIC8vIFJlbW92ZXIgYXBlbmFzIGFzIG1lbnNhZ2VucyBBUMOTUyBhIG1lbnNhZ2VtIHNlbGVjaW9uYWRhIChuw6NvIGluY2x1aW5kbyBlbGEpXG4gICAgICBjb25zdCBtZXNzYWdlc0JlZm9yZVJlZ2VuZXJhdGlvbiA9IGNvbnZlcnRlZEZyZXNoTWVzc2FnZXMuc2xpY2UoMCwgbWVzc2FnZUluZGV4ICsgMSk7XG4gICAgICBzZXRNZXNzYWdlcyhtZXNzYWdlc0JlZm9yZVJlZ2VuZXJhdGlvbik7XG5cbiAgICAgIC8vIFByZXBhcmFyIG8gY29udGXDumRvIGRhIG1lbnNhZ2VtIHBhcmEgcmVnZW5lcmFyXG4gICAgICBzZXRNZXNzYWdlKG1lc3NhZ2VUb1JlZ2VuZXJhdGUuY29udGVudCk7XG4gICAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG4gICAgICBzZXRJc1N0cmVhbWluZyh0cnVlKTtcblxuICAgICAgLy8gUHJlcGFyYXIgSUQgcGFyYSBhIG5vdmEgbWVuc2FnZW0gZGEgSUEgcXVlIHNlcsOhIGNyaWFkYSBkdXJhbnRlIG8gc3RyZWFtaW5nXG4gICAgICBjb25zdCBhaU1lc3NhZ2VJZCA9IGFpU2VydmljZS5nZW5lcmF0ZU1lc3NhZ2VJZCgpO1xuICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZUlkKGFpTWVzc2FnZUlkKTtcblxuICAgICAgLy8gRGVsZXRhciBhcGVuYXMgYXMgbWVuc2FnZW5zIFBPU1RFUklPUkVTIGRvIEZpcmViYXNlIFN0b3JhZ2UgKG7Do28gYSBtZW5zYWdlbSBhdHVhbClcbiAgICAgIGNvbnNvbGUubG9nKGDwn5eR77iPIERlbGV0YW5kbyAke2NvbnZlcnRlZEZyZXNoTWVzc2FnZXMubGVuZ3RoIC0gbWVzc2FnZUluZGV4IC0gMX0gbWVuc2FnZW5zIHBvc3RlcmlvcmVzLi4uYCk7XG4gICAgICBmb3IgKGxldCBpID0gbWVzc2FnZUluZGV4ICsgMTsgaSA8IGNvbnZlcnRlZEZyZXNoTWVzc2FnZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3QgbXNnVG9EZWxldGUgPSBjb252ZXJ0ZWRGcmVzaE1lc3NhZ2VzW2ldO1xuICAgICAgICBjb25zb2xlLmxvZygn8J+Xke+4jyBEZWxldGFuZG8gbWVuc2FnZW06JywgbXNnVG9EZWxldGUuaWQpO1xuICAgICAgICBhd2FpdCBhaVNlcnZpY2UuZGVsZXRlTWVzc2FnZSh1c2VybmFtZSwgYWN0dWFsQ2hhdElkLCBtc2dUb0RlbGV0ZS5pZCk7XG4gICAgICB9XG5cbiAgICAgIC8vIEVudmlhciBwYXJhIGEgSUEgcGFyYSByZWdlbmVyYXJcbiAgICAgIGF3YWl0IGFpU2VydmljZS5zZW5kTWVzc2FnZVNhZmUoXG4gICAgICAgIHtcbiAgICAgICAgICB1c2VybmFtZTogdXNlcm5hbWUsXG4gICAgICAgICAgY2hhdElkOiBhY3R1YWxDaGF0SWQsXG4gICAgICAgICAgbWVzc2FnZTogbWVzc2FnZVRvUmVnZW5lcmF0ZS5jb250ZW50LFxuICAgICAgICAgIG1vZGVsOiBzZWxlY3RlZE1vZGVsLFxuICAgICAgICAgIGlzUmVnZW5lcmF0aW9uOiB0cnVlLFxuICAgICAgICB9LFxuICAgICAgICAvLyBvbkNodW5rIC0gY3JpYXIgbWVuc2FnZW0gbmEgcHJpbWVpcmEgY2h1bmsgZSBhdHVhbGl6YXIgY29tIGNhZGEgY2h1bmtcbiAgICAgICAgKGNodW5rOiBzdHJpbmcpID0+IHtcbiAgICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHtcbiAgICAgICAgICAgIC8vIFZlcmlmaWNhciBzZSBhIG1lbnNhZ2VtIGRhIElBIGrDoSBleGlzdGVcbiAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nTWVzc2FnZUluZGV4ID0gcHJldi5maW5kSW5kZXgobXNnID0+IG1zZy5pZCA9PT0gYWlNZXNzYWdlSWQpO1xuXG4gICAgICAgICAgICBpZiAoZXhpc3RpbmdNZXNzYWdlSW5kZXggIT09IC0xKSB7XG4gICAgICAgICAgICAgIC8vIEF0dWFsaXphciBtZW5zYWdlbSBleGlzdGVudGVcbiAgICAgICAgICAgICAgcmV0dXJuIHByZXYubWFwKG1zZyA9PlxuICAgICAgICAgICAgICAgIG1zZy5pZCA9PT0gYWlNZXNzYWdlSWRcbiAgICAgICAgICAgICAgICAgID8geyAuLi5tc2csIGNvbnRlbnQ6IG1zZy5jb250ZW50ICsgY2h1bmsgfVxuICAgICAgICAgICAgICAgICAgOiBtc2dcbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgIC8vIENyaWFyIG5vdmEgbWVuc2FnZW0gZGEgSUEgbmEgcHJpbWVpcmEgY2h1bmtcbiAgICAgICAgICAgICAgLy8gUmVtb3ZlciBvIGluZGljYWRvciBkZSBsb2FkaW5nIGFzc2ltIHF1ZSBhIHByaW1laXJhIGNodW5rIGNoZWdhclxuICAgICAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuXG4gICAgICAgICAgICAgIGNvbnN0IGFpTWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgICAgICAgICAgICBpZDogYWlNZXNzYWdlSWQsXG4gICAgICAgICAgICAgICAgY29udGVudDogY2h1bmssXG4gICAgICAgICAgICAgICAgc2VuZGVyOiAnYWknLFxuICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICAgIGhhc1dlYlNlYXJjaDogZmFsc2UsIC8vIFJlZ2VuZXJhw6fDo28gbsOjbyB1c2Egd2ViIHNlYXJjaCBwb3IgcGFkcsOjb1xuICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICByZXR1cm4gWy4uLnByZXYsIGFpTWVzc2FnZV07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIC8vIG9uQ29tcGxldGUgLSBmaW5hbGl6YXIgc3RyZWFtaW5nXG4gICAgICAgIChmdWxsUmVzcG9uc2U6IHN0cmluZykgPT4ge1xuICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gcHJldi5tYXAobXNnID0+XG4gICAgICAgICAgICBtc2cuaWQgPT09IGFpTWVzc2FnZUlkXG4gICAgICAgICAgICAgID8geyAuLi5tc2csIGNvbnRlbnQ6IGZ1bGxSZXNwb25zZSB9XG4gICAgICAgICAgICAgIDogbXNnXG4gICAgICAgICAgKSk7XG4gICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgICBzZXRJc1N0cmVhbWluZyhmYWxzZSk7XG4gICAgICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZUlkKG51bGwpO1xuICAgICAgICAgIHNldE1lc3NhZ2UoJycpOyAvLyBMaW1wYXIgbyBjYW1wbyBkZSBpbnB1dFxuXG4gICAgICAgICAgLy8gQXR1YWxpemFyIHNhbGRvIGRvIE9wZW5Sb3V0ZXIgYXDDs3MgYSByZWdlbmVyYcOnw6NvIGNvbSBkZWxheSBkZSA1IHNlZ3VuZG9zXG4gICAgICAgICAgaWYgKG9uVXBkYXRlT3BlblJvdXRlckJhbGFuY2UpIHtcbiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgICBvblVwZGF0ZU9wZW5Sb3V0ZXJCYWxhbmNlKCk7XG4gICAgICAgICAgICB9LCA1MDAwKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIC8vIG9uRXJyb3IgLSB0cmF0YXIgZXJyb3NcbiAgICAgICAgKGVycm9yOiBzdHJpbmcpID0+IHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIG5hIHJlZ2VuZXJhw6fDo286JywgZXJyb3IpO1xuICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gcHJldi5tYXAobXNnID0+XG4gICAgICAgICAgICBtc2cuaWQgPT09IGFpTWVzc2FnZUlkXG4gICAgICAgICAgICAgID8geyAuLi5tc2csIGNvbnRlbnQ6IGDinYwgRXJybyBuYSByZWdlbmVyYcOnw6NvOiAke2Vycm9yfWAgfVxuICAgICAgICAgICAgICA6IG1zZ1xuICAgICAgICAgICkpO1xuICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xuICAgICAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2VJZChudWxsKTtcbiAgICAgICAgICBzZXRNZXNzYWdlKCcnKTsgLy8gTGltcGFyIG8gY2FtcG8gZGUgaW5wdXRcbiAgICAgICAgfVxuICAgICAgKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJybyBhbyByZWdlbmVyYXIgbWVuc2FnZW06JywgZXJyb3IpO1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcbiAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2VJZChudWxsKTtcbiAgICAgIHNldE1lc3NhZ2UoJycpOyAvLyBMaW1wYXIgbyBjYW1wbyBkZSBpbnB1dFxuXG4gICAgICAvLyBSZWNhcnJlZ2FyIG1lbnNhZ2VucyBlbSBjYXNvIGRlIGVycm9cbiAgICAgIGxvYWRDaGF0TWVzc2FnZXMoYWN0dWFsQ2hhdElkKTtcbiAgICB9XG4gIH07XG5cblxuXG4gIGNvbnN0IGhhbmRsZUVkaXRNZXNzYWdlID0gYXN5bmMgKG1lc3NhZ2VJZDogc3RyaW5nLCBuZXdDb250ZW50OiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgICBpZiAoIWFjdHVhbENoYXRJZCB8fCAhdXNlcj8uZW1haWwpIHJldHVybiBmYWxzZTtcblxuICAgIGNvbnNvbGUubG9nKCfinI/vuI8gSW5pY2lhbmRvIGVkacOnw6NvIGRlIG1lbnNhZ2VtOicsIHtcbiAgICAgIG1lc3NhZ2VJZCxcbiAgICAgIGNoYXRJZDogYWN0dWFsQ2hhdElkLFxuICAgICAgbmV3Q29udGVudExlbmd0aDogbmV3Q29udGVudC5sZW5ndGgsXG4gICAgICBuZXdDb250ZW50UHJldmlldzogbmV3Q29udGVudC5zdWJzdHJpbmcoMCwgMTAwKSArICcuLi4nXG4gICAgfSk7XG5cbiAgICAvLyBBdHVhbGl6YXIgdmlzdWFsbWVudGUgcHJpbWVpcm8gcGFyYSBtZWxob3IgVVhcbiAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHByZXYubWFwKG1zZyA9PlxuICAgICAgbXNnLmlkID09PSBtZXNzYWdlSWQgPyB7IC4uLm1zZywgY29udGVudDogbmV3Q29udGVudCB9IDogbXNnXG4gICAgKSk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OkIEVudmlhbmRvIGF0dWFsaXphw6fDo28gcGFyYSBvIHNlcnZpZG9yLi4uJyk7XG5cbiAgICAgIGNvbnN0IHN1Y2Nlc3MgPSBhd2FpdCBhaVNlcnZpY2UudXBkYXRlTWVzc2FnZSh1c2VybmFtZSwgYWN0dWFsQ2hhdElkLCBtZXNzYWdlSWQsIG5ld0NvbnRlbnQpO1xuXG4gICAgICBpZiAoIXN1Y2Nlc3MpIHtcbiAgICAgICAgLy8gU2UgZmFsaG91LCByZXN0YXVyYXIgbyBjb250ZcO6ZG8gb3JpZ2luYWxcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEZhbGhhIGFvIGF0dWFsaXphciBtZW5zYWdlbSBubyBzZXJ2aWRvcicpO1xuICAgICAgICBsb2FkQ2hhdE1lc3NhZ2VzKGFjdHVhbENoYXRJZCk7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgTWVuc2FnZW0gZWRpdGFkYSBlIHNhbHZhIGNvbSBzdWNlc3NvIG5vIEZpcmViYXNlIFN0b3JhZ2U6Jywge1xuICAgICAgICAgIG1lc3NhZ2VJZCxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIC8vIFNlIGZhbGhvdSwgcmVzdGF1cmFyIG8gY29udGXDumRvIG9yaWdpbmFsXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJybyBhbyBhdHVhbGl6YXIgbWVuc2FnZW06JywgZXJyb3IpO1xuICAgICAgbG9hZENoYXRNZXNzYWdlcyhhY3R1YWxDaGF0SWQpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVFZGl0QW5kUmVnZW5lcmF0ZSA9IGFzeW5jIChtZXNzYWdlSWQ6IHN0cmluZywgbmV3Q29udGVudDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgaWYgKCFhY3R1YWxDaGF0SWQgfHwgIXVzZXI/LmVtYWlsKSByZXR1cm47XG5cbiAgICBjb25zb2xlLmxvZygn4pyP77iP8J+UhCBJbmljaWFuZG8gZWRpw6fDo28gZSByZWdlbmVyYcOnw6NvIGRlIG1lbnNhZ2VtOicsIHtcbiAgICAgIG1lc3NhZ2VJZCxcbiAgICAgIGNoYXRJZDogYWN0dWFsQ2hhdElkLFxuICAgICAgbmV3Q29udGVudExlbmd0aDogbmV3Q29udGVudC5sZW5ndGgsXG4gICAgICBuZXdDb250ZW50UHJldmlldzogbmV3Q29udGVudC5zdWJzdHJpbmcoMCwgMTAwKSArICcuLi4nXG4gICAgfSk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gMS4gUHJpbWVpcm8sIHNhbHZhciBhIGVkacOnw6NvXG4gICAgICBjb25zdCBlZGl0U3VjY2VzcyA9IGF3YWl0IGhhbmRsZUVkaXRNZXNzYWdlKG1lc3NhZ2VJZCwgbmV3Q29udGVudCk7XG4gICAgICBpZiAoIWVkaXRTdWNjZXNzKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWxoYSBhbyBlZGl0YXIgbWVuc2FnZW0sIGNhbmNlbGFuZG8gcmVnZW5lcmHDp8OjbycpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfinIUgTWVuc2FnZW0gZWRpdGFkYSBjb20gc3VjZXNzbywgaW5pY2lhbmRvIHJlZ2VuZXJhw6fDo28uLi4nKTtcblxuICAgICAgLy8gMi4gQWd1YXJkYXIgdW0gcG91Y28gcGFyYSBnYXJhbnRpciBxdWUgYSBlZGnDp8OjbyBmb2kgc2FsdmFcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAyMDApKTtcblxuICAgICAgLy8gMy4gUmVjYXJyZWdhciBtZW5zYWdlbnMgZG8gRmlyZWJhc2UgU3RvcmFnZSBwYXJhIGdhcmFudGlyIGVzdGFkbyBhdHVhbGl6YWRvXG4gICAgICBjb25zb2xlLmxvZygn8J+UhCBSZWNhcnJlZ2FuZG8gbWVuc2FnZW5zIGFudGVzIGRhIHJlZ2VuZXJhw6fDo28uLi4nKTtcblxuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcblxuICAgICAgLy8gQ2FycmVnYXIgbWVuc2FnZW5zIGRpcmV0YW1lbnRlIGRvIEZpcmViYXNlIFN0b3JhZ2VcbiAgICAgIGNvbnN0IGZyZXNoTWVzc2FnZXMgPSBhd2FpdCBhaVNlcnZpY2UubG9hZENoYXRNZXNzYWdlcyh1c2VybmFtZSwgYWN0dWFsQ2hhdElkKTtcbiAgICAgIGNvbnN0IGNvbnZlcnRlZEZyZXNoTWVzc2FnZXMgPSBhaVNlcnZpY2UuY29udmVydEZyb21BSUZvcm1hdChmcmVzaE1lc3NhZ2VzKTtcblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6UgTWVuc2FnZW5zIHJlY2FycmVnYWRhcyBkbyBTdG9yYWdlOicsIGNvbnZlcnRlZEZyZXNoTWVzc2FnZXMubGVuZ3RoKTtcblxuICAgICAgLy8gNC4gQnVzY2FyIG8gw61uZGljZSBkYSBtZW5zYWdlbSBuYXMgbWVuc2FnZW5zIGZyZXNjYXNcbiAgICAgIGNvbnN0IG1lc3NhZ2VJbmRleCA9IGNvbnZlcnRlZEZyZXNoTWVzc2FnZXMuZmluZEluZGV4KG1zZyA9PiBtc2cuaWQgPT09IG1lc3NhZ2VJZCk7XG4gICAgICBpZiAobWVzc2FnZUluZGV4ID09PSAtMSkge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgTWVuc2FnZW0gbsOjbyBlbmNvbnRyYWRhIGFww7NzIHJlY2FycmVnYXI6JywgbWVzc2FnZUlkKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBtZXNzYWdlVG9SZWdlbmVyYXRlID0gY29udmVydGVkRnJlc2hNZXNzYWdlc1ttZXNzYWdlSW5kZXhdO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk50gTWVuc2FnZW0gcXVlIHNlcsOhIHJlZ2VuZXJhZGE6Jywge1xuICAgICAgICBpZDogbWVzc2FnZVRvUmVnZW5lcmF0ZS5pZCxcbiAgICAgICAgY29udGVudDogbWVzc2FnZVRvUmVnZW5lcmF0ZS5jb250ZW50LnN1YnN0cmluZygwLCAxMDApICsgJy4uLicsXG4gICAgICAgIGluZGV4OiBtZXNzYWdlSW5kZXhcbiAgICAgIH0pO1xuXG4gICAgICAvLyA1LiBWZXJpZmljYXIgc2UgaMOhIG1lbnNhZ2VucyBhcMOzcyBlc3RhIG1lbnNhZ2VtXG4gICAgICBjb25zdCBoYXNNZXNzYWdlc0FmdGVyID0gbWVzc2FnZUluZGV4IDwgY29udmVydGVkRnJlc2hNZXNzYWdlcy5sZW5ndGggLSAxO1xuICAgICAgY29uc29sZS5sb2coYPCfk4ogTWVuc2FnZW5zIGFww7NzIGVzdGE6ICR7Y29udmVydGVkRnJlc2hNZXNzYWdlcy5sZW5ndGggLSBtZXNzYWdlSW5kZXggLSAxfWApO1xuXG4gICAgICAvLyA2LiBSZW1vdmVyIGFwZW5hcyBhcyBtZW5zYWdlbnMgQVDDk1MgYSBtZW5zYWdlbSBzZWxlY2lvbmFkYSAobsOjbyBpbmNsdWluZG8gZWxhKVxuICAgICAgY29uc3QgbWVzc2FnZXNCZWZvcmVSZWdlbmVyYXRpb24gPSBjb252ZXJ0ZWRGcmVzaE1lc3NhZ2VzLnNsaWNlKDAsIG1lc3NhZ2VJbmRleCArIDEpO1xuICAgICAgc2V0TWVzc2FnZXMobWVzc2FnZXNCZWZvcmVSZWdlbmVyYXRpb24pO1xuXG4gICAgICAvLyA3LiBQcmVwYXJhciBvIGNvbnRlw7pkbyBkYSBtZW5zYWdlbSBwYXJhIHJlZ2VuZXJhclxuICAgICAgc2V0TWVzc2FnZShtZXNzYWdlVG9SZWdlbmVyYXRlLmNvbnRlbnQpO1xuICAgICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgICAgc2V0SXNTdHJlYW1pbmcodHJ1ZSk7XG5cbiAgICAgIC8vIDguIFByZXBhcmFyIElEIHBhcmEgYSBub3ZhIG1lbnNhZ2VtIGRhIElBIHF1ZSBzZXLDoSBjcmlhZGEgZHVyYW50ZSBvIHN0cmVhbWluZ1xuICAgICAgY29uc3QgYWlNZXNzYWdlSWQgPSBhaVNlcnZpY2UuZ2VuZXJhdGVNZXNzYWdlSWQoKTtcbiAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2VJZChhaU1lc3NhZ2VJZCk7XG5cbiAgICAgIC8vIDkuIERlbGV0YXIgYXBlbmFzIGFzIG1lbnNhZ2VucyBQT1NURVJJT1JFUyBkbyBGaXJlYmFzZSBTdG9yYWdlIChzZSBob3V2ZXIpXG4gICAgICBpZiAoaGFzTWVzc2FnZXNBZnRlcikge1xuICAgICAgICBjb25zb2xlLmxvZyhg8J+Xke+4jyBEZWxldGFuZG8gJHtjb252ZXJ0ZWRGcmVzaE1lc3NhZ2VzLmxlbmd0aCAtIG1lc3NhZ2VJbmRleCAtIDF9IG1lbnNhZ2VucyBwb3N0ZXJpb3Jlcy4uLmApO1xuICAgICAgICBmb3IgKGxldCBpID0gbWVzc2FnZUluZGV4ICsgMTsgaSA8IGNvbnZlcnRlZEZyZXNoTWVzc2FnZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICBjb25zdCBtc2dUb0RlbGV0ZSA9IGNvbnZlcnRlZEZyZXNoTWVzc2FnZXNbaV07XG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfl5HvuI8gRGVsZXRhbmRvIG1lbnNhZ2VtOicsIG1zZ1RvRGVsZXRlLmlkKTtcbiAgICAgICAgICBhd2FpdCBhaVNlcnZpY2UuZGVsZXRlTWVzc2FnZSh1c2VybmFtZSwgYWN0dWFsQ2hhdElkLCBtc2dUb0RlbGV0ZS5pZCk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gMTAuIEVudmlhciBwYXJhIGEgSUEgcGFyYSByZWdlbmVyYXJcbiAgICAgIGF3YWl0IGFpU2VydmljZS5zZW5kTWVzc2FnZVNhZmUoXG4gICAgICAgIHtcbiAgICAgICAgICB1c2VybmFtZTogdXNlcm5hbWUsXG4gICAgICAgICAgY2hhdElkOiBhY3R1YWxDaGF0SWQsXG4gICAgICAgICAgbWVzc2FnZTogbWVzc2FnZVRvUmVnZW5lcmF0ZS5jb250ZW50LFxuICAgICAgICAgIG1vZGVsOiBzZWxlY3RlZE1vZGVsLFxuICAgICAgICAgIGlzUmVnZW5lcmF0aW9uOiB0cnVlLFxuICAgICAgICB9LFxuICAgICAgICAvLyBvbkNodW5rIC0gY3JpYXIgbWVuc2FnZW0gbmEgcHJpbWVpcmEgY2h1bmsgZSBhdHVhbGl6YXIgY29tIGNhZGEgY2h1bmtcbiAgICAgICAgKGNodW5rOiBzdHJpbmcpID0+IHtcbiAgICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHtcbiAgICAgICAgICAgIC8vIFZlcmlmaWNhciBzZSBhIG1lbnNhZ2VtIGRhIElBIGrDoSBleGlzdGVcbiAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nTWVzc2FnZUluZGV4ID0gcHJldi5maW5kSW5kZXgobXNnID0+IG1zZy5pZCA9PT0gYWlNZXNzYWdlSWQpO1xuXG4gICAgICAgICAgICBpZiAoZXhpc3RpbmdNZXNzYWdlSW5kZXggIT09IC0xKSB7XG4gICAgICAgICAgICAgIC8vIEF0dWFsaXphciBtZW5zYWdlbSBleGlzdGVudGVcbiAgICAgICAgICAgICAgcmV0dXJuIHByZXYubWFwKG1zZyA9PlxuICAgICAgICAgICAgICAgIG1zZy5pZCA9PT0gYWlNZXNzYWdlSWRcbiAgICAgICAgICAgICAgICAgID8geyAuLi5tc2csIGNvbnRlbnQ6IG1zZy5jb250ZW50ICsgY2h1bmsgfVxuICAgICAgICAgICAgICAgICAgOiBtc2dcbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgIC8vIENyaWFyIG5vdmEgbWVuc2FnZW0gZGEgSUEgbmEgcHJpbWVpcmEgY2h1bmtcbiAgICAgICAgICAgICAgLy8gUmVtb3ZlciBvIGluZGljYWRvciBkZSBsb2FkaW5nIGFzc2ltIHF1ZSBhIHByaW1laXJhIGNodW5rIGNoZWdhclxuICAgICAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuXG4gICAgICAgICAgICAgIGNvbnN0IGFpTWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgICAgICAgICAgICBpZDogYWlNZXNzYWdlSWQsXG4gICAgICAgICAgICAgICAgY29udGVudDogY2h1bmssXG4gICAgICAgICAgICAgICAgc2VuZGVyOiAnYWknLFxuICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICAgIGhhc1dlYlNlYXJjaDogZmFsc2UsIC8vIFJlZ2VuZXJhw6fDo28gbsOjbyB1c2Egd2ViIHNlYXJjaCBwb3IgcGFkcsOjb1xuICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICByZXR1cm4gWy4uLnByZXYsIGFpTWVzc2FnZV07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIC8vIG9uQ29tcGxldGUgLSBmaW5hbGl6YXIgc3RyZWFtaW5nXG4gICAgICAgIChmdWxsUmVzcG9uc2U6IHN0cmluZykgPT4ge1xuICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gcHJldi5tYXAobXNnID0+XG4gICAgICAgICAgICBtc2cuaWQgPT09IGFpTWVzc2FnZUlkXG4gICAgICAgICAgICAgID8geyAuLi5tc2csIGNvbnRlbnQ6IGZ1bGxSZXNwb25zZSB9XG4gICAgICAgICAgICAgIDogbXNnXG4gICAgICAgICAgKSk7XG4gICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgICBzZXRJc1N0cmVhbWluZyhmYWxzZSk7XG4gICAgICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZUlkKG51bGwpO1xuICAgICAgICAgIHNldE1lc3NhZ2UoJycpOyAvLyBMaW1wYXIgbyBjYW1wbyBkZSBpbnB1dFxuXG4gICAgICAgICAgLy8gQXR1YWxpemFyIHNhbGRvIGRvIE9wZW5Sb3V0ZXIgYXDDs3MgYSByZWdlbmVyYcOnw6NvIGNvbSBkZWxheSBkZSA1IHNlZ3VuZG9zXG4gICAgICAgICAgaWYgKG9uVXBkYXRlT3BlblJvdXRlckJhbGFuY2UpIHtcbiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgICBvblVwZGF0ZU9wZW5Sb3V0ZXJCYWxhbmNlKCk7XG4gICAgICAgICAgICB9LCA1MDAwKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIC8vIG9uRXJyb3IgLSB0cmF0YXIgZXJyb3NcbiAgICAgICAgKGVycm9yOiBzdHJpbmcpID0+IHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIG5hIHJlZ2VuZXJhw6fDo286JywgZXJyb3IpO1xuICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gcHJldi5tYXAobXNnID0+XG4gICAgICAgICAgICBtc2cuaWQgPT09IGFpTWVzc2FnZUlkXG4gICAgICAgICAgICAgID8geyAuLi5tc2csIGNvbnRlbnQ6IGDinYwgRXJybyBuYSByZWdlbmVyYcOnw6NvOiAke2Vycm9yfWAgfVxuICAgICAgICAgICAgICA6IG1zZ1xuICAgICAgICAgICkpO1xuICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xuICAgICAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2VJZChudWxsKTtcbiAgICAgICAgICBzZXRNZXNzYWdlKCcnKTsgLy8gTGltcGFyIG8gY2FtcG8gZGUgaW5wdXRcbiAgICAgICAgfVxuICAgICAgKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJybyBhbyBlZGl0YXIgZSByZWdlbmVyYXIgbWVuc2FnZW06JywgZXJyb3IpO1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcbiAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2VJZChudWxsKTtcbiAgICAgIHNldE1lc3NhZ2UoJycpOyAvLyBMaW1wYXIgbyBjYW1wbyBkZSBpbnB1dFxuXG4gICAgICAvLyBSZWNhcnJlZ2FyIG1lbnNhZ2VucyBlbSBjYXNvIGRlIGVycm9cbiAgICAgIGxvYWRDaGF0TWVzc2FnZXMoYWN0dWFsQ2hhdElkKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ29weU1lc3NhZ2UgPSAoY29udGVudDogc3RyaW5nKSA9PiB7XG4gICAgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQoY29udGVudCkudGhlbigoKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnTWVuc2FnZW0gY29waWFkYSBwYXJhIGEgw6FyZWEgZGUgdHJhbnNmZXLDqm5jaWEnKTtcbiAgICB9KTtcbiAgfTtcblxuICAvLyBGdW7Dp8O1ZXMgZGUgbmF2ZWdhw6fDo29cbiAgY29uc3QgaGFuZGxlU2Nyb2xsVG9Ub3AgPSAoKSA9PiB7XG4gICAgY29uc3Qgc2Nyb2xsQ29udGFpbmVyID0gY2hhdEludGVyZmFjZVJlZi5jdXJyZW50Py5xdWVyeVNlbGVjdG9yKCcub3ZlcmZsb3cteS1hdXRvJyk7XG4gICAgaWYgKHNjcm9sbENvbnRhaW5lcikge1xuICAgICAgc2Nyb2xsQ29udGFpbmVyLnNjcm9sbFRvKHsgdG9wOiAwLCBiZWhhdmlvcjogJ3Ntb290aCcgfSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNjcm9sbFRvQm90dG9tID0gKCkgPT4ge1xuICAgIGNvbnN0IHNjcm9sbENvbnRhaW5lciA9IGNoYXRJbnRlcmZhY2VSZWYuY3VycmVudD8ucXVlcnlTZWxlY3RvcignLm92ZXJmbG93LXktYXV0bycpO1xuICAgIGlmIChzY3JvbGxDb250YWluZXIpIHtcbiAgICAgIHNjcm9sbENvbnRhaW5lci5zY3JvbGxUbyh7XG4gICAgICAgIHRvcDogc2Nyb2xsQ29udGFpbmVyLnNjcm9sbEhlaWdodCxcbiAgICAgICAgYmVoYXZpb3I6ICdzbW9vdGgnXG4gICAgICB9KTtcbiAgICB9XG4gIH07XG5cblxuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgY29udmVydGVyIG1lbnNhZ2VucyBwYXJhIG8gZm9ybWF0byBDaGF0TWVzc2FnZVxuICBjb25zdCBjb252ZXJ0VG9DaGF0TWVzc2FnZXMgPSAobWVzc2FnZXM6IE1lc3NhZ2VbXSk6IENoYXRNZXNzYWdlW10gPT4ge1xuICAgIHJldHVybiBtZXNzYWdlcy5tYXAobXNnID0+ICh7XG4gICAgICBpZDogbXNnLmlkLFxuICAgICAgY29udGVudDogbXNnLmNvbnRlbnQsXG4gICAgICByb2xlOiBtc2cuc2VuZGVyID09PSAndXNlcicgPyAndXNlcicgOiAnYXNzaXN0YW50JyxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUobXNnLnRpbWVzdGFtcCkuZ2V0VGltZSgpLFxuICAgICAgaXNGYXZvcml0ZTogbXNnLmlzRmF2b3JpdGUgfHwgZmFsc2UsXG4gICAgICBhdHRhY2htZW50czogbXNnLmF0dGFjaG1lbnRzIHx8IFtdXG4gICAgfSkpO1xuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgYWJyaXIgbyBtb2RhbCBkZSBkb3dubG9hZFxuICBjb25zdCBoYW5kbGVEb3dubG9hZE1vZGFsID0gKCkgPT4ge1xuICAgIHNldElzRG93bmxvYWRNb2RhbE9wZW4odHJ1ZSk7XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBhYnJpciBvIG1vZGFsIGRlIGFuZXhvc1xuICBjb25zdCBoYW5kbGVBdHRhY2htZW50c01vZGFsID0gKCkgPT4ge1xuICAgIHNldElzQXR0YWNobWVudHNNb2RhbE9wZW4odHJ1ZSk7XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBhYnJpciBvIG1vZGFsIGRlIGVzdGF0w61zdGljYXNcbiAgY29uc3QgaGFuZGxlU3RhdGlzdGljc01vZGFsID0gKCkgPT4ge1xuICAgIHNldElzU3RhdGlzdGljc01vZGFsT3Blbih0cnVlKTtcbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIG9idGVyIHRvZG9zIG9zIGFuZXhvcyBkbyBjaGF0XG4gIGNvbnN0IGdldEFsbENoYXRBdHRhY2htZW50cyA9ICgpOiBpbXBvcnQoJ0AvbGliL3R5cGVzL2NoYXQnKS5BdHRhY2htZW50TWV0YWRhdGFbXSA9PiB7XG4gICAgY29uc3QgYWxsQXR0YWNobWVudHM6IGltcG9ydCgnQC9saWIvdHlwZXMvY2hhdCcpLkF0dGFjaG1lbnRNZXRhZGF0YVtdID0gW107XG5cbiAgICBtZXNzYWdlcy5mb3JFYWNoKG1lc3NhZ2UgPT4ge1xuICAgICAgaWYgKG1lc3NhZ2UuYXR0YWNobWVudHMgJiYgbWVzc2FnZS5hdHRhY2htZW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGFsbEF0dGFjaG1lbnRzLnB1c2goLi4ubWVzc2FnZS5hdHRhY2htZW50cyk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICAvLyBSZW1vdmVyIGR1cGxpY2F0YXMgYmFzZWFkbyBubyBJRFxuICAgIGNvbnN0IHVuaXF1ZUF0dGFjaG1lbnRzID0gYWxsQXR0YWNobWVudHMuZmlsdGVyKChhdHRhY2htZW50LCBpbmRleCwgc2VsZikgPT5cbiAgICAgIGluZGV4ID09PSBzZWxmLmZpbmRJbmRleChhID0+IGEuaWQgPT09IGF0dGFjaG1lbnQuaWQpXG4gICAgKTtcblxuICAgIHJldHVybiB1bmlxdWVBdHRhY2htZW50cztcbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIHNhbHZhciBvIGVzdGFkbyBkb3MgYW5leG9zIG5vIEZpcmViYXNlIFN0b3JhZ2VcbiAgY29uc3Qgc2F2ZUF0dGFjaG1lbnRTdGF0ZXMgPSBhc3luYyAodXBkYXRlZE1lc3NhZ2VzOiBNZXNzYWdlW10pID0+IHtcbiAgICBpZiAoIWN1cnJlbnRVc2VybmFtZSB8fCAhYWN0dWFsQ2hhdElkKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgLy8gUHJlcGFyYXIgZGFkb3MgZG8gY2hhdCBwYXJhIHNhbHZhclxuICAgICAgY29uc3QgY2hhdERhdGEgPSB7XG4gICAgICAgIGlkOiBhY3R1YWxDaGF0SWQsXG4gICAgICAgIG5hbWU6IGNoYXROYW1lIHx8ICdDaGF0JyxcbiAgICAgICAgbWVzc2FnZXM6IHVwZGF0ZWRNZXNzYWdlcy5tYXAobXNnID0+ICh7XG4gICAgICAgICAgaWQ6IG1zZy5pZCxcbiAgICAgICAgICBjb250ZW50OiBtc2cuY29udGVudCxcbiAgICAgICAgICByb2xlOiBtc2cuc2VuZGVyID09PSAndXNlcicgPyAndXNlcicgOiAnYXNzaXN0YW50JyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG1zZy50aW1lc3RhbXAsXG4gICAgICAgICAgaXNGYXZvcml0ZTogbXNnLmlzRmF2b3JpdGUsXG4gICAgICAgICAgYXR0YWNobWVudHM6IG1zZy5hdHRhY2htZW50c1xuICAgICAgICB9KSksXG4gICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICBsYXN0VXBkYXRlZDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9O1xuXG4gICAgICAvLyBTYWx2YXIgbm8gRmlyZWJhc2UgU3RvcmFnZVxuICAgICAgY29uc3QgY2hhdEpzb25CbG9iID0gbmV3IEJsb2IoW0pTT04uc3RyaW5naWZ5KGNoYXREYXRhLCBudWxsLCAyKV0sIHtcbiAgICAgICAgdHlwZTogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICB9KTtcblxuICAgICAgY29uc3QgY2hhdEpzb25SZWYgPSByZWYoc3RvcmFnZSwgYHVzdWFyaW9zLyR7Y3VycmVudFVzZXJuYW1lfS9jb252ZXJzYXMvJHthY3R1YWxDaGF0SWR9L2NoYXQuanNvbmApO1xuICAgICAgYXdhaXQgdXBsb2FkQnl0ZXMoY2hhdEpzb25SZWYsIGNoYXRKc29uQmxvYik7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfinIUgRXN0YWRvIGRvcyBhbmV4b3Mgc2Fsdm8gbm8gRmlyZWJhc2UgU3RvcmFnZScpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk4EgRGFkb3Mgc2Fsdm9zOicsIHtcbiAgICAgICAgY2hhdElkOiBhY3R1YWxDaGF0SWQsXG4gICAgICAgIHRvdGFsTWVzc2FnZXM6IGNoYXREYXRhLm1lc3NhZ2VzLmxlbmd0aCxcbiAgICAgICAgbWVzc2FnZXNXaXRoQXR0YWNobWVudHM6IGNoYXREYXRhLm1lc3NhZ2VzLmZpbHRlcihtc2cgPT4gbXNnLmF0dGFjaG1lbnRzICYmIG1zZy5hdHRhY2htZW50cy5sZW5ndGggPiAwKS5sZW5ndGhcbiAgICAgIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJybyBhbyBzYWx2YXIgZXN0YWRvIGRvcyBhbmV4b3M6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGFsdGVybmFyIG8gZXN0YWRvIGF0aXZvIGRlIHVtIGFuZXhvXG4gIGNvbnN0IGhhbmRsZVRvZ2dsZUF0dGFjaG1lbnQgPSAoYXR0YWNobWVudElkOiBzdHJpbmcpID0+IHtcbiAgICBzZXRNZXNzYWdlcyhwcmV2TWVzc2FnZXMgPT4ge1xuICAgICAgY29uc3QgdXBkYXRlZE1lc3NhZ2VzID0gcHJldk1lc3NhZ2VzLm1hcChtZXNzYWdlID0+IHtcbiAgICAgICAgaWYgKG1lc3NhZ2UuYXR0YWNobWVudHMgJiYgbWVzc2FnZS5hdHRhY2htZW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgY29uc3QgdXBkYXRlZEF0dGFjaG1lbnRzID0gbWVzc2FnZS5hdHRhY2htZW50cy5tYXAoYXR0YWNobWVudCA9PiB7XG4gICAgICAgICAgICBpZiAoYXR0YWNobWVudC5pZCA9PT0gYXR0YWNobWVudElkKSB7XG4gICAgICAgICAgICAgIC8vIFNlIGlzQWN0aXZlIG7Do28gZXN0w6EgZGVmaW5pZG8sIGNvbnNpZGVyYXIgY29tbyB0cnVlIChhdGl2byBwb3IgcGFkcsOjbylcbiAgICAgICAgICAgICAgY29uc3QgY3VycmVudFN0YXRlID0gYXR0YWNobWVudC5pc0FjdGl2ZSAhPT0gZmFsc2U7XG4gICAgICAgICAgICAgIHJldHVybiB7IC4uLmF0dGFjaG1lbnQsIGlzQWN0aXZlOiAhY3VycmVudFN0YXRlIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gYXR0YWNobWVudDtcbiAgICAgICAgICB9KTtcbiAgICAgICAgICByZXR1cm4geyAuLi5tZXNzYWdlLCBhdHRhY2htZW50czogdXBkYXRlZEF0dGFjaG1lbnRzIH07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG1lc3NhZ2U7XG4gICAgICB9KTtcblxuICAgICAgLy8gU2FsdmFyIG8gZXN0YWRvIGF0dWFsaXphZG8gbm8gRmlyZWJhc2UgU3RvcmFnZVxuICAgICAgc2F2ZUF0dGFjaG1lbnRTdGF0ZXModXBkYXRlZE1lc3NhZ2VzKTtcblxuICAgICAgcmV0dXJuIHVwZGF0ZWRNZXNzYWdlcztcbiAgICB9KTtcbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGZpbHRyYXIgYW5leG9zIGF0aXZvcyBwYXJhIGVudmlvIMOgIElBXG4gIGNvbnN0IGdldEFjdGl2ZUF0dGFjaG1lbnRzID0gKGF0dGFjaG1lbnRzPzogaW1wb3J0KCdAL2xpYi90eXBlcy9jaGF0JykuQXR0YWNobWVudE1ldGFkYXRhW10pOiBpbXBvcnQoJ0AvbGliL3R5cGVzL2NoYXQnKS5BdHRhY2htZW50TWV0YWRhdGFbXSA9PiB7XG4gICAgaWYgKCFhdHRhY2htZW50cykgcmV0dXJuIFtdO1xuXG4gICAgLy8gUGFyYSBhbmV4b3Mgbm92b3MgKHNlbSBpc0FjdGl2ZSBkZWZpbmlkbyksIGluY2x1aXIgcG9yIHBhZHLDo29cbiAgICAvLyBQYXJhIGFuZXhvcyBleGlzdGVudGVzLCB2ZXJpZmljYXIgc2UgaXNBY3RpdmUgbsOjbyDDqSBmYWxzZVxuICAgIHJldHVybiBhdHRhY2htZW50cy5maWx0ZXIoYXR0YWNobWVudCA9PiB7XG4gICAgICAvLyBTZSBpc0FjdGl2ZSBuw6NvIGVzdMOhIGRlZmluaWRvIChhbmV4byBub3ZvKSwgaW5jbHVpclxuICAgICAgaWYgKGF0dGFjaG1lbnQuaXNBY3RpdmUgPT09IHVuZGVmaW5lZCkgcmV0dXJuIHRydWU7XG4gICAgICAvLyBTZSBpc0FjdGl2ZSBlc3TDoSBkZWZpbmlkbywgaW5jbHVpciBhcGVuYXMgc2UgbsOjbyBmb3IgZmFsc2VcbiAgICAgIHJldHVybiBhdHRhY2htZW50LmlzQWN0aXZlICE9PSBmYWxzZTtcbiAgICB9KTtcbiAgfTtcblxuXG5cbiAgLy8gRnVuw6fDo28gcGFyYSBvYnRlciBJRHMgZG9zIGFuZXhvcyBhdGl2b3NcbiAgY29uc3QgZ2V0QWN0aXZlQXR0YWNobWVudElkcyA9ICgpOiBzdHJpbmdbXSA9PiB7XG4gICAgY29uc3QgYWxsQXR0YWNobWVudHMgPSBnZXRBbGxDaGF0QXR0YWNobWVudHMoKTtcbiAgICByZXR1cm4gYWxsQXR0YWNobWVudHMuZmlsdGVyKGF0dCA9PiBhdHQuaXNBY3RpdmUgIT09IGZhbHNlKS5tYXAoYXR0ID0+IGF0dC5pZCk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sIGgtc2NyZWVuXCI+XG4gICAgICB7LyogVXBwZXJiYXIgKi99XG4gICAgICA8VXBwZXJiYXJcbiAgICAgICAgY3VycmVudENoYXQ9e2N1cnJlbnRDaGF0fVxuICAgICAgICBjaGF0TmFtZT17Y2hhdE5hbWV9XG4gICAgICAgIGFpTW9kZWw9e3NlbGVjdGVkTW9kZWx9XG4gICAgICAgIG9uRG93bmxvYWQ9e2hhbmRsZURvd25sb2FkTW9kYWx9XG4gICAgICAgIG9uQXR0YWNobWVudHM9e2hhbmRsZUF0dGFjaG1lbnRzTW9kYWx9XG4gICAgICAgIG9uU3RhdGlzdGljcz17aGFuZGxlU3RhdGlzdGljc01vZGFsfVxuICAgICAgICBpc0xvYWRpbmc9e2lzTG9hZGluZ31cbiAgICAgICAgYXR0YWNobWVudHNDb3VudD17Z2V0QWxsQ2hhdEF0dGFjaG1lbnRzKCkubGVuZ3RofVxuICAgICAgICBhaU1ldGFkYXRhPXt7XG4gICAgICAgICAgdXNlZENvVDogZmFsc2VcbiAgICAgICAgfX1cbiAgICAgIC8+XG5cbiAgICAgIHsvKiBDaGF0SW50ZXJmYWNlICovfVxuICAgICAgPGRpdiByZWY9e2NoYXRJbnRlcmZhY2VSZWZ9IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4taC0wXCIgc3R5bGU9e3sgaGVpZ2h0OiAnY2FsYygxMDB2aCAtIDIwMHB4KScgfX0+XG4gICAgICAgIDxDaGF0SW50ZXJmYWNlXG4gICAgICAgICAgbWVzc2FnZXM9e21lc3NhZ2VzfVxuICAgICAgICAgIGlzTG9hZGluZz17aXNMb2FkaW5nfVxuICAgICAgICAgIGlzTG9hZGluZ0NoYXQ9e2lzTG9hZGluZ0NoYXR9XG4gICAgICAgICAgaXNTdHJlYW1pbmc9e2lzU3RyZWFtaW5nfVxuICAgICAgICAgIHN0cmVhbWluZ01lc3NhZ2VJZD17c3RyZWFtaW5nTWVzc2FnZUlkIHx8IHVuZGVmaW5lZH1cbiAgICAgICAgICBvbkRlbGV0ZU1lc3NhZ2U9e2hhbmRsZURlbGV0ZU1lc3NhZ2V9XG4gICAgICAgICAgb25SZWdlbmVyYXRlTWVzc2FnZT17aGFuZGxlUmVnZW5lcmF0ZU1lc3NhZ2V9XG4gICAgICAgICAgb25FZGl0TWVzc2FnZT17aGFuZGxlRWRpdE1lc3NhZ2V9XG4gICAgICAgICAgb25FZGl0QW5kUmVnZW5lcmF0ZT17aGFuZGxlRWRpdEFuZFJlZ2VuZXJhdGV9XG4gICAgICAgICAgb25Db3B5TWVzc2FnZT17aGFuZGxlQ29weU1lc3NhZ2V9XG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIElucHV0QmFyICovfVxuICAgICAgPElucHV0QmFyXG4gICAgICAgIG1lc3NhZ2U9e21lc3NhZ2V9XG4gICAgICAgIHNldE1lc3NhZ2U9e3NldE1lc3NhZ2V9XG4gICAgICAgIG9uU2VuZE1lc3NhZ2U9e2hhbmRsZVNlbmRNZXNzYWdlfVxuICAgICAgICBpc0xvYWRpbmc9e2lzTG9hZGluZ31cbiAgICAgICAgc2VsZWN0ZWRNb2RlbD17c2VsZWN0ZWRNb2RlbH1cbiAgICAgICAgb25Nb2RlbENoYW5nZT17aGFuZGxlTW9kZWxDaGFuZ2V9XG4gICAgICAgIG9uU2Nyb2xsVG9Ub3A9e2hhbmRsZVNjcm9sbFRvVG9wfVxuICAgICAgICBvblNjcm9sbFRvQm90dG9tPXtoYW5kbGVTY3JvbGxUb0JvdHRvbX1cbiAgICAgICAgaXNTdHJlYW1pbmc9e2lzU3RyZWFtaW5nfVxuICAgICAgICBvbkNhbmNlbFN0cmVhbWluZz17aGFuZGxlQ2FuY2VsU3RyZWFtaW5nfVxuICAgICAgICBvbk9wZW5Nb2RlbE1vZGFsPXsoKSA9PiBzZXRJc01vZGVsTW9kYWxPcGVuKHRydWUpfVxuICAgICAgICB1c2VybmFtZT17Y3VycmVudFVzZXJuYW1lfVxuICAgICAgICBjaGF0SWQ9e2FjdHVhbENoYXRJZCB8fCB1bmRlZmluZWR9XG4gICAgICAgIGFjdGl2ZUF0dGFjaG1lbnRzQ291bnQ9e2dldEFjdGl2ZUF0dGFjaG1lbnRJZHMoKS5sZW5ndGh9XG4gICAgICAvPlxuXG4gICAgICB7LyogRG93bmxvYWQgTW9kYWwgKi99XG4gICAgICA8RG93bmxvYWRNb2RhbFxuICAgICAgICBpc09wZW49e2lzRG93bmxvYWRNb2RhbE9wZW59XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldElzRG93bmxvYWRNb2RhbE9wZW4oZmFsc2UpfVxuICAgICAgICBtZXNzYWdlcz17Y29udmVydFRvQ2hhdE1lc3NhZ2VzKG1lc3NhZ2VzKX1cbiAgICAgICAgY2hhdE5hbWU9e2NoYXROYW1lfVxuICAgICAgLz5cblxuICAgICAgey8qIE1vZGVsIFNlbGVjdGlvbiBNb2RhbCAqL31cbiAgICAgIDxNb2RlbFNlbGVjdGlvbk1vZGFsXG4gICAgICAgIGlzT3Blbj17aXNNb2RlbE1vZGFsT3Blbn1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNNb2RlbE1vZGFsT3BlbihmYWxzZSl9XG4gICAgICAgIGN1cnJlbnRNb2RlbD17c2VsZWN0ZWRNb2RlbH1cbiAgICAgICAgb25Nb2RlbFNlbGVjdD17aGFuZGxlTW9kZWxDaGFuZ2V9XG4gICAgICAvPlxuXG4gICAgICB7LyogQXR0YWNobWVudHMgTW9kYWwgKi99XG4gICAgICA8QXR0YWNobWVudHNNb2RhbFxuICAgICAgICBpc09wZW49e2lzQXR0YWNobWVudHNNb2RhbE9wZW59XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldElzQXR0YWNobWVudHNNb2RhbE9wZW4oZmFsc2UpfVxuICAgICAgICBhdHRhY2htZW50cz17Z2V0QWxsQ2hhdEF0dGFjaG1lbnRzKCl9XG4gICAgICAgIGFjdGl2ZUF0dGFjaG1lbnRzPXtnZXRBY3RpdmVBdHRhY2htZW50SWRzKCl9XG4gICAgICAgIG9uVG9nZ2xlQXR0YWNobWVudD17aGFuZGxlVG9nZ2xlQXR0YWNobWVudH1cbiAgICAgIC8+XG5cbiAgICAgIHsvKiBTdGF0aXN0aWNzIE1vZGFsICovfVxuICAgICAgPFN0YXRpc3RpY3NNb2RhbFxuICAgICAgICBpc09wZW49e2lzU3RhdGlzdGljc01vZGFsT3Blbn1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNTdGF0aXN0aWNzTW9kYWxPcGVuKGZhbHNlKX1cbiAgICAgICAgbWVzc2FnZXM9e2NvbnZlcnRUb0NoYXRNZXNzYWdlcyhtZXNzYWdlcyl9XG4gICAgICAgIGNoYXROYW1lPXtjaGF0TmFtZX1cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJkb2MiLCJzZXREb2MiLCJjb2xsZWN0aW9uIiwiZ2V0RG9jIiwidXBkYXRlRG9jIiwicXVlcnkiLCJ3aGVyZSIsImdldERvY3MiLCJyZWYiLCJ1cGxvYWRCeXRlcyIsImRiIiwic3RvcmFnZSIsInVzZUF1dGgiLCJVcHBlcmJhciIsIkNoYXRJbnRlcmZhY2UiLCJJbnB1dEJhciIsIkRvd25sb2FkTW9kYWwiLCJNb2RlbFNlbGVjdGlvbk1vZGFsIiwiQXR0YWNobWVudHNNb2RhbCIsIlN0YXRpc3RpY3NNb2RhbCIsImFpU2VydmljZSIsIkNoYXRBcmVhIiwiY3VycmVudENoYXQiLCJvbkNoYXRDcmVhdGVkIiwib25VcGRhdGVPcGVuUm91dGVyQmFsYW5jZSIsInVzZXIiLCJtZXNzYWdlIiwic2V0TWVzc2FnZSIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsIm1lc3NhZ2VzIiwic2V0TWVzc2FnZXMiLCJzZWxlY3RlZE1vZGVsIiwic2V0U2VsZWN0ZWRNb2RlbCIsImFjdHVhbENoYXRJZCIsInNldEFjdHVhbENoYXRJZCIsImlzRG93bmxvYWRNb2RhbE9wZW4iLCJzZXRJc0Rvd25sb2FkTW9kYWxPcGVuIiwiaXNNb2RlbE1vZGFsT3BlbiIsInNldElzTW9kZWxNb2RhbE9wZW4iLCJpc0F0dGFjaG1lbnRzTW9kYWxPcGVuIiwic2V0SXNBdHRhY2htZW50c01vZGFsT3BlbiIsImlzU3RhdGlzdGljc01vZGFsT3BlbiIsInNldElzU3RhdGlzdGljc01vZGFsT3BlbiIsImlzU3RyZWFtaW5nIiwic2V0SXNTdHJlYW1pbmciLCJzdHJlYW1pbmdNZXNzYWdlSWQiLCJzZXRTdHJlYW1pbmdNZXNzYWdlSWQiLCJjaGF0TmFtZSIsInNldENoYXROYW1lIiwiaXNMb2FkaW5nQ2hhdCIsInNldElzTG9hZGluZ0NoYXQiLCJjdXJyZW50VXNlcm5hbWUiLCJzZXRDdXJyZW50VXNlcm5hbWUiLCJ1bmRlZmluZWQiLCJjaGF0SW50ZXJmYWNlUmVmIiwibG9hZFVzZXJuYW1lIiwiZW1haWwiLCJ1c2VybmFtZSIsImdldFVzZXJuYW1lRnJvbUZpcmVzdG9yZSIsInVzdWFyaW9zUmVmIiwicSIsInF1ZXJ5U25hcHNob3QiLCJlbXB0eSIsInVzZXJEb2MiLCJkb2NzIiwidXNlckRhdGEiLCJkYXRhIiwic3BsaXQiLCJlcnJvciIsImNvbnNvbGUiLCJzYXZlTGFzdFVzZWRNb2RlbEZvckNoYXQiLCJtb2RlbElkIiwiY2hhdElkIiwiY2hhdFJlZiIsImxhc3RVc2VkTW9kZWwiLCJsYXN0TW9kZWxVcGRhdGVBdCIsIkRhdGUiLCJub3ciLCJsb2ciLCJsb2FkTGFzdFVzZWRNb2RlbEZvckNoYXQiLCJjaGF0RG9jIiwiZXhpc3RzIiwiaXNWYWxpZCIsImlzVmFsaWRNb2RlbCIsIm1vZGVsIiwibG9hZERlZmF1bHRNb2RlbEZyb21BY3RpdmVFbmRwb2ludCIsInVzZXJSZWYiLCJlbmRwb2ludHMiLCJhY3RpdmVFbmRwb2ludCIsIk9iamVjdCIsInZhbHVlcyIsImZpbmQiLCJlbmRwb2ludCIsImF0aXZvIiwibW9kZWxvUGFkcmFvIiwiaW52YWxpZE1vZGVscyIsImluY2x1ZGVzIiwiY2xlYW51cEludmFsaWRNb2RlbHNGcm9tQWxsQ2hhdHMiLCJjaGF0c1JlZiIsImNoYXRzU25hcHNob3QiLCJ1cGRhdGVQcm9taXNlcyIsImlkIiwicHVzaCIsImxlbmd0aCIsIlByb21pc2UiLCJhbGwiLCJsb2FkR2xvYmFsTGFzdFVzZWRNb2RlbCIsImhhbmRsZU1vZGVsQ2hhbmdlIiwiY3JlYXRlQXV0b0NoYXQiLCJmaXJzdE1lc3NhZ2UiLCJ0aW1lc3RhbXAiLCJyYW5kb20iLCJNYXRoIiwidG9TdHJpbmciLCJzdWJzdHJpbmciLCJ0b0lTT1N0cmluZyIsImZpbmFsQ2hhdE5hbWUiLCJ0cmltIiwid29yZHMiLCJzbGljZSIsIm1pbiIsImpvaW4iLCJmaXJlc3RvcmVEYXRhIiwiY29udGV4dCIsImNyZWF0ZWRBdCIsImZvbGRlcklkIiwiZnJlcXVlbmN5UGVuYWx0eSIsImlzRml4ZWQiLCJsYXN0VXBkYXRlZEF0IiwibGF0ZXhJbnN0cnVjdGlvbnMiLCJtYXhUb2tlbnMiLCJuYW1lIiwicGFzc3dvcmQiLCJyZXBldGl0aW9uUGVuYWx0eSIsInNlc3Npb25UaW1lIiwibGFzdFNlc3Npb25TdGFydCIsImxhc3RVcGRhdGVkIiwidG90YWxUaW1lIiwic3lzdGVtUHJvbXB0IiwidGVtcGVyYXR1cmUiLCJ1bHRpbWFNZW5zYWdlbSIsInVsdGltYU1lbnNhZ2VtRW0iLCJ1cGRhdGVkQXQiLCJjaGF0SnNvbkRhdGEiLCJjaGF0SnNvbkJsb2IiLCJCbG9iIiwiSlNPTiIsInN0cmluZ2lmeSIsInR5cGUiLCJzdG9yYWdlUmVmIiwiaGFuZGxlU2VuZE1lc3NhZ2UiLCJhdHRhY2htZW50cyIsIndlYlNlYXJjaEVuYWJsZWQiLCJoaXN0b3JpY2FsQXR0YWNobWVudHMiLCJnZXRBbGxDaGF0QXR0YWNobWVudHMiLCJmaWx0ZXIiLCJhdHQiLCJpc0FjdGl2ZSIsImFsbEF0dGFjaG1lbnRzVG9TZW5kIiwidW5pcXVlQXR0YWNobWVudHMiLCJhdHRhY2htZW50IiwiaW5kZXgiLCJzZWxmIiwiZmluZEluZGV4IiwiYSIsImZvckVhY2giLCJmaWxlbmFtZSIsIm1lc3NhZ2VzV2l0aEF0dGFjaG1lbnRzIiwibXNnIiwic2VuZGVyIiwiYXR0YWNobWVudHNDb3VudCIsInVzZXJNZXNzYWdlIiwiZ2VuZXJhdGVNZXNzYWdlSWQiLCJjb250ZW50IiwiY2hhdElkVG9Vc2UiLCJtZXNzYWdlRm9yQ2hhdCIsImxvYWRDaGF0TmFtZSIsInByZXYiLCJjdXJyZW50TWVzc2FnZSIsImFpTWVzc2FnZUlkIiwic2VuZE1lc3NhZ2VTYWZlIiwiY2h1bmsiLCJleGlzdGluZ01lc3NhZ2VJbmRleCIsIm1hcCIsImFpTWVzc2FnZSIsImhhc1dlYlNlYXJjaCIsImZ1bGxSZXNwb25zZSIsInNldFRpbWVvdXQiLCJoYW5kbGVDYW5jZWxTdHJlYW1pbmciLCJjYW5jZWxSZXF1ZXN0IiwiY2hhdERhdGEiLCJsb2FkQ2hhdE1lc3NhZ2VzIiwiY2hhdE1lc3NhZ2VzIiwiY29udmVydGVkTWVzc2FnZXMiLCJjb252ZXJ0RnJvbUFJRm9ybWF0IiwiaGFuZGxlRGVsZXRlTWVzc2FnZSIsIm1lc3NhZ2VJZCIsInN1Y2Nlc3MiLCJkZWxldGVNZXNzYWdlIiwiaGFuZGxlUmVnZW5lcmF0ZU1lc3NhZ2UiLCJmcmVzaE1lc3NhZ2VzIiwiY29udmVydGVkRnJlc2hNZXNzYWdlcyIsIm1lc3NhZ2VJbmRleCIsIm1lc3NhZ2VUb1JlZ2VuZXJhdGUiLCJtZXNzYWdlc0JlZm9yZVJlZ2VuZXJhdGlvbiIsImkiLCJtc2dUb0RlbGV0ZSIsImlzUmVnZW5lcmF0aW9uIiwiaGFuZGxlRWRpdE1lc3NhZ2UiLCJuZXdDb250ZW50IiwibmV3Q29udGVudExlbmd0aCIsIm5ld0NvbnRlbnRQcmV2aWV3IiwidXBkYXRlTWVzc2FnZSIsImhhbmRsZUVkaXRBbmRSZWdlbmVyYXRlIiwiZWRpdFN1Y2Nlc3MiLCJyZXNvbHZlIiwiaGFzTWVzc2FnZXNBZnRlciIsImhhbmRsZUNvcHlNZXNzYWdlIiwibmF2aWdhdG9yIiwiY2xpcGJvYXJkIiwid3JpdGVUZXh0IiwidGhlbiIsImhhbmRsZVNjcm9sbFRvVG9wIiwic2Nyb2xsQ29udGFpbmVyIiwiY3VycmVudCIsInF1ZXJ5U2VsZWN0b3IiLCJzY3JvbGxUbyIsInRvcCIsImJlaGF2aW9yIiwiaGFuZGxlU2Nyb2xsVG9Cb3R0b20iLCJzY3JvbGxIZWlnaHQiLCJjb252ZXJ0VG9DaGF0TWVzc2FnZXMiLCJyb2xlIiwiZ2V0VGltZSIsImlzRmF2b3JpdGUiLCJoYW5kbGVEb3dubG9hZE1vZGFsIiwiaGFuZGxlQXR0YWNobWVudHNNb2RhbCIsImhhbmRsZVN0YXRpc3RpY3NNb2RhbCIsImFsbEF0dGFjaG1lbnRzIiwic2F2ZUF0dGFjaG1lbnRTdGF0ZXMiLCJ1cGRhdGVkTWVzc2FnZXMiLCJjaGF0SnNvblJlZiIsInRvdGFsTWVzc2FnZXMiLCJoYW5kbGVUb2dnbGVBdHRhY2htZW50IiwiYXR0YWNobWVudElkIiwicHJldk1lc3NhZ2VzIiwidXBkYXRlZEF0dGFjaG1lbnRzIiwiY3VycmVudFN0YXRlIiwiZ2V0QWN0aXZlQXR0YWNobWVudHMiLCJnZXRBY3RpdmVBdHRhY2htZW50SWRzIiwiZGl2IiwiY2xhc3NOYW1lIiwiYWlNb2RlbCIsIm9uRG93bmxvYWQiLCJvbkF0dGFjaG1lbnRzIiwib25TdGF0aXN0aWNzIiwiYWlNZXRhZGF0YSIsInVzZWRDb1QiLCJzdHlsZSIsImhlaWdodCIsIm9uRGVsZXRlTWVzc2FnZSIsIm9uUmVnZW5lcmF0ZU1lc3NhZ2UiLCJvbkVkaXRNZXNzYWdlIiwib25FZGl0QW5kUmVnZW5lcmF0ZSIsIm9uQ29weU1lc3NhZ2UiLCJvblNlbmRNZXNzYWdlIiwib25Nb2RlbENoYW5nZSIsIm9uU2Nyb2xsVG9Ub3AiLCJvblNjcm9sbFRvQm90dG9tIiwib25DYW5jZWxTdHJlYW1pbmciLCJvbk9wZW5Nb2RlbE1vZGFsIiwiYWN0aXZlQXR0YWNobWVudHNDb3VudCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJjdXJyZW50TW9kZWwiLCJvbk1vZGVsU2VsZWN0IiwiYWN0aXZlQXR0YWNobWVudHMiLCJvblRvZ2dsZUF0dGFjaG1lbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});