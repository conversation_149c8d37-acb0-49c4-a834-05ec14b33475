"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _StatisticsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./StatisticsModal */ \"(app-pages-browser)/./src/components/dashboard/StatisticsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* harmony import */ var _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated, onUpdateOpenRouterBalance } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStatisticsModalOpen, setIsStatisticsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado para um chat específico\n    const saveLastUsedModelForChat = async (modelId, chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            // Atualizar o lastUsedModel no documento do chat\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                lastUsedModel: modelId,\n                lastModelUpdateAt: Date.now()\n            });\n            console.log(\"Last used model saved for chat:\", {\n                chatId,\n                modelId\n            });\n        } catch (error) {\n            console.error(\"Error saving last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado de um chat específico\n    const loadLastUsedModelForChat = async (chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(chatRef);\n            if (chatDoc.exists()) {\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded last used model for chat:\", {\n                        chatId,\n                        model: data.lastUsedModel\n                    });\n                } else {\n                    // Se o chat não tem modelo salvo, usar o modelo padrão\n                    setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n                    console.log(\"No saved model for chat, using default:\", chatId);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado globalmente (fallback)\n    const loadGlobalLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded global last used model:\", data.lastUsedModel);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading global last used model:\", error);\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        // Salvar no chat específico se houver um chat ativo\n        if (actualChatId) {\n            saveLastUsedModelForChat(modelId, actualChatId);\n        }\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            // Definir o nome do chat imediatamente após criação\n            setChatName(finalChatName);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments, webSearchEnabled)=>{\n        // Obter anexos históricos ativos\n        const historicalAttachments = getAllChatAttachments().filter((att)=>att.isActive !== false);\n        // Combinar anexos novos com anexos históricos ativos\n        const allAttachmentsToSend = [\n            ...attachments || [],\n            ...historicalAttachments // Anexos históricos ativos\n        ];\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachmentsToSend.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        console.log(\"\\uD83D\\uDE80 RESULTADO FINAL - ANEXOS QUE SER\\xc3O ENVIADOS PARA A IA:\", uniqueAttachments.length);\n        uniqueAttachments.forEach((att)=>{\n            console.log(\"\\uD83D\\uDE80 Enviando para IA: \".concat(att.filename, \" (ID: \").concat(att.id, \", isActive: \").concat(att.isActive, \")\"));\n        });\n        if (uniqueAttachments.length === 0) {\n            console.log(\"❌ NENHUM ANEXO SER\\xc1 ENVIADO PARA A IA\");\n        }\n        // Debug: verificar mensagens atuais no estado\n        console.log(\"=== DEBUG: MENSAGENS ATUAIS NO ESTADO ===\");\n        console.log(\"Total de mensagens no estado:\", messages.length);\n        const messagesWithAttachments = messages.filter((msg)=>msg.attachments && msg.attachments.length > 0);\n        console.log(\"Mensagens com anexos no estado:\", messagesWithAttachments.length);\n        messagesWithAttachments.forEach((msg, index)=>{\n            console.log(\"Mensagem \".concat(index + 1, \" com anexos:\"), {\n                id: msg.id,\n                sender: msg.sender,\n                attachmentsCount: msg.attachments && msg.attachments.length || 0,\n                attachments: msg.attachments\n            });\n        });\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            console.log(\"=== DEBUG: CONDI\\xc7\\xc3O DE RETORNO ===\");\n            console.log(\"Mensagem vazia:\", !message.trim());\n            console.log(\"Sem anexos:\", !attachments || attachments.length === 0);\n            console.log(\"Loading:\", isLoading);\n            console.log(\"Streaming:\", isStreaming);\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        let attachmentsToSend = uniqueAttachments;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // O nome já foi definido na função createAutoChat, mas vamos garantir carregando também\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n                // Se há anexos novos de um chat temporário, movê-los para o chat real\n                if (attachments && attachments.length > 0) {\n                    const username = await getUsernameFromFirestore();\n                    // Identificar anexos temporários (que têm temp_chat_ no storagePath)\n                    const tempAttachments = attachments.filter((att)=>{\n                        var _att_storagePath;\n                        return (_att_storagePath = att.storagePath) === null || _att_storagePath === void 0 ? void 0 : _att_storagePath.includes(\"temp_chat_\");\n                    });\n                    if (tempAttachments.length > 0) {\n                        console.log(\"\\uD83D\\uDD04 Movendo \".concat(tempAttachments.length, \" anexos tempor\\xe1rios para o chat real\"));\n                        try {\n                            var _tempAttachments__storagePath;\n                            // Extrair o tempChatId do primeiro anexo temporário\n                            const tempChatId = ((_tempAttachments__storagePath = tempAttachments[0].storagePath) === null || _tempAttachments__storagePath === void 0 ? void 0 : _tempAttachments__storagePath.split(\"/\")[2]) || \"\";\n                            // Mover anexos para o chat real\n                            const movedAttachments = await _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_14__[\"default\"].moveAttachmentsToRealChat(tempAttachments, username, tempChatId, chatIdToUse);\n                            // Atualizar a lista de anexos com os anexos movidos\n                            attachmentsToSend = [\n                                ...uniqueAttachments.filter((att)=>{\n                                    var _att_storagePath;\n                                    return !((_att_storagePath = att.storagePath) === null || _att_storagePath === void 0 ? void 0 : _att_storagePath.includes(\"temp_chat_\"));\n                                }),\n                                ...movedAttachments\n                            ];\n                            console.log(\"✅ Anexos movidos com sucesso para o chat real\");\n                        } catch (error) {\n                            console.error(\"❌ Erro ao mover anexos para o chat real:\", error);\n                        // Em caso de erro, usar os anexos originais\n                        }\n                    }\n                }\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA (incluindo anexos históricos ativos + anexos novos)\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: uniqueAttachments,\n            webSearchEnabled: webSearchEnabled\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString(),\n                        hasWebSearch: webSearchEnabled\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            // Salvar o modelo usado no chat específico\n            if (chatIdToUse) {\n                saveLastUsedModelForChat(selectedModel, chatIdToUse);\n            }\n            // Atualizar saldo do OpenRouter após a resposta\n            if (onUpdateOpenRouterBalance) {\n                onUpdateOpenRouterBalance();\n            }\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                const chatName = chatData.name || \"Conversa sem nome\";\n                setChatName(chatName);\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        console.log(\"\\uD83D\\uDCE5 Carregando mensagens do chat:\", {\n            chatId,\n            timestamp: new Date().toISOString()\n        });\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, chatId);\n            // Debug: verificar mensagens carregadas\n            console.log(\"=== \\uD83D\\uDCE5 MENSAGENS CARREGADAS DO FIREBASE STORAGE ===\");\n            console.log(\"Total de mensagens:\", chatMessages.length);\n            chatMessages.forEach((msg, index)=>{\n                console.log(\"Mensagem \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    role: msg.role,\n                    contentPreview: msg.content.substring(0, 50) + \"...\",\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: msg.attachments && msg.attachments.length || 0,\n                    timestamp: msg.timestamp\n                });\n            });\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(chatMessages);\n            // Debug: verificar mensagens convertidas\n            console.log(\"=== \\uD83D\\uDD04 MENSAGENS CONVERTIDAS PARA ESTADO LOCAL ===\");\n            console.log(\"Total de mensagens convertidas:\", convertedMessages.length);\n            convertedMessages.forEach((msg, index)=>{\n                console.log(\"Mensagem convertida \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    sender: msg.sender,\n                    contentPreview: msg.content.substring(0, 50) + \"...\",\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: msg.attachments && msg.attachments.length || 0,\n                    timestamp: msg.timestamp\n                });\n            });\n            setMessages(convertedMessages);\n            console.log(\"✅ Estado das mensagens atualizado com sucesso\");\n        } catch (error) {\n            console.error(\"❌ Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado globalmente quando o componente montar (apenas se não há chat)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && !currentChat) {\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        user,\n        currentChat\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n            // Carregar o modelo específico do chat\n            loadLastUsedModelForChat(currentChat);\n        } else if (!currentChat && actualChatId) {\n            // Só resetar se realmente não há chat e havia um chat antes\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n            // Carregar modelo global quando não há chat específico\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // ✅ CORREÇÃO: Recarregar mensagens do Firebase Storage para garantir estado atualizado\n        console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o para garantir estado atualizado...\");\n        try {\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                setIsLoading(false);\n                setIsStreaming(false);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // Deletar apenas as mensagens POSTERIORES do Firebase Storage (não a mensagem atual)\n            console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n            for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                const msgToDelete = convertedFreshMessages[i];\n                console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração\n                if (onUpdateOpenRouterBalance) {\n                    onUpdateOpenRouterBalance();\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return false;\n        console.log(\"✏️ Iniciando edi\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            console.log(\"\\uD83D\\uDCE4 Enviando atualiza\\xe7\\xe3o para o servidor...\");\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                console.error(\"❌ Falha ao atualizar mensagem no servidor\");\n                loadChatMessages(actualChatId);\n                return false;\n            } else {\n                console.log(\"✅ Mensagem editada e salva com sucesso no Firebase Storage:\", {\n                    messageId,\n                    timestamp: new Date().toISOString()\n                });\n                return true;\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            console.error(\"❌ Erro ao atualizar mensagem:\", error);\n            loadChatMessages(actualChatId);\n            return false;\n        }\n    };\n    const handleEditAndRegenerate = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        console.log(\"✏️\\uD83D\\uDD04 Iniciando edi\\xe7\\xe3o e regenera\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        try {\n            // 1. Primeiro, salvar a edição\n            const editSuccess = await handleEditMessage(messageId, newContent);\n            if (!editSuccess) {\n                console.error(\"❌ Falha ao editar mensagem, cancelando regenera\\xe7\\xe3o\");\n                return;\n            }\n            console.log(\"✅ Mensagem editada com sucesso, iniciando regenera\\xe7\\xe3o...\");\n            // 2. Aguardar um pouco para garantir que a edição foi salva\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            // 3. Recarregar mensagens do Firebase Storage para garantir estado atualizado\n            console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o...\");\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // 4. Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // 5. Verificar se há mensagens após esta mensagem\n            const hasMessagesAfter = messageIndex < convertedFreshMessages.length - 1;\n            console.log(\"\\uD83D\\uDCCA Mensagens ap\\xf3s esta: \".concat(convertedFreshMessages.length - messageIndex - 1));\n            // 6. Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // 7. Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // 8. Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // 9. Deletar apenas as mensagens POSTERIORES do Firebase Storage (se houver)\n            if (hasMessagesAfter) {\n                console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n                for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                    const msgToDelete = convertedFreshMessages[i];\n                    console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                    await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n                }\n            }\n            // 10. Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração\n                if (onUpdateOpenRouterBalance) {\n                    onUpdateOpenRouterBalance();\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao editar e regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: scrollContainer.scrollHeight,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = ()=>{\n        setIsAttachmentsModalOpen(true);\n    };\n    // Função para abrir o modal de estatísticas\n    const handleStatisticsModal = ()=>{\n        setIsStatisticsModalOpen(true);\n    };\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para salvar o estado dos anexos no Firebase Storage\n    const saveAttachmentStates = async (updatedMessages)=>{\n        if (!currentUsername || !actualChatId) return;\n        try {\n            // Preparar dados do chat para salvar\n            const chatData = {\n                id: actualChatId,\n                name: chatName || \"Chat\",\n                messages: updatedMessages.map((msg)=>({\n                        id: msg.id,\n                        content: msg.content,\n                        role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                        timestamp: msg.timestamp,\n                        isFavorite: msg.isFavorite,\n                        attachments: msg.attachments\n                    })),\n                createdAt: new Date().toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            // Salvar no Firebase Storage\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(currentUsername, \"/conversas/\").concat(actualChatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(chatJsonRef, chatJsonBlob);\n            console.log(\"✅ Estado dos anexos salvo no Firebase Storage\");\n            console.log(\"\\uD83D\\uDCC1 Dados salvos:\", {\n                chatId: actualChatId,\n                totalMessages: chatData.messages.length,\n                messagesWithAttachments: chatData.messages.filter((msg)=>msg.attachments && msg.attachments.length > 0).length\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao salvar estado dos anexos:\", error);\n        }\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setMessages((prevMessages)=>{\n            const updatedMessages = prevMessages.map((message)=>{\n                if (message.attachments && message.attachments.length > 0) {\n                    const updatedAttachments = message.attachments.map((attachment)=>{\n                        if (attachment.id === attachmentId) {\n                            // Se isActive não está definido, considerar como true (ativo por padrão)\n                            const currentState = attachment.isActive !== false;\n                            return {\n                                ...attachment,\n                                isActive: !currentState\n                            };\n                        }\n                        return attachment;\n                    });\n                    return {\n                        ...message,\n                        attachments: updatedAttachments\n                    };\n                }\n                return message;\n            });\n            // Salvar o estado atualizado no Firebase Storage\n            saveAttachmentStates(updatedMessages);\n            return updatedMessages;\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        // Para anexos novos (sem isActive definido), incluir por padrão\n        // Para anexos existentes, verificar se isActive não é false\n        return attachments.filter((attachment)=>{\n            // Se isActive não está definido (anexo novo), incluir\n            if (attachment.isActive === undefined) return true;\n            // Se isActive está definido, incluir apenas se não for false\n            return attachment.isActive !== false;\n        });\n    };\n    // Função para obter IDs dos anexos ativos\n    const getActiveAttachmentIds = ()=>{\n        const allAttachments = getAllChatAttachments();\n        return allAttachments.filter((att)=>att.isActive !== false).map((att)=>att.id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                onDownload: handleDownloadModal,\n                onAttachments: handleAttachmentsModal,\n                onStatistics: handleStatisticsModal,\n                isLoading: isLoading,\n                attachmentsCount: getAllChatAttachments().length,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1095,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    isStreaming: isStreaming,\n                    streamingMessageId: streamingMessageId || undefined,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onEditAndRegenerate: handleEditAndRegenerate,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1111,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined,\n                activeAttachmentsCount: getActiveAttachmentIds().length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: getActiveAttachmentIds(),\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatisticsModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: isStatisticsModalOpen,\n                onClose: ()=>setIsStatisticsModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1169,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 1093,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"Hf7a+h7SnkMddq+yigBa/N2m+7g=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});