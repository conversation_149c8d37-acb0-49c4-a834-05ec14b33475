"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/attachmentService.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/attachmentService.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachmentService: function() { return /* binding */ attachmentService; }\n/* harmony export */ });\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\nclass AttachmentService {\n    /**\n   * Valida se o arquivo é suportado\n   */ validateFile(file) {\n        // Verificar tamanho\n        if (file.size > this.MAX_FILE_SIZE) {\n            return {\n                isValid: false,\n                error: \"Arquivo muito grande. M\\xe1ximo permitido: 10MB\"\n            };\n        }\n        // Verificar tipo\n        const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n        const isPdf = file.type === this.SUPPORTED_PDF_TYPE;\n        if (!isImage && !isPdf) {\n            return {\n                isValid: false,\n                error: \"Tipo de arquivo n\\xe3o suportado. Use PNG, JPEG, WebP ou PDF\"\n            };\n        }\n        return {\n            isValid: true\n        };\n    }\n    /**\n   * Converte arquivo para base64\n   */ async fileToBase64(file) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>{\n                const result = reader.result;\n                // Remover o prefixo data:type;base64,\n                const base64 = result.split(\",\")[1];\n                resolve(base64);\n            };\n            reader.onerror = reject;\n            reader.readAsDataURL(file);\n        });\n    }\n    /**\n   * Gera ID único para anexo\n   */ generateAttachmentId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Faz upload do arquivo para Firebase Storage\n   */ async uploadAttachment(params) {\n        const { file, username, chatId } = params;\n        console.log(\"=== DEBUG: ATTACHMENT SERVICE - UPLOAD INICIADO ===\");\n        console.log(\"Arquivo:\", file.name);\n        console.log(\"Username:\", username);\n        console.log(\"ChatId:\", chatId);\n        // Validar arquivo\n        const validation = this.validateFile(file);\n        console.log(\"=== DEBUG: VALIDA\\xc7\\xc3O DO ARQUIVO ===\");\n        console.log(\"Valida\\xe7\\xe3o:\", validation);\n        if (!validation.isValid) {\n            console.error(\"=== DEBUG: ARQUIVO INV\\xc1LIDO ===\");\n            console.error(\"Erro:\", validation.error);\n            throw new Error(validation.error);\n        }\n        try {\n            // Gerar ID único para o anexo\n            const attachmentId = this.generateAttachmentId();\n            console.log(\"=== DEBUG: ID DO ANEXO GERADO ===\");\n            console.log(\"AttachmentId:\", attachmentId);\n            // Determinar tipo\n            const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n            const type = isImage ? \"image\" : \"pdf\";\n            console.log(\"=== DEBUG: TIPO DO ARQUIVO ===\");\n            console.log(\"Tipo determinado:\", type);\n            console.log(\"\\xc9 imagem:\", isImage);\n            // Criar caminho no Storage\n            const storagePath = \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/anexos/\").concat(attachmentId, \"_\").concat(file.name);\n            console.log(\"=== DEBUG: CAMINHO NO STORAGE ===\");\n            console.log(\"Storage path:\", storagePath);\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.storage, storagePath);\n            console.log(\"=== DEBUG: REFER\\xcaNCIA DO STORAGE CRIADA ===\");\n            // Upload do arquivo\n            console.log(\"=== DEBUG: INICIANDO UPLOAD PARA FIREBASE STORAGE ===\");\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.uploadBytes)(storageRef, file);\n            console.log(\"=== DEBUG: UPLOAD PARA FIREBASE STORAGE CONCLU\\xcdDO ===\");\n            // Obter URL de download\n            console.log(\"=== DEBUG: OBTENDO URL DE DOWNLOAD ===\");\n            const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.getDownloadURL)(storageRef);\n            console.log(\"=== DEBUG: URL DE DOWNLOAD OBTIDA ===\");\n            console.log(\"Download URL:\", downloadURL);\n            // Preparar metadados\n            const metadata = {\n                id: attachmentId,\n                type,\n                filename: file.name,\n                url: downloadURL,\n                size: file.size,\n                uploadedAt: Date.now(),\n                storagePath,\n                isActive: true // Por padrão, anexos são ativos\n            };\n            console.log(\"=== DEBUG: METADADOS PREPARADOS ===\");\n            console.log(\"Metadata:\", metadata);\n            // Para PDFs, também converter para base64 para envio ao OpenRouter\n            let base64Data;\n            if (type === \"pdf\") {\n                console.log(\"=== DEBUG: CONVERTENDO PDF PARA BASE64 ===\");\n                base64Data = await this.fileToBase64(file);\n                metadata.base64Data = base64Data;\n                console.log(\"=== DEBUG: PDF CONVERTIDO PARA BASE64 ===\");\n                console.log(\"Base64 length:\", base64Data === null || base64Data === void 0 ? void 0 : base64Data.length);\n            }\n            const result = {\n                metadata,\n                base64Data\n            };\n            console.log(\"=== DEBUG: UPLOAD ATTACHMENT CONCLU\\xcdDO COM SUCESSO ===\");\n            console.log(\"Resultado:\", result);\n            return result;\n        } catch (error) {\n            console.error(\"=== DEBUG: ERRO NO UPLOAD ATTACHMENT ===\");\n            console.error(\"Erro:\", error);\n            throw new Error(\"Falha no upload do arquivo. Tente novamente.\");\n        }\n    }\n    /**\n   * Processa múltiplos arquivos\n   */ async uploadMultipleAttachments(files, username, chatId) {\n        const results = [];\n        for (const file of files){\n            try {\n                const result = await this.uploadAttachment({\n                    file,\n                    username,\n                    chatId\n                });\n                results.push(result);\n            } catch (error) {\n                console.error(\"Erro ao processar arquivo \".concat(file.name, \":\"), error);\n            // Continuar com os outros arquivos\n            }\n        }\n        return results;\n    }\n    /**\n   * Prepara anexos para envio ao OpenRouter\n   */ prepareAttachmentsForOpenRouter(attachments) {\n        const openRouterContent = [];\n        for (const attachment of attachments){\n            if (attachment.type === \"image\") {\n                // Para imagens, usar URL\n                openRouterContent.push({\n                    type: \"image_url\",\n                    image_url: {\n                        url: attachment.url\n                    }\n                });\n            } else if (attachment.type === \"pdf\" && attachment.base64Data) {\n                // Para PDFs, usar base64 com formato file\n                const dataUrl = \"data:application/pdf;base64,\".concat(attachment.base64Data);\n                openRouterContent.push({\n                    type: \"file\",\n                    file: {\n                        filename: attachment.filename,\n                        file_data: dataUrl\n                    }\n                });\n            }\n        }\n        return openRouterContent;\n    }\n    /**\n   * Prepara plugins para PDFs (engine mistral-ocr)\n   */ preparePDFPlugins(attachments) {\n        const hasPDF = attachments.some((att)=>att.type === \"pdf\");\n        if (!hasPDF) {\n            return [];\n        }\n        return [\n            {\n                id: \"file-parser\",\n                pdf: {\n                    engine: \"mistral-ocr\"\n                }\n            }\n        ];\n    }\n    /**\n   * Limpa anexos temporários (se necessário)\n   */ async cleanupTemporaryAttachments(attachments) {\n        // Implementar limpeza se necessário\n        // Por enquanto, mantemos os arquivos no Storage\n        console.log(\"Cleanup de anexos tempor\\xe1rios:\", attachments.length);\n    }\n    constructor(){\n        this.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB\n        ;\n        this.SUPPORTED_IMAGE_TYPES = [\n            \"image/png\",\n            \"image/jpeg\",\n            \"image/webp\"\n        ];\n        this.SUPPORTED_PDF_TYPE = \"application/pdf\";\n    }\n}\n// Exportar instância singleton\nconst attachmentService = new AttachmentService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (attachmentService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/attachmentService.ts\n"));

/***/ })

});