"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/InputBar.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/InputBar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InputBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst AI_MODELS = [\n    {\n        id: \"gpt-4.1-nano\",\n        name: \"GPT-4.1 Nano\",\n        description: \"R\\xe1pido e eficiente\"\n    },\n    {\n        id: \"gpt-4-turbo\",\n        name: \"GPT-4 Turbo\",\n        description: \"Mais poderoso\"\n    },\n    {\n        id: \"claude-3-sonnet\",\n        name: \"Claude 3 Sonnet\",\n        description: \"Criativo e preciso\"\n    },\n    {\n        id: \"gemini-pro\",\n        name: \"Gemini Pro\",\n        description: \"Multimodal\"\n    }\n];\nfunction InputBar(param) {\n    let { message, setMessage, onSendMessage, isLoading, selectedModel, onModelChange, onScrollToTop, onScrollToBottom, isStreaming = false, onCancelStreaming, onOpenModelModal, username, chatId, activeAttachmentsCount = 0 } = param;\n    _s();\n    const [webSearchEnabled, setWebSearchEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [processedAttachments, setProcessedAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const adjustHeightTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (adjustHeightTimeoutRef.current) {\n                clearTimeout(adjustHeightTimeoutRef.current);\n            }\n        };\n    }, []);\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const handleInputChange = (e)=>{\n        setMessage(e.target.value);\n        // Ajustar altura imediatamente após a mudança\n        setTimeout(()=>{\n            adjustTextareaHeight();\n        }, 0);\n    };\n    const handleSend = ()=>{\n        if (((message === null || message === void 0 ? void 0 : message.trim()) || attachments.length > 0) && !isLoading && !isUploading) {\n            console.log(\"=== DEBUG: ENVIANDO MENSAGEM ===\");\n            console.log(\"Mensagem:\", message);\n            console.log(\"Anexos locais:\", attachments.length);\n            console.log(\"Anexos processados:\", processedAttachments.length);\n            console.log(\"Anexos processados detalhes:\", processedAttachments);\n            console.log(\"Web Search Enabled:\", webSearchEnabled);\n            onSendMessage(processedAttachments, webSearchEnabled);\n            // Limpar anexos após envio\n            setAttachments([]);\n            setProcessedAttachments([]);\n        }\n    };\n    const handleAttachment = ()=>{\n        if (fileInputRef.current) {\n            fileInputRef.current.click();\n        }\n    };\n    const handleFileSelect = async (e)=>{\n        const files = e.target.files;\n        if (!files || !username || !chatId) return;\n        setIsUploading(true);\n        try {\n            // Primeiro, adicionar arquivos localmente para preview\n            const localAttachments = [];\n            for (const file of Array.from(files)){\n                const attachment = {\n                    id: Date.now().toString() + Math.random().toString(36).substring(2, 9),\n                    filename: file.name,\n                    type: file.type.startsWith(\"image/\") ? \"image\" : \"document\",\n                    file\n                };\n                localAttachments.push(attachment);\n            }\n            setAttachments((prev)=>[\n                    ...prev,\n                    ...localAttachments\n                ]);\n            // Fazer upload dos arquivos\n            const uploadedAttachments = await _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].uploadMultipleAttachments(Array.from(files), username, chatId);\n            // Atualizar com metadados dos arquivos processados\n            setProcessedAttachments((prev)=>[\n                    ...prev,\n                    ...uploadedAttachments.map((att)=>att.metadata)\n                ]);\n        } catch (error) {\n            console.error(\"Erro ao processar arquivos:\", error);\n            // Remover anexos que falharam\n            setAttachments((prev)=>prev.filter((att)=>!Array.from(files).some((file)=>file.name === att.filename)));\n        } finally{\n            setIsUploading(false);\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n        }\n    };\n    const removeAttachment = (id)=>{\n        // Encontrar o anexo para obter o filename\n        const attachment = attachments.find((att)=>att.id === id);\n        if (attachment) {\n            // Remover do estado local\n            setAttachments((prev)=>prev.filter((att)=>att.id !== id));\n            // Remover dos anexos processados também\n            setProcessedAttachments((prev)=>prev.filter((att)=>att.filename !== attachment.filename));\n        }\n    };\n    const handleWebSearch = ()=>{\n        setWebSearchEnabled(!webSearchEnabled);\n    };\n    const adjustTextareaHeight = ()=>{\n        const textarea = textareaRef.current;\n        if (textarea) {\n            // Salvar estado atual\n            const currentHeight = textarea.style.height;\n            const scrollTop = textarea.scrollTop;\n            // Temporariamente definir altura como auto para medir conteúdo\n            textarea.style.height = \"auto\";\n            // Calcular nova altura baseada no scrollHeight\n            const scrollHeight = textarea.scrollHeight;\n            const minHeight = 44;\n            const maxHeight = 120;\n            // Determinar altura final\n            const newHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));\n            // Só aplicar se realmente mudou para evitar flicker\n            if (currentHeight !== newHeight + \"px\") {\n                textarea.style.height = newHeight + \"px\";\n            }\n            // Restaurar scroll se necessário\n            if (scrollTop > 0) {\n                textarea.scrollTop = scrollTop;\n            }\n        }\n    };\n    const isWebSearchEnabled = ()=>{\n        // Web search está disponível para todos os modelos via OpenRouter plugins\n        // Apenas ocultar para modelos locais ou específicos que não suportam\n        const unsupportedModels = [\n            \"local/\",\n            \"offline/\"\n        ];\n        return !unsupportedModels.some((prefix)=>selectedModel.startsWith(prefix));\n    };\n    const currentModel = AI_MODELS.find((model)=>model.id === selectedModel);\n    const modelName = currentModel ? currentModel.name : selectedModel;\n    const selectedAttachmentsCount = attachments.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-3 sm:p-4 lg:p-6 border-t border-blue-700/30 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto relative z-10\",\n                children: [\n                    attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3 sm:mb-4 p-3 sm:p-4 bg-blue-900/30 backdrop-blur-sm rounded-lg sm:rounded-xl border border-blue-600/30\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 sm:gap-3\",\n                            children: attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-800/40 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 border border-blue-600/20\",\n                                    children: [\n                                        attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-blue-400 flex-shrink-0\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-red-400 flex-shrink-0\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs sm:text-sm text-blue-100 truncate max-w-20 sm:max-w-32 font-medium\",\n                                            children: attachment.filename\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>removeAttachment(attachment.id),\n                                            className: \"text-blue-300 hover:text-red-400 transition-colors p-0.5 sm:p-1 rounded-full hover:bg-red-500/20 flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3.5 h-3.5 sm:w-4 sm:h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, attachment.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-2 sm:space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative bg-gradient-to-r from-blue-900/80 to-blue-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-blue-600/30 shadow-xl shadow-blue-900/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-xl sm:rounded-2xl shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10 flex items-end space-x-2 sm:space-x-3 p-3 sm:p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 sm:space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: onOpenModelModal,\n                                                        className: \"p-2 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 hover:scale-105\",\n                                                        title: \"Selecionar modelo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleAttachment,\n                                                        disabled: isUploading,\n                                                        className: \"p-2.5 rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105\",\n                                                        title: \"Anexar arquivo\",\n                                                        children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isWebSearchEnabled() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleWebSearch,\n                                                        className: \"p-2.5 rounded-xl transition-all duration-200 hover:shadow-lg border hover:scale-105 backdrop-blur-sm \".concat(webSearchEnabled ? \"bg-cyan-600/30 hover:bg-cyan-600/40 text-cyan-200 hover:text-cyan-100 border-cyan-500/50 shadow-lg shadow-cyan-500/20\" : \"bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 border-blue-600/20 hover:shadow-blue-500/20\"),\n                                                        title: \"Busca na web - \".concat(webSearchEnabled ? \"Ativada\" : \"Desativada\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    ref: textareaRef,\n                                                    value: message,\n                                                    onChange: handleInputChange,\n                                                    onKeyDown: handleKeyPress,\n                                                    className: \"w-full bg-transparent border-none rounded-xl px-4 text-blue-100 placeholder:text-blue-300/60 focus:outline-none resize-none text-sm selection:bg-blue-500/30\",\n                                                    rows: 1,\n                                                    placeholder: \"Digite sua mensagem aqui... ✨\",\n                                                    disabled: isLoading || isStreaming,\n                                                    style: {\n                                                        height: \"44px\",\n                                                        lineHeight: \"1.4\",\n                                                        paddingTop: \"10px\",\n                                                        paddingBottom: \"10px\",\n                                                        minHeight: \"44px\",\n                                                        maxHeight: \"120px\",\n                                                        overflow: \"hidden\",\n                                                        boxSizing: \"border-box\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 15\n                                            }, this),\n                                            isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onCancelStreaming,\n                                                className: \"p-3 rounded-xl bg-gradient-to-r from-red-600 via-red-500 to-red-600 hover:from-red-500 hover:via-red-400 hover:to-red-500 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-red-500/30\",\n                                                title: \"Parar gera\\xe7\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSend,\n                                                disabled: !(message === null || message === void 0 ? void 0 : message.trim()) && attachments.length === 0 || isLoading || isUploading || isStreaming,\n                                                className: \"p-3 rounded-xl bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105 disabled:hover:scale-100 border border-blue-500/30\",\n                                                title: \"Enviar mensagem\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M14 5l7 7m0 0l-7 7m7-7H3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-8 left-4 flex items-center space-x-2\",\n                                        children: [\n                                            selectedModel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-blue-300\",\n                                                        children: \"Modelo: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-cyan-300 font-medium\",\n                                                        children: modelName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this),\n                                            activeAttachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-green-600/30 shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-300\",\n                                                        children: \"Anexos: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-200 font-medium\",\n                                                        children: activeAttachmentsCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-2\",\n                                children: [\n                                    onScrollToTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onScrollToTop,\n                                        className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                        title: \"Ir para o topo\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"relative w-5 h-5 transform group-hover:-translate-y-0.5 transition-transform duration-200\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2.5,\n                                                    d: \"M5 10l7-7m0 0l7 7m-7-7v18\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    onScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onScrollToBottom,\n                                        className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                        title: \"Ir para o final\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"relative w-5 h-5 transform group-hover:translate-y-0.5 transition-transform duration-200\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2.5,\n                                                    d: \"M19 14l-7 7m0 0l-7-7m7 7V4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        multiple: true,\n                        accept: \"image/png,image/jpeg,image/webp,application/pdf\",\n                        onChange: handleFileSelect,\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, this);\n}\n_s(InputBar, \"NATbgEVliPa57PsWdc1QpV4trNc=\");\n_c = InputBar;\nvar _c;\n$RefreshReg$(_c, \"InputBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/InputBar.tsx\n"));

/***/ })

});