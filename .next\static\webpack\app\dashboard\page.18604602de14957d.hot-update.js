"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/attachmentService.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/attachmentService.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachmentService: function() { return /* binding */ attachmentService; }\n/* harmony export */ });\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\nclass AttachmentService {\n    /**\n   * Valida se o arquivo é suportado\n   */ validateFile(file) {\n        console.log(\"=== DEBUG: VALIDANDO ARQUIVO ===\");\n        console.log(\"Nome:\", file.name);\n        console.log(\"Tipo:\", file.type);\n        console.log(\"Tamanho:\", file.size);\n        console.log(\"Tamanho m\\xe1ximo permitido:\", this.MAX_FILE_SIZE);\n        // Verificar tamanho\n        if (file.size > this.MAX_FILE_SIZE) {\n            console.log(\"=== DEBUG: ARQUIVO MUITO GRANDE ===\");\n            return {\n                isValid: false,\n                error: \"Arquivo muito grande. M\\xe1ximo permitido: 10MB\"\n            };\n        }\n        // Verificar tipo\n        const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n        const isPdf = file.type === this.SUPPORTED_PDF_TYPE;\n        console.log(\"=== DEBUG: VERIFICA\\xc7\\xc3O DE TIPO ===\");\n        console.log(\"\\xc9 imagem:\", isImage);\n        console.log(\"\\xc9 PDF:\", isPdf);\n        console.log(\"Tipos de imagem suportados:\", this.SUPPORTED_IMAGE_TYPES);\n        console.log(\"Tipo PDF suportado:\", this.SUPPORTED_PDF_TYPE);\n        if (!isImage && !isPdf) {\n            console.log(\"=== DEBUG: TIPO DE ARQUIVO N\\xc3O SUPORTADO ===\");\n            return {\n                isValid: false,\n                error: \"Tipo de arquivo n\\xe3o suportado. Use PNG, JPEG, WebP ou PDF\"\n            };\n        }\n        console.log(\"=== DEBUG: ARQUIVO V\\xc1LIDO ===\");\n        return {\n            isValid: true\n        };\n    }\n    /**\n   * Converte arquivo para base64\n   */ async fileToBase64(file) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>{\n                const result = reader.result;\n                // Remover o prefixo data:type;base64,\n                const base64 = result.split(\",\")[1];\n                resolve(base64);\n            };\n            reader.onerror = reject;\n            reader.readAsDataURL(file);\n        });\n    }\n    /**\n   * Gera ID único para anexo\n   */ generateAttachmentId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Faz upload do arquivo para Firebase Storage\n   */ async uploadAttachment(params) {\n        const { file, username, chatId } = params;\n        console.log(\"=== DEBUG: ATTACHMENT SERVICE - UPLOAD INICIADO ===\");\n        console.log(\"Arquivo:\", file.name);\n        console.log(\"Username:\", username);\n        console.log(\"ChatId:\", chatId);\n        // Validar arquivo\n        const validation = this.validateFile(file);\n        console.log(\"=== DEBUG: VALIDA\\xc7\\xc3O DO ARQUIVO ===\");\n        console.log(\"Valida\\xe7\\xe3o:\", validation);\n        if (!validation.isValid) {\n            console.error(\"=== DEBUG: ARQUIVO INV\\xc1LIDO ===\");\n            console.error(\"Erro:\", validation.error);\n            throw new Error(validation.error);\n        }\n        try {\n            // Gerar ID único para o anexo\n            const attachmentId = this.generateAttachmentId();\n            console.log(\"=== DEBUG: ID DO ANEXO GERADO ===\");\n            console.log(\"AttachmentId:\", attachmentId);\n            // Determinar tipo\n            const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n            const type = isImage ? \"image\" : \"pdf\";\n            console.log(\"=== DEBUG: TIPO DO ARQUIVO ===\");\n            console.log(\"Tipo determinado:\", type);\n            console.log(\"\\xc9 imagem:\", isImage);\n            // Criar caminho no Storage\n            const storagePath = \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/anexos/\").concat(attachmentId, \"_\").concat(file.name);\n            console.log(\"=== DEBUG: CAMINHO NO STORAGE ===\");\n            console.log(\"Storage path:\", storagePath);\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.storage, storagePath);\n            console.log(\"=== DEBUG: REFER\\xcaNCIA DO STORAGE CRIADA ===\");\n            // Upload do arquivo\n            console.log(\"=== DEBUG: INICIANDO UPLOAD PARA FIREBASE STORAGE ===\");\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.uploadBytes)(storageRef, file);\n            console.log(\"=== DEBUG: UPLOAD PARA FIREBASE STORAGE CONCLU\\xcdDO ===\");\n            // Obter URL de download\n            console.log(\"=== DEBUG: OBTENDO URL DE DOWNLOAD ===\");\n            const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.getDownloadURL)(storageRef);\n            console.log(\"=== DEBUG: URL DE DOWNLOAD OBTIDA ===\");\n            console.log(\"Download URL:\", downloadURL);\n            // Preparar metadados\n            const metadata = {\n                id: attachmentId,\n                type,\n                filename: file.name,\n                url: downloadURL,\n                size: file.size,\n                uploadedAt: Date.now(),\n                storagePath,\n                isActive: true // Por padrão, anexos são ativos\n            };\n            console.log(\"=== DEBUG: METADADOS PREPARADOS ===\");\n            console.log(\"Metadata:\", metadata);\n            // Para PDFs, também converter para base64 para envio ao OpenRouter\n            let base64Data;\n            if (type === \"pdf\") {\n                console.log(\"=== DEBUG: CONVERTENDO PDF PARA BASE64 ===\");\n                base64Data = await this.fileToBase64(file);\n                metadata.base64Data = base64Data;\n                console.log(\"=== DEBUG: PDF CONVERTIDO PARA BASE64 ===\");\n                console.log(\"Base64 length:\", base64Data === null || base64Data === void 0 ? void 0 : base64Data.length);\n            }\n            const result = {\n                metadata,\n                base64Data\n            };\n            console.log(\"=== DEBUG: UPLOAD ATTACHMENT CONCLU\\xcdDO COM SUCESSO ===\");\n            console.log(\"Resultado:\", result);\n            return result;\n        } catch (error) {\n            console.error(\"=== DEBUG: ERRO NO UPLOAD ATTACHMENT ===\");\n            console.error(\"Erro:\", error);\n            throw new Error(\"Falha no upload do arquivo. Tente novamente.\");\n        }\n    }\n    /**\n   * Processa múltiplos arquivos\n   */ async uploadMultipleAttachments(files, username, chatId) {\n        console.log(\"=== DEBUG: UPLOAD M\\xdaLTIPLOS ANEXOS INICIADO ===\");\n        console.log(\"N\\xfamero de arquivos:\", files.length);\n        console.log(\"Username:\", username);\n        console.log(\"ChatId:\", chatId);\n        const results = [];\n        for(let i = 0; i < files.length; i++){\n            const file = files[i];\n            console.log(\"=== DEBUG: PROCESSANDO ARQUIVO \".concat(i + 1, \"/\").concat(files.length, \" ===\"));\n            console.log(\"Nome do arquivo:\", file.name);\n            try {\n                const result = await this.uploadAttachment({\n                    file,\n                    username,\n                    chatId\n                });\n                results.push(result);\n                console.log(\"=== DEBUG: ARQUIVO \".concat(i + 1, \" PROCESSADO COM SUCESSO ===\"));\n            } catch (error) {\n                console.error(\"=== DEBUG: ERRO AO PROCESSAR ARQUIVO \".concat(file.name, \" ===\"));\n                console.error(\"Erro:\", error);\n            // Continuar com os outros arquivos\n            }\n        }\n        console.log(\"=== DEBUG: UPLOAD M\\xdaLTIPLOS ANEXOS CONCLU\\xcdDO ===\");\n        console.log(\"Resultados:\", results.length, \"de\", files.length, \"arquivos processados\");\n        return results;\n    }\n    /**\n   * Prepara anexos para envio ao OpenRouter\n   */ prepareAttachmentsForOpenRouter(attachments) {\n        const openRouterContent = [];\n        for (const attachment of attachments){\n            if (attachment.type === \"image\") {\n                // Para imagens, usar URL\n                openRouterContent.push({\n                    type: \"image_url\",\n                    image_url: {\n                        url: attachment.url\n                    }\n                });\n            } else if (attachment.type === \"pdf\" && attachment.base64Data) {\n                // Para PDFs, usar base64 com formato file\n                const dataUrl = \"data:application/pdf;base64,\".concat(attachment.base64Data);\n                openRouterContent.push({\n                    type: \"file\",\n                    file: {\n                        filename: attachment.filename,\n                        file_data: dataUrl\n                    }\n                });\n            }\n        }\n        return openRouterContent;\n    }\n    /**\n   * Prepara plugins para PDFs (engine mistral-ocr)\n   */ preparePDFPlugins(attachments) {\n        const hasPDF = attachments.some((att)=>att.type === \"pdf\");\n        if (!hasPDF) {\n            return [];\n        }\n        return [\n            {\n                id: \"file-parser\",\n                pdf: {\n                    engine: \"mistral-ocr\"\n                }\n            }\n        ];\n    }\n    /**\n   * Limpa anexos temporários (se necessário)\n   */ async cleanupTemporaryAttachments(attachments) {\n        // Implementar limpeza se necessário\n        // Por enquanto, mantemos os arquivos no Storage\n        console.log(\"Cleanup de anexos tempor\\xe1rios:\", attachments.length);\n    }\n    constructor(){\n        this.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB\n        ;\n        this.SUPPORTED_IMAGE_TYPES = [\n            \"image/png\",\n            \"image/jpeg\",\n            \"image/webp\"\n        ];\n        this.SUPPORTED_PDF_TYPE = \"application/pdf\";\n    }\n}\n// Exportar instância singleton\nconst attachmentService = new AttachmentService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (attachmentService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/attachmentService.ts\n"));

/***/ })

});