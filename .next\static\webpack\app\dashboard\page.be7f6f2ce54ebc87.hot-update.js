"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/InputBar.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/InputBar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InputBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst AI_MODELS = [\n    {\n        id: \"gpt-4.1-nano\",\n        name: \"GPT-4.1 Nano\",\n        description: \"R\\xe1pido e eficiente\"\n    },\n    {\n        id: \"gpt-4-turbo\",\n        name: \"GPT-4 Turbo\",\n        description: \"Mais poderoso\"\n    },\n    {\n        id: \"claude-3-sonnet\",\n        name: \"Claude 3 Sonnet\",\n        description: \"Criativo e preciso\"\n    },\n    {\n        id: \"gemini-pro\",\n        name: \"Gemini Pro\",\n        description: \"Multimodal\"\n    }\n];\nfunction InputBar(param) {\n    let { message, setMessage, onSendMessage, isLoading, selectedModel, onModelChange, onScrollToTop, onScrollToBottom, isStreaming = false, onCancelStreaming, onOpenModelModal, username, chatId, activeAttachmentsCount = 0 } = param;\n    _s();\n    const [webSearchEnabled, setWebSearchEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [processedAttachments, setProcessedAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const adjustHeightTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (adjustHeightTimeoutRef.current) {\n                clearTimeout(adjustHeightTimeoutRef.current);\n            }\n        };\n    }, []);\n    // Adjust textarea height when message changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        adjustTextareaHeight();\n    }, [\n        message\n    ]);\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        // Ajustar altura imediatamente para evitar flickering\n        requestAnimationFrame(()=>{\n            adjustTextareaHeight();\n        });\n    };\n    const handleSend = ()=>{\n        console.log(\"=== DEBUG: TENTATIVA DE ENVIO ===\");\n        console.log(\"Mensagem:\", message);\n        console.log(\"Anexos locais:\", attachments.length);\n        console.log(\"Anexos processados:\", processedAttachments.length);\n        console.log(\"isLoading:\", isLoading);\n        console.log(\"isUploading:\", isUploading);\n        const hasMessage = message === null || message === void 0 ? void 0 : message.trim();\n        const hasAttachments = attachments.length > 0;\n        const hasProcessedAttachments = processedAttachments.length > 0;\n        console.log(\"hasMessage:\", hasMessage);\n        console.log(\"hasAttachments:\", hasAttachments);\n        console.log(\"hasProcessedAttachments:\", hasProcessedAttachments);\n        if ((hasMessage || hasProcessedAttachments) && !isLoading && !isUploading) {\n            console.log(\"✅ Condi\\xe7\\xf5es atendidas, enviando mensagem...\");\n            console.log(\"Anexos processados detalhes:\", processedAttachments);\n            console.log(\"Web Search Enabled:\", webSearchEnabled);\n            onSendMessage(processedAttachments, webSearchEnabled);\n            // Limpar anexos após envio\n            setAttachments([]);\n            setProcessedAttachments([]);\n        } else {\n            console.log(\"❌ Condi\\xe7\\xf5es n\\xe3o atendidas para envio\");\n            if (!hasMessage && !hasProcessedAttachments) {\n                console.log(\"- Sem mensagem e sem anexos processados\");\n            }\n            if (isLoading) {\n                console.log(\"- Sistema est\\xe1 carregando\");\n            }\n            if (isUploading) {\n                console.log(\"- Upload em andamento\");\n            }\n        }\n    };\n    const handleAttachment = ()=>{\n        if (fileInputRef.current) {\n            fileInputRef.current.click();\n        }\n    };\n    const handleFileSelect = async (e)=>{\n        const files = e.target.files;\n        console.log(\"=== DEBUG: SELE\\xc7\\xc3O DE ARQUIVOS ===\");\n        console.log(\"Files:\", files);\n        console.log(\"Username:\", username);\n        console.log(\"ChatId:\", chatId);\n        if (!files || files.length === 0) {\n            console.log(\"❌ Nenhum arquivo selecionado\");\n            return;\n        }\n        if (!username) {\n            console.log(\"❌ Username n\\xe3o dispon\\xedvel\");\n            return;\n        }\n        // Se não há chatId, criar um temporário para o upload\n        // O chat real será criado quando a mensagem for enviada\n        let chatIdForUpload = chatId;\n        if (!chatIdForUpload) {\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            chatIdForUpload = \"temp_chat_\".concat(timestamp, \"_\").concat(random);\n            console.log(\"\\uD83C\\uDD94 Criando chatId tempor\\xe1rio para upload: \".concat(chatIdForUpload));\n        }\n        console.log(\"✅ Processando \".concat(files.length, \" arquivo(s)\"));\n        setIsUploading(true);\n        try {\n            // Primeiro, adicionar arquivos localmente para preview\n            const localAttachments = [];\n            for (const file of Array.from(files)){\n                console.log(\"\\uD83D\\uDCC1 Processando arquivo: \".concat(file.name, \" (\").concat(file.type, \", \").concat(file.size, \" bytes)\"));\n                const attachment = {\n                    id: Date.now().toString() + Math.random().toString(36).substring(2, 9),\n                    filename: file.name,\n                    type: file.type.startsWith(\"image/\") ? \"image\" : \"document\",\n                    file\n                };\n                localAttachments.push(attachment);\n            }\n            console.log(\"\\uD83D\\uDCCE Adicionando \".concat(localAttachments.length, \" anexos locais para preview\"));\n            setAttachments((prev)=>[\n                    ...prev,\n                    ...localAttachments\n                ]);\n            // Fazer upload dos arquivos\n            console.log(\"\\uD83D\\uDE80 Iniciando upload dos arquivos...\");\n            const uploadedAttachments = await _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].uploadMultipleAttachments(Array.from(files), username, chatIdForUpload);\n            console.log(\"✅ Upload conclu\\xeddo! \".concat(uploadedAttachments.length, \" arquivos processados\"));\n            console.log(\"Anexos processados:\", uploadedAttachments);\n            // Atualizar com metadados dos arquivos processados\n            const processedMetadata = uploadedAttachments.map((att)=>att.metadata);\n            console.log(\"\\uD83D\\uDCCB Metadados dos anexos:\", processedMetadata);\n            setProcessedAttachments((prev)=>[\n                    ...prev,\n                    ...processedMetadata\n                ]);\n        } catch (error) {\n            console.error(\"❌ Erro ao processar arquivos:\", error);\n            // Remover anexos que falharam\n            setAttachments((prev)=>prev.filter((att)=>!Array.from(files).some((file)=>file.name === att.filename)));\n        } finally{\n            setIsUploading(false);\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n        }\n    };\n    const removeAttachment = (id)=>{\n        // Encontrar o anexo para obter o filename\n        const attachment = attachments.find((att)=>att.id === id);\n        if (attachment) {\n            // Remover do estado local\n            setAttachments((prev)=>prev.filter((att)=>att.id !== id));\n            // Remover dos anexos processados também\n            setProcessedAttachments((prev)=>prev.filter((att)=>att.filename !== attachment.filename));\n        }\n    };\n    const handleWebSearch = ()=>{\n        setWebSearchEnabled(!webSearchEnabled);\n    };\n    const adjustTextareaHeight = ()=>{\n        const textarea = textareaRef.current;\n        if (!textarea) return;\n        // Store current scroll position to prevent jumping\n        const scrollTop = textarea.scrollTop;\n        // Temporarily set height to auto to get accurate scrollHeight\n        textarea.style.height = \"auto\";\n        // Calculate new height with proper constraints\n        const scrollHeight = textarea.scrollHeight;\n        const minHeight = 44; // Minimum height in pixels\n        const maxHeight = 120; // Maximum height in pixels\n        // Set the new height within bounds\n        const newHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));\n        textarea.style.height = newHeight + \"px\";\n        // Handle overflow and scrolling\n        if (scrollHeight > maxHeight) {\n            textarea.style.overflowY = \"auto\";\n            // Restore scroll position if content was scrolled\n            if (scrollTop > 0) {\n                textarea.scrollTop = scrollTop;\n            }\n        } else {\n            textarea.style.overflowY = \"hidden\";\n        }\n    };\n    const isWebSearchEnabled = ()=>{\n        // Web search está disponível para todos os modelos via OpenRouter plugins\n        // Apenas ocultar para modelos locais ou específicos que não suportam\n        const unsupportedModels = [\n            \"local/\",\n            \"offline/\"\n        ];\n        return !unsupportedModels.some((prefix)=>selectedModel.startsWith(prefix));\n    };\n    const currentModel = AI_MODELS.find((model)=>model.id === selectedModel);\n    const modelName = currentModel ? currentModel.name : selectedModel;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-x-0 -top-8 h-8 bg-gradient-to-t from-blue-950/30 via-blue-950/10 to-transparent pointer-events-none blur-sm\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 sm:p-4 lg:p-6 bg-gradient-to-r from-blue-950/98 via-blue-900/98 to-blue-950/98 backdrop-blur-xl relative shadow-2xl shadow-blue-900/60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-500/8 via-transparent to-cyan-500/8 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-blue-600/15 via-blue-500/5 to-transparent pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 inset-x-0 h-px bg-gradient-to-r from-transparent via-blue-400/30 to-transparent pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto relative z-10\",\n                        children: [\n                            attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3 sm:mb-4 p-3 sm:p-4 bg-blue-900/30 backdrop-blur-sm rounded-lg sm:rounded-xl border border-blue-600/30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 sm:gap-3\",\n                                    children: attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-800/40 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 border border-blue-600/20\",\n                                            children: [\n                                                attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-blue-400 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-red-400 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs sm:text-sm text-blue-100 truncate max-w-20 sm:max-w-32 font-medium\",\n                                                    children: attachment.filename\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>removeAttachment(attachment.id),\n                                                    className: \"text-blue-300 hover:text-red-400 transition-colors p-0.5 sm:p-1 rounded-full hover:bg-red-500/20 flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3.5 h-3.5 sm:w-4 sm:h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M6 18L18 6M6 6l12 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, attachment.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this),\n                            (selectedModel || activeAttachmentsCount > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-1 flex items-center justify-start space-x-2\",\n                                children: [\n                                    selectedModel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-blue-300\",\n                                                children: \"Modelo: \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-cyan-300 font-medium\",\n                                                children: modelName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeAttachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-green-600/30 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-300\",\n                                                children: \"Anexos: \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: activeAttachmentsCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end space-x-2 sm:space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 relative bg-gradient-to-r from-blue-900/80 to-blue-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-blue-600/30 shadow-xl shadow-blue-900/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl sm:rounded-2xl shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 flex items-end space-x-2 sm:space-x-3 p-3 sm:p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 sm:space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: onOpenModelModal,\n                                                                className: \"p-2 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 hover:scale-105\",\n                                                                title: \"Selecionar modelo\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleAttachment,\n                                                                disabled: isUploading,\n                                                                className: \"p-2.5 rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105\",\n                                                                title: \"Anexar arquivo\",\n                                                                children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 21\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                        lineNumber: 379,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            isWebSearchEnabled() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleWebSearch,\n                                                                className: \"p-2.5 rounded-xl transition-all duration-200 hover:shadow-lg border hover:scale-105 backdrop-blur-sm \".concat(webSearchEnabled ? \"bg-cyan-600/30 hover:bg-cyan-600/40 text-cyan-200 hover:text-cyan-100 border-cyan-500/50 shadow-lg shadow-cyan-500/20\" : \"bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 border-blue-600/20 hover:shadow-blue-500/20\"),\n                                                                title: \"Busca na web - \".concat(webSearchEnabled ? \"Ativada\" : \"Desativada\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                        lineNumber: 396,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            ref: textareaRef,\n                                                            value: message,\n                                                            onChange: handleInputChange,\n                                                            onKeyDown: handleKeyPress,\n                                                            className: \"w-full bg-transparent border-none rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none resize-none text-sm selection:bg-blue-500/30\",\n                                                            rows: 1,\n                                                            placeholder: \"Digite sua mensagem aqui...\",\n                                                            disabled: isLoading || isStreaming,\n                                                            style: {\n                                                                height: \"44px\",\n                                                                minHeight: \"44px\",\n                                                                maxHeight: \"120px\",\n                                                                lineHeight: \"1.5\",\n                                                                wordWrap: \"break-word\",\n                                                                whiteSpace: \"pre-wrap\",\n                                                                overflowY: \"hidden\",\n                                                                overflowX: \"hidden\",\n                                                                scrollbarWidth: \"thin\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: onCancelStreaming,\n                                                        className: \"p-3 rounded-xl bg-gradient-to-r from-red-600 via-red-500 to-red-600 hover:from-red-500 hover:via-red-400 hover:to-red-500 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-red-500/30\",\n                                                        title: \"Parar gera\\xe7\\xe3o\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 17\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleSend,\n                                                        disabled: !(message === null || message === void 0 ? void 0 : message.trim()) && attachments.length === 0 || isLoading || isUploading || isStreaming,\n                                                        className: \"p-3 rounded-xl bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105 disabled:hover:scale-100 border border-blue-500/30\",\n                                                        title: \"Enviar mensagem\",\n                                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M14 5l7 7m0 0l-7 7m7-7H3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-2\",\n                                        children: [\n                                            onScrollToTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onScrollToTop,\n                                                className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                                title: \"Ir para o topo\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"relative w-5 h-5 transform group-hover:-translate-y-0.5 transition-transform duration-200\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2.5,\n                                                            d: \"M5 10l7-7m0 0l7 7m-7-7v18\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 15\n                                            }, this),\n                                            onScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onScrollToBottom,\n                                                className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                                title: \"Ir para o final\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"relative w-5 h-5 transform group-hover:translate-y-0.5 transition-transform duration-200\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2.5,\n                                                            d: \"M19 14l-7 7m0 0l-7-7m7 7V4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: fileInputRef,\n                                type: \"file\",\n                                multiple: true,\n                                accept: \"image/png,image/jpeg,image/webp,application/pdf\",\n                                onChange: handleFileSelect,\n                                className: \"hidden\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(InputBar, \"P23FsoqKhoN2XSImOuRpWlk/+tg=\");\n_c = InputBar;\nvar _c;\n$RefreshReg$(_c, \"InputBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9JbnB1dEJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVvRDtBQUVhO0FBMkJqRSxNQUFNSSxZQUFZO0lBQ2hCO1FBQUVDLElBQUk7UUFBZ0JDLE1BQU07UUFBZ0JDLGFBQWE7SUFBcUI7SUFDOUU7UUFBRUYsSUFBSTtRQUFlQyxNQUFNO1FBQWVDLGFBQWE7SUFBZ0I7SUFDdkU7UUFBRUYsSUFBSTtRQUFtQkMsTUFBTTtRQUFtQkMsYUFBYTtJQUFxQjtJQUNwRjtRQUFFRixJQUFJO1FBQWNDLE1BQU07UUFBY0MsYUFBYTtJQUFhO0NBQ25FO0FBRWMsU0FBU0MsU0FBUyxLQWVqQjtRQWZpQixFQUMvQkMsT0FBTyxFQUNQQyxVQUFVLEVBQ1ZDLGFBQWEsRUFDYkMsU0FBUyxFQUNUQyxhQUFhLEVBQ2JDLGFBQWEsRUFDYkMsYUFBYSxFQUNiQyxnQkFBZ0IsRUFDaEJDLGNBQWMsS0FBSyxFQUNuQkMsaUJBQWlCLEVBQ2pCQyxnQkFBZ0IsRUFDaEJDLFFBQVEsRUFDUkMsTUFBTSxFQUNOQyx5QkFBeUIsQ0FBQyxFQUNaLEdBZmlCOztJQWlCL0IsTUFBTSxDQUFDQyxrQkFBa0JDLG9CQUFvQixHQUFHeEIsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDeUIsYUFBYUMsZUFBZSxHQUFHMUIsK0NBQVFBLENBQWUsRUFBRTtJQUMvRCxNQUFNLENBQUMyQixzQkFBc0JDLHdCQUF3QixHQUFHNUIsK0NBQVFBLENBQXVCLEVBQUU7SUFDekYsTUFBTSxDQUFDNkIsYUFBYUMsZUFBZSxHQUFHOUIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTStCLGNBQWM5Qiw2Q0FBTUEsQ0FBc0I7SUFDaEQsTUFBTStCLGVBQWUvQiw2Q0FBTUEsQ0FBbUI7SUFDOUMsTUFBTWdDLHlCQUF5QmhDLDZDQUFNQSxDQUF3QjtJQUU3RCw2QkFBNkI7SUFDN0JDLGdEQUFTQSxDQUFDO1FBQ1IsT0FBTztZQUNMLElBQUkrQix1QkFBdUJDLE9BQU8sRUFBRTtnQkFDbENDLGFBQWFGLHVCQUF1QkMsT0FBTztZQUM3QztRQUNGO0lBQ0YsR0FBRyxFQUFFO0lBRUwsOENBQThDO0lBQzlDaEMsZ0RBQVNBLENBQUM7UUFDUmtDO0lBQ0YsR0FBRztRQUFDM0I7S0FBUTtJQUVaLE1BQU00QixpQkFBaUIsQ0FBQ0M7UUFDdEIsSUFBSUEsRUFBRUMsR0FBRyxLQUFLLFdBQVcsQ0FBQ0QsRUFBRUUsUUFBUSxFQUFFO1lBQ3BDRixFQUFFRyxjQUFjO1lBQ2hCQztRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxvQkFBb0IsQ0FBQ0w7UUFDekIsTUFBTU0sUUFBUU4sRUFBRU8sTUFBTSxDQUFDRCxLQUFLO1FBQzVCbEMsV0FBV2tDO1FBRVgsc0RBQXNEO1FBQ3RERSxzQkFBc0I7WUFDcEJWO1FBQ0Y7SUFDRjtJQUVBLE1BQU1NLGFBQWE7UUFDakJLLFFBQVFDLEdBQUcsQ0FBQztRQUNaRCxRQUFRQyxHQUFHLENBQUMsYUFBYXZDO1FBQ3pCc0MsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQnZCLFlBQVl3QixNQUFNO1FBQ2hERixRQUFRQyxHQUFHLENBQUMsdUJBQXVCckIscUJBQXFCc0IsTUFBTTtRQUM5REYsUUFBUUMsR0FBRyxDQUFDLGNBQWNwQztRQUMxQm1DLFFBQVFDLEdBQUcsQ0FBQyxnQkFBZ0JuQjtRQUU1QixNQUFNcUIsYUFBYXpDLG9CQUFBQSw4QkFBQUEsUUFBUzBDLElBQUk7UUFDaEMsTUFBTUMsaUJBQWlCM0IsWUFBWXdCLE1BQU0sR0FBRztRQUM1QyxNQUFNSSwwQkFBMEIxQixxQkFBcUJzQixNQUFNLEdBQUc7UUFFOURGLFFBQVFDLEdBQUcsQ0FBQyxlQUFlRTtRQUMzQkgsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQkk7UUFDL0JMLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEJLO1FBRXhDLElBQUksQ0FBQ0gsY0FBY0csdUJBQXNCLEtBQU0sQ0FBQ3pDLGFBQWEsQ0FBQ2lCLGFBQWE7WUFDekVrQixRQUFRQyxHQUFHLENBQUM7WUFDWkQsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ3JCO1lBQzVDb0IsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QnpCO1lBRW5DWixjQUFjZ0Isc0JBQXNCSjtZQUNwQywyQkFBMkI7WUFDM0JHLGVBQWUsRUFBRTtZQUNqQkUsd0JBQXdCLEVBQUU7UUFDNUIsT0FBTztZQUNMbUIsUUFBUUMsR0FBRyxDQUFDO1lBQ1osSUFBSSxDQUFDRSxjQUFjLENBQUNHLHlCQUF5QjtnQkFDM0NOLFFBQVFDLEdBQUcsQ0FBQztZQUNkO1lBQ0EsSUFBSXBDLFdBQVc7Z0JBQ2JtQyxRQUFRQyxHQUFHLENBQUM7WUFDZDtZQUNBLElBQUluQixhQUFhO2dCQUNma0IsUUFBUUMsR0FBRyxDQUFDO1lBQ2Q7UUFDRjtJQUNGO0lBRUEsTUFBTU0sbUJBQW1CO1FBQ3ZCLElBQUl0QixhQUFhRSxPQUFPLEVBQUU7WUFDeEJGLGFBQWFFLE9BQU8sQ0FBQ3FCLEtBQUs7UUFDNUI7SUFDRjtJQUVBLE1BQU1DLG1CQUFtQixPQUFPbEI7UUFDOUIsTUFBTW1CLFFBQVFuQixFQUFFTyxNQUFNLENBQUNZLEtBQUs7UUFDNUJWLFFBQVFDLEdBQUcsQ0FBQztRQUNaRCxRQUFRQyxHQUFHLENBQUMsVUFBVVM7UUFDdEJWLFFBQVFDLEdBQUcsQ0FBQyxhQUFhNUI7UUFDekIyQixRQUFRQyxHQUFHLENBQUMsV0FBVzNCO1FBRXZCLElBQUksQ0FBQ29DLFNBQVNBLE1BQU1SLE1BQU0sS0FBSyxHQUFHO1lBQ2hDRixRQUFRQyxHQUFHLENBQUM7WUFDWjtRQUNGO1FBRUEsSUFBSSxDQUFDNUIsVUFBVTtZQUNiMkIsUUFBUUMsR0FBRyxDQUFDO1lBQ1o7UUFDRjtRQUVBLHNEQUFzRDtRQUN0RCx3REFBd0Q7UUFDeEQsSUFBSVUsa0JBQWtCckM7UUFDdEIsSUFBSSxDQUFDcUMsaUJBQWlCO1lBQ3BCLE1BQU1DLFlBQVlDLEtBQUtDLEdBQUc7WUFDMUIsTUFBTUMsU0FBU0MsS0FBS0QsTUFBTSxHQUFHRSxRQUFRLENBQUMsSUFBSUMsU0FBUyxDQUFDLEdBQUc7WUFDdkRQLGtCQUFrQixhQUEwQkksT0FBYkgsV0FBVSxLQUFVLE9BQVBHO1lBQzVDZixRQUFRQyxHQUFHLENBQUMsMERBQTZELE9BQWhCVTtRQUMzRDtRQUVBWCxRQUFRQyxHQUFHLENBQUMsaUJBQThCLE9BQWJTLE1BQU1SLE1BQU0sRUFBQztRQUMxQ25CLGVBQWU7UUFFZixJQUFJO1lBQ0YsdURBQXVEO1lBQ3ZELE1BQU1vQyxtQkFBaUMsRUFBRTtZQUN6QyxLQUFLLE1BQU1DLFFBQVFDLE1BQU1DLElBQUksQ0FBQ1osT0FBUTtnQkFDcENWLFFBQVFDLEdBQUcsQ0FBQyxxQ0FBeUNtQixPQUFkQSxLQUFLN0QsSUFBSSxFQUFDLE1BQWtCNkQsT0FBZEEsS0FBS0csSUFBSSxFQUFDLE1BQWMsT0FBVkgsS0FBS0ksSUFBSSxFQUFDO2dCQUU3RSxNQUFNQyxhQUF5QjtvQkFDN0JuRSxJQUFJdUQsS0FBS0MsR0FBRyxHQUFHRyxRQUFRLEtBQUtELEtBQUtELE1BQU0sR0FBR0UsUUFBUSxDQUFDLElBQUlDLFNBQVMsQ0FBQyxHQUFHO29CQUNwRVEsVUFBVU4sS0FBSzdELElBQUk7b0JBQ25CZ0UsTUFBTUgsS0FBS0csSUFBSSxDQUFDSSxVQUFVLENBQUMsWUFBWSxVQUFVO29CQUNqRFA7Z0JBQ0Y7Z0JBQ0FELGlCQUFpQlMsSUFBSSxDQUFDSDtZQUN4QjtZQUVBekIsUUFBUUMsR0FBRyxDQUFDLDRCQUEwQyxPQUF4QmtCLGlCQUFpQmpCLE1BQU0sRUFBQztZQUN0RHZCLGVBQWVrRCxDQUFBQSxPQUFRO3VCQUFJQTt1QkFBU1Y7aUJBQWlCO1lBRXJELDRCQUE0QjtZQUM1Qm5CLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU02QixzQkFBc0IsTUFBTTFFLHVFQUFpQkEsQ0FBQzJFLHlCQUF5QixDQUMzRVYsTUFBTUMsSUFBSSxDQUFDWixRQUNYckMsVUFDQXNDO1lBR0ZYLFFBQVFDLEdBQUcsQ0FBQywwQkFBa0QsT0FBM0I2QixvQkFBb0I1QixNQUFNLEVBQUM7WUFDOURGLFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUI2QjtZQUVuQyxtREFBbUQ7WUFDbkQsTUFBTUUsb0JBQW9CRixvQkFBb0JHLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSUMsUUFBUTtZQUNyRW5DLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBNEIrQjtZQUV4Q25ELHdCQUF3QmdELENBQUFBLE9BQVE7dUJBQzNCQTt1QkFDQUc7aUJBQ0o7UUFFSCxFQUFFLE9BQU9JLE9BQU87WUFDZHBDLFFBQVFvQyxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQyw4QkFBOEI7WUFDOUJ6RCxlQUFla0QsQ0FBQUEsT0FBUUEsS0FBS1EsTUFBTSxDQUFDSCxDQUFBQSxNQUNqQyxDQUFDYixNQUFNQyxJQUFJLENBQUNaLE9BQU80QixJQUFJLENBQUNsQixDQUFBQSxPQUFRQSxLQUFLN0QsSUFBSSxLQUFLMkUsSUFBSVIsUUFBUTtRQUU5RCxTQUFVO1lBQ1IzQyxlQUFlO1lBQ2YsSUFBSUUsYUFBYUUsT0FBTyxFQUFFO2dCQUN4QkYsYUFBYUUsT0FBTyxDQUFDVSxLQUFLLEdBQUc7WUFDL0I7UUFDRjtJQUNGO0lBRUEsTUFBTTBDLG1CQUFtQixDQUFDakY7UUFDeEIsMENBQTBDO1FBQzFDLE1BQU1tRSxhQUFhL0MsWUFBWThELElBQUksQ0FBQ04sQ0FBQUEsTUFBT0EsSUFBSTVFLEVBQUUsS0FBS0E7UUFDdEQsSUFBSW1FLFlBQVk7WUFDZCwwQkFBMEI7WUFDMUI5QyxlQUFla0QsQ0FBQUEsT0FBUUEsS0FBS1EsTUFBTSxDQUFDSCxDQUFBQSxNQUFPQSxJQUFJNUUsRUFBRSxLQUFLQTtZQUNyRCx3Q0FBd0M7WUFDeEN1Qix3QkFBd0JnRCxDQUFBQSxPQUFRQSxLQUFLUSxNQUFNLENBQUNILENBQUFBLE1BQU9BLElBQUlSLFFBQVEsS0FBS0QsV0FBV0MsUUFBUTtRQUN6RjtJQUNGO0lBRUEsTUFBTWUsa0JBQWtCO1FBQ3RCaEUsb0JBQW9CLENBQUNEO0lBQ3ZCO0lBRUEsTUFBTWEsdUJBQXVCO1FBQzNCLE1BQU1xRCxXQUFXMUQsWUFBWUcsT0FBTztRQUNwQyxJQUFJLENBQUN1RCxVQUFVO1FBRWYsbURBQW1EO1FBQ25ELE1BQU1DLFlBQVlELFNBQVNDLFNBQVM7UUFFcEMsOERBQThEO1FBQzlERCxTQUFTRSxLQUFLLENBQUNDLE1BQU0sR0FBRztRQUV4QiwrQ0FBK0M7UUFDL0MsTUFBTUMsZUFBZUosU0FBU0ksWUFBWTtRQUMxQyxNQUFNQyxZQUFZLElBQUksMkJBQTJCO1FBQ2pELE1BQU1DLFlBQVksS0FBSywyQkFBMkI7UUFFbEQsbUNBQW1DO1FBQ25DLE1BQU1DLFlBQVlqQyxLQUFLa0MsR0FBRyxDQUFDSCxXQUFXL0IsS0FBS21DLEdBQUcsQ0FBQ0wsY0FBY0U7UUFDN0ROLFNBQVNFLEtBQUssQ0FBQ0MsTUFBTSxHQUFHSSxZQUFZO1FBRXBDLGdDQUFnQztRQUNoQyxJQUFJSCxlQUFlRSxXQUFXO1lBQzVCTixTQUFTRSxLQUFLLENBQUNRLFNBQVMsR0FBRztZQUMzQixrREFBa0Q7WUFDbEQsSUFBSVQsWUFBWSxHQUFHO2dCQUNqQkQsU0FBU0MsU0FBUyxHQUFHQTtZQUN2QjtRQUNGLE9BQU87WUFDTEQsU0FBU0UsS0FBSyxDQUFDUSxTQUFTLEdBQUc7UUFDN0I7SUFDRjtJQUVBLE1BQU1DLHFCQUFxQjtRQUN6QiwwRUFBMEU7UUFDMUUscUVBQXFFO1FBQ3JFLE1BQU1DLG9CQUFvQjtZQUFDO1lBQVU7U0FBVztRQUNoRCxPQUFPLENBQUNBLGtCQUFrQmhCLElBQUksQ0FBQ2lCLENBQUFBLFNBQVV6RixjQUFjNkQsVUFBVSxDQUFDNEI7SUFDcEU7SUFFQSxNQUFNQyxlQUFlbkcsVUFBVW1GLElBQUksQ0FBQ2lCLENBQUFBLFFBQVNBLE1BQU1uRyxFQUFFLEtBQUtRO0lBQzFELE1BQU00RixZQUFZRixlQUFlQSxhQUFhakcsSUFBSSxHQUFHTztJQUVyRCxxQkFDRSw4REFBQzZGO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7Ozs7OzBCQUVmLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBR2YsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBR2YsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBRWpCLDhEQUFDRDt3QkFBSUMsV0FBVTs7NEJBRVpsRixZQUFZd0IsTUFBTSxHQUFHLG1CQUNwQiw4REFBQ3lEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWmxGLFlBQVl1RCxHQUFHLENBQUMsQ0FBQ1IsMkJBQ2hCLDhEQUFDa0M7NENBRUNDLFdBQVU7O2dEQUVUbkMsV0FBV0YsSUFBSSxLQUFLLHdCQUNuQiw4REFBQ3NDO29EQUFJRCxXQUFVO29EQUF3REUsTUFBSztvREFBT0MsUUFBTztvREFBZUMsU0FBUTs4REFDL0csNEVBQUNDO3dEQUFLQyxlQUFjO3dEQUFRQyxnQkFBZTt3REFBUUMsYUFBYTt3REFBR0MsR0FBRTs7Ozs7Ozs7Ozt5RUFHdkUsOERBQUNSO29EQUFJRCxXQUFVO29EQUF1REUsTUFBSztvREFBT0MsUUFBTztvREFBZUMsU0FBUTs4REFDOUcsNEVBQUNDO3dEQUFLQyxlQUFjO3dEQUFRQyxnQkFBZTt3REFBUUMsYUFBYTt3REFBR0MsR0FBRTs7Ozs7Ozs7Ozs7OERBR3pFLDhEQUFDQztvREFBS1YsV0FBVTs4REFDYm5DLFdBQVdDLFFBQVE7Ozs7Ozs4REFFdEIsOERBQUM2QztvREFDQ0MsU0FBUyxJQUFNakMsaUJBQWlCZCxXQUFXbkUsRUFBRTtvREFDN0NzRyxXQUFVOzhEQUVWLDRFQUFDQzt3REFBSUQsV0FBVTt3REFBNEJFLE1BQUs7d0RBQU9DLFFBQU87d0RBQWVDLFNBQVE7a0VBQ25GLDRFQUFDQzs0REFBS0MsZUFBYzs0REFBUUMsZ0JBQWU7NERBQVFDLGFBQWE7NERBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OzJDQXBCcEU1QyxXQUFXbkUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OzRCQThCMUJRLENBQUFBLGlCQUFpQlMseUJBQXlCLG9CQUMxQyw4REFBQ29GO2dDQUFJQyxXQUFVOztvQ0FDWjlGLCtCQUNDLDhEQUFDNkY7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDVTtnREFBS1YsV0FBVTswREFBd0I7Ozs7OzswREFDeEMsOERBQUNVO2dEQUFLVixXQUFVOzBEQUFxQ0Y7Ozs7Ozs7Ozs7OztvQ0FJeERuRix5QkFBeUIsbUJBQ3hCLDhEQUFDb0Y7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDVTtnREFBS1YsV0FBVTswREFBeUI7Ozs7OzswREFDekMsOERBQUNVO2dEQUFLVixXQUFVOzBEQUFzQ3JGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTTlELDhEQUFDb0Y7Z0NBQUlDLFdBQVU7O2tEQUViLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBRWIsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7MERBRWYsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFFYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUViLDhEQUFDVztnRUFDQ0MsU0FBU3BHO2dFQUNUd0YsV0FBVTtnRUFDVmEsT0FBTTswRUFFTiw0RUFBQ1o7b0VBQUlELFdBQVU7b0VBQXdCRSxNQUFLO29FQUFPQyxRQUFPO29FQUFlQyxTQUFROzhFQUMvRSw0RUFBQ0M7d0VBQUtDLGVBQWM7d0VBQVFDLGdCQUFlO3dFQUFRQyxhQUFhO3dFQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBFQUt6RSw4REFBQ0U7Z0VBQ0NDLFNBQVNqRTtnRUFDVG1FLFVBQVU1RjtnRUFDVjhFLFdBQVU7Z0VBQ1ZhLE9BQU07MEVBRUwzRiw0QkFDQyw4REFBQzZFO29FQUFJQyxXQUFVOzs7Ozt5RkFFZiw4REFBQ0M7b0VBQUlELFdBQVU7b0VBQVVFLE1BQUs7b0VBQU9DLFFBQU87b0VBQWVDLFNBQVE7OEVBQ2pFLDRFQUFDQzt3RUFBS0MsZUFBYzt3RUFBUUMsZ0JBQWU7d0VBQVFDLGFBQWE7d0VBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7NERBTTFFaEIsc0NBQ0MsOERBQUNrQjtnRUFDQ0MsU0FBUy9CO2dFQUNUbUIsV0FBVyx3R0FJVixPQUhDcEYsbUJBQ0ksMEhBQ0E7Z0VBRU5pRyxPQUFPLGtCQUE4RCxPQUE1Q2pHLG1CQUFtQixZQUFZOzBFQUV4RCw0RUFBQ3FGO29FQUFJRCxXQUFVO29FQUFVRSxNQUFLO29FQUFlRSxTQUFROzhFQUNuRCw0RUFBQ0M7d0VBQUtJLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBT2hCLDhEQUFDVjt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQ2xCOzREQUNDaUMsS0FBSzNGOzREQUNMYSxPQUFPbkM7NERBQ1BrSCxVQUFVaEY7NERBQ1ZpRixXQUFXdkY7NERBQ1hzRSxXQUFVOzREQUNWa0IsTUFBTTs0REFDTkMsYUFBWTs0REFDWkwsVUFBVTdHLGFBQWFLOzREQUN2QjBFLE9BQU87Z0VBQ0xDLFFBQVE7Z0VBQ1JFLFdBQVc7Z0VBQ1hDLFdBQVc7Z0VBQ1hnQyxZQUFZO2dFQUNaQyxVQUFVO2dFQUNWQyxZQUFZO2dFQUNaOUIsV0FBVztnRUFDWCtCLFdBQVc7Z0VBQ1hDLGdCQUFnQjs0REFDbEI7Ozs7Ozs7Ozs7O29EQUtIbEgsNEJBQ0MsOERBQUNxRzt3REFDQ0MsU0FBU3JHO3dEQUNUeUYsV0FBVTt3REFDVmEsT0FBTTtrRUFFTiw0RUFBQ1o7NERBQUlELFdBQVU7NERBQVVFLE1BQUs7NERBQU9DLFFBQU87NERBQWVDLFNBQVE7c0VBQ2pFLDRFQUFDQztnRUFBS0MsZUFBYztnRUFBUUMsZ0JBQWU7Z0VBQVFDLGFBQWE7Z0VBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs2RUFJekUsOERBQUNFO3dEQUNDQyxTQUFTN0U7d0RBQ1QrRSxVQUFVLEVBQUVoSCxvQkFBQUEsOEJBQUFBLFFBQVMwQyxJQUFJLE9BQU0xQixZQUFZd0IsTUFBTSxLQUFLLEtBQU1yQyxhQUFhaUIsZUFBZVo7d0RBQ3hGMEYsV0FBVTt3REFDVmEsT0FBTTtrRUFFTDVHLDBCQUNDLDhEQUFDOEY7NERBQUlDLFdBQVU7Ozs7O2lGQUVmLDhEQUFDQzs0REFBSXdCLE9BQU07NERBQTZCekIsV0FBVTs0REFBVUUsTUFBSzs0REFBT0UsU0FBUTs0REFBWUQsUUFBTztzRUFDakcsNEVBQUNFO2dFQUFLQyxlQUFjO2dFQUFRQyxnQkFBZTtnRUFBUUMsYUFBYTtnRUFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFXakYsOERBQUNWO3dDQUFJQyxXQUFVOzs0Q0FFWjVGLCtCQUNDLDhEQUFDdUc7Z0RBQ0NDLFNBQVN4RztnREFDVDRGLFdBQVU7Z0RBQ1ZhLE9BQU07O2tFQUdOLDhEQUFDZDt3REFBSUMsV0FBVTs7Ozs7O2tFQUNmLDhEQUFDQzt3REFBSUQsV0FBVTt3REFBNEZFLE1BQUs7d0RBQU9DLFFBQU87d0RBQWVDLFNBQVE7a0VBQ25KLDRFQUFDQzs0REFBS0MsZUFBYzs0REFBUUMsZ0JBQWU7NERBQVFDLGFBQWE7NERBQUtDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OzRDQU01RXBHLGtDQUNDLDhEQUFDc0c7Z0RBQ0NDLFNBQVN2RztnREFDVDJGLFdBQVU7Z0RBQ1ZhLE9BQU07O2tFQUdOLDhEQUFDZDt3REFBSUMsV0FBVTs7Ozs7O2tFQUNmLDhEQUFDQzt3REFBSUQsV0FBVTt3REFBMkZFLE1BQUs7d0RBQU9DLFFBQU87d0RBQWVDLFNBQVE7a0VBQ2xKLDRFQUFDQzs0REFBS0MsZUFBYzs0REFBUUMsZ0JBQWU7NERBQVFDLGFBQWE7NERBQUtDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVFqRiw4REFBQ2lCO2dDQUNDWCxLQUFLMUY7Z0NBQ0xzQyxNQUFLO2dDQUNMZ0UsUUFBUTtnQ0FDUkMsUUFBTztnQ0FDUFosVUFBVW5FO2dDQUNWbUQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXBCO0dBbmR3Qm5HO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9JbnB1dEJhci50c3g/Y2FjNyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VSZWYsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFNlbmQsIFBhcGVyY2xpcCwgTWljLCBTcXVhcmUsIEFycm93VXAsIEFycm93RG93biwgQm90LCBaYXAsIFNldHRpbmdzIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCBhdHRhY2htZW50U2VydmljZSBmcm9tICdAL2xpYi9zZXJ2aWNlcy9hdHRhY2htZW50U2VydmljZSc7XG5pbXBvcnQgeyBBdHRhY2htZW50TWV0YWRhdGEgfSBmcm9tICdAL2xpYi90eXBlcy9jaGF0JztcblxuaW50ZXJmYWNlIEF0dGFjaG1lbnQge1xuICBpZDogc3RyaW5nO1xuICBmaWxlbmFtZTogc3RyaW5nO1xuICB0eXBlOiAnaW1hZ2UnIHwgJ2RvY3VtZW50JztcbiAgZmlsZTogRmlsZTtcbn1cblxuaW50ZXJmYWNlIElucHV0QmFyUHJvcHMge1xuICBtZXNzYWdlOiBzdHJpbmc7XG4gIHNldE1lc3NhZ2U6IChtZXNzYWdlOiBzdHJpbmcpID0+IHZvaWQ7XG4gIG9uU2VuZE1lc3NhZ2U6IChhdHRhY2htZW50cz86IGltcG9ydCgnQC9saWIvdHlwZXMvY2hhdCcpLkF0dGFjaG1lbnRNZXRhZGF0YVtdLCB3ZWJTZWFyY2hFbmFibGVkPzogYm9vbGVhbikgPT4gdm9pZDtcbiAgaXNMb2FkaW5nOiBib29sZWFuO1xuICBzZWxlY3RlZE1vZGVsOiBzdHJpbmc7XG4gIG9uTW9kZWxDaGFuZ2U6IChtb2RlbDogc3RyaW5nKSA9PiB2b2lkO1xuICBvblNjcm9sbFRvVG9wPzogKCkgPT4gdm9pZDtcbiAgb25TY3JvbGxUb0JvdHRvbT86ICgpID0+IHZvaWQ7XG4gIGlzU3RyZWFtaW5nPzogYm9vbGVhbjtcbiAgb25DYW5jZWxTdHJlYW1pbmc/OiAoKSA9PiB2b2lkO1xuICBvbk9wZW5Nb2RlbE1vZGFsPzogKCkgPT4gdm9pZDtcbiAgdXNlcm5hbWU/OiBzdHJpbmc7XG4gIGNoYXRJZD86IHN0cmluZztcbiAgYWN0aXZlQXR0YWNobWVudHNDb3VudD86IG51bWJlcjsgLy8gTsO6bWVybyBkZSBhbmV4b3MgYXRpdm9zIG5vIGNoYXRcbn1cblxuY29uc3QgQUlfTU9ERUxTID0gW1xuICB7IGlkOiAnZ3B0LTQuMS1uYW5vJywgbmFtZTogJ0dQVC00LjEgTmFubycsIGRlc2NyaXB0aW9uOiAnUsOhcGlkbyBlIGVmaWNpZW50ZScgfSxcbiAgeyBpZDogJ2dwdC00LXR1cmJvJywgbmFtZTogJ0dQVC00IFR1cmJvJywgZGVzY3JpcHRpb246ICdNYWlzIHBvZGVyb3NvJyB9LFxuICB7IGlkOiAnY2xhdWRlLTMtc29ubmV0JywgbmFtZTogJ0NsYXVkZSAzIFNvbm5ldCcsIGRlc2NyaXB0aW9uOiAnQ3JpYXRpdm8gZSBwcmVjaXNvJyB9LFxuICB7IGlkOiAnZ2VtaW5pLXBybycsIG5hbWU6ICdHZW1pbmkgUHJvJywgZGVzY3JpcHRpb246ICdNdWx0aW1vZGFsJyB9LFxuXTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSW5wdXRCYXIoe1xuICBtZXNzYWdlLFxuICBzZXRNZXNzYWdlLFxuICBvblNlbmRNZXNzYWdlLFxuICBpc0xvYWRpbmcsXG4gIHNlbGVjdGVkTW9kZWwsXG4gIG9uTW9kZWxDaGFuZ2UsXG4gIG9uU2Nyb2xsVG9Ub3AsXG4gIG9uU2Nyb2xsVG9Cb3R0b20sXG4gIGlzU3RyZWFtaW5nID0gZmFsc2UsXG4gIG9uQ2FuY2VsU3RyZWFtaW5nLFxuICBvbk9wZW5Nb2RlbE1vZGFsLFxuICB1c2VybmFtZSxcbiAgY2hhdElkLFxuICBhY3RpdmVBdHRhY2htZW50c0NvdW50ID0gMFxufTogSW5wdXRCYXJQcm9wcykge1xuXG4gIGNvbnN0IFt3ZWJTZWFyY2hFbmFibGVkLCBzZXRXZWJTZWFyY2hFbmFibGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2F0dGFjaG1lbnRzLCBzZXRBdHRhY2htZW50c10gPSB1c2VTdGF0ZTxBdHRhY2htZW50W10+KFtdKTtcbiAgY29uc3QgW3Byb2Nlc3NlZEF0dGFjaG1lbnRzLCBzZXRQcm9jZXNzZWRBdHRhY2htZW50c10gPSB1c2VTdGF0ZTxBdHRhY2htZW50TWV0YWRhdGFbXT4oW10pO1xuICBjb25zdCBbaXNVcGxvYWRpbmcsIHNldElzVXBsb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgdGV4dGFyZWFSZWYgPSB1c2VSZWY8SFRNTFRleHRBcmVhRWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IGZpbGVJbnB1dFJlZiA9IHVzZVJlZjxIVE1MSW5wdXRFbGVtZW50PihudWxsKTtcbiAgY29uc3QgYWRqdXN0SGVpZ2h0VGltZW91dFJlZiA9IHVzZVJlZjxOb2RlSlMuVGltZW91dCB8IG51bGw+KG51bGwpO1xuXG4gIC8vIENsZWFudXAgdGltZW91dCBvbiB1bm1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlmIChhZGp1c3RIZWlnaHRUaW1lb3V0UmVmLmN1cnJlbnQpIHtcbiAgICAgICAgY2xlYXJUaW1lb3V0KGFkanVzdEhlaWdodFRpbWVvdXRSZWYuY3VycmVudCk7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW10pO1xuXG4gIC8vIEFkanVzdCB0ZXh0YXJlYSBoZWlnaHQgd2hlbiBtZXNzYWdlIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBhZGp1c3RUZXh0YXJlYUhlaWdodCgpO1xuICB9LCBbbWVzc2FnZV0pO1xuXG4gIGNvbnN0IGhhbmRsZUtleVByZXNzID0gKGU6IFJlYWN0LktleWJvYXJkRXZlbnQpID0+IHtcbiAgICBpZiAoZS5rZXkgPT09ICdFbnRlcicgJiYgIWUuc2hpZnRLZXkpIHtcbiAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgIGhhbmRsZVNlbmQoKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlSW5wdXRDaGFuZ2UgPSAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTFRleHRBcmVhRWxlbWVudD4pID0+IHtcbiAgICBjb25zdCB2YWx1ZSA9IGUudGFyZ2V0LnZhbHVlO1xuICAgIHNldE1lc3NhZ2UodmFsdWUpO1xuXG4gICAgLy8gQWp1c3RhciBhbHR1cmEgaW1lZGlhdGFtZW50ZSBwYXJhIGV2aXRhciBmbGlja2VyaW5nXG4gICAgcmVxdWVzdEFuaW1hdGlvbkZyYW1lKCgpID0+IHtcbiAgICAgIGFkanVzdFRleHRhcmVhSGVpZ2h0KCk7XG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2VuZCA9ICgpID0+IHtcbiAgICBjb25zb2xlLmxvZygnPT09IERFQlVHOiBURU5UQVRJVkEgREUgRU5WSU8gPT09Jyk7XG4gICAgY29uc29sZS5sb2coJ01lbnNhZ2VtOicsIG1lc3NhZ2UpO1xuICAgIGNvbnNvbGUubG9nKCdBbmV4b3MgbG9jYWlzOicsIGF0dGFjaG1lbnRzLmxlbmd0aCk7XG4gICAgY29uc29sZS5sb2coJ0FuZXhvcyBwcm9jZXNzYWRvczonLCBwcm9jZXNzZWRBdHRhY2htZW50cy5sZW5ndGgpO1xuICAgIGNvbnNvbGUubG9nKCdpc0xvYWRpbmc6JywgaXNMb2FkaW5nKTtcbiAgICBjb25zb2xlLmxvZygnaXNVcGxvYWRpbmc6JywgaXNVcGxvYWRpbmcpO1xuXG4gICAgY29uc3QgaGFzTWVzc2FnZSA9IG1lc3NhZ2U/LnRyaW0oKTtcbiAgICBjb25zdCBoYXNBdHRhY2htZW50cyA9IGF0dGFjaG1lbnRzLmxlbmd0aCA+IDA7XG4gICAgY29uc3QgaGFzUHJvY2Vzc2VkQXR0YWNobWVudHMgPSBwcm9jZXNzZWRBdHRhY2htZW50cy5sZW5ndGggPiAwO1xuXG4gICAgY29uc29sZS5sb2coJ2hhc01lc3NhZ2U6JywgaGFzTWVzc2FnZSk7XG4gICAgY29uc29sZS5sb2coJ2hhc0F0dGFjaG1lbnRzOicsIGhhc0F0dGFjaG1lbnRzKTtcbiAgICBjb25zb2xlLmxvZygnaGFzUHJvY2Vzc2VkQXR0YWNobWVudHM6JywgaGFzUHJvY2Vzc2VkQXR0YWNobWVudHMpO1xuXG4gICAgaWYgKChoYXNNZXNzYWdlIHx8IGhhc1Byb2Nlc3NlZEF0dGFjaG1lbnRzKSAmJiAhaXNMb2FkaW5nICYmICFpc1VwbG9hZGluZykge1xuICAgICAgY29uc29sZS5sb2coJ+KchSBDb25kacOnw7VlcyBhdGVuZGlkYXMsIGVudmlhbmRvIG1lbnNhZ2VtLi4uJyk7XG4gICAgICBjb25zb2xlLmxvZygnQW5leG9zIHByb2Nlc3NhZG9zIGRldGFsaGVzOicsIHByb2Nlc3NlZEF0dGFjaG1lbnRzKTtcbiAgICAgIGNvbnNvbGUubG9nKCdXZWIgU2VhcmNoIEVuYWJsZWQ6Jywgd2ViU2VhcmNoRW5hYmxlZCk7XG5cbiAgICAgIG9uU2VuZE1lc3NhZ2UocHJvY2Vzc2VkQXR0YWNobWVudHMsIHdlYlNlYXJjaEVuYWJsZWQpO1xuICAgICAgLy8gTGltcGFyIGFuZXhvcyBhcMOzcyBlbnZpb1xuICAgICAgc2V0QXR0YWNobWVudHMoW10pO1xuICAgICAgc2V0UHJvY2Vzc2VkQXR0YWNobWVudHMoW10pO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmxvZygn4p2MIENvbmRpw6fDtWVzIG7Do28gYXRlbmRpZGFzIHBhcmEgZW52aW8nKTtcbiAgICAgIGlmICghaGFzTWVzc2FnZSAmJiAhaGFzUHJvY2Vzc2VkQXR0YWNobWVudHMpIHtcbiAgICAgICAgY29uc29sZS5sb2coJy0gU2VtIG1lbnNhZ2VtIGUgc2VtIGFuZXhvcyBwcm9jZXNzYWRvcycpO1xuICAgICAgfVxuICAgICAgaWYgKGlzTG9hZGluZykge1xuICAgICAgICBjb25zb2xlLmxvZygnLSBTaXN0ZW1hIGVzdMOhIGNhcnJlZ2FuZG8nKTtcbiAgICAgIH1cbiAgICAgIGlmIChpc1VwbG9hZGluZykge1xuICAgICAgICBjb25zb2xlLmxvZygnLSBVcGxvYWQgZW0gYW5kYW1lbnRvJyk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUF0dGFjaG1lbnQgPSAoKSA9PiB7XG4gICAgaWYgKGZpbGVJbnB1dFJlZi5jdXJyZW50KSB7XG4gICAgICBmaWxlSW5wdXRSZWYuY3VycmVudC5jbGljaygpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVGaWxlU2VsZWN0ID0gYXN5bmMgKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgZmlsZXMgPSBlLnRhcmdldC5maWxlcztcbiAgICBjb25zb2xlLmxvZygnPT09IERFQlVHOiBTRUxFw4fDg08gREUgQVJRVUlWT1MgPT09Jyk7XG4gICAgY29uc29sZS5sb2coJ0ZpbGVzOicsIGZpbGVzKTtcbiAgICBjb25zb2xlLmxvZygnVXNlcm5hbWU6JywgdXNlcm5hbWUpO1xuICAgIGNvbnNvbGUubG9nKCdDaGF0SWQ6JywgY2hhdElkKTtcblxuICAgIGlmICghZmlsZXMgfHwgZmlsZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICBjb25zb2xlLmxvZygn4p2MIE5lbmh1bSBhcnF1aXZvIHNlbGVjaW9uYWRvJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgaWYgKCF1c2VybmFtZSkge1xuICAgICAgY29uc29sZS5sb2coJ+KdjCBVc2VybmFtZSBuw6NvIGRpc3BvbsOtdmVsJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gU2UgbsOjbyBow6EgY2hhdElkLCBjcmlhciB1bSB0ZW1wb3LDoXJpbyBwYXJhIG8gdXBsb2FkXG4gICAgLy8gTyBjaGF0IHJlYWwgc2Vyw6EgY3JpYWRvIHF1YW5kbyBhIG1lbnNhZ2VtIGZvciBlbnZpYWRhXG4gICAgbGV0IGNoYXRJZEZvclVwbG9hZCA9IGNoYXRJZDtcbiAgICBpZiAoIWNoYXRJZEZvclVwbG9hZCkge1xuICAgICAgY29uc3QgdGltZXN0YW1wID0gRGF0ZS5ub3coKTtcbiAgICAgIGNvbnN0IHJhbmRvbSA9IE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLCA4KTtcbiAgICAgIGNoYXRJZEZvclVwbG9hZCA9IGB0ZW1wX2NoYXRfJHt0aW1lc3RhbXB9XyR7cmFuZG9tfWA7XG4gICAgICBjb25zb2xlLmxvZyhg8J+GlCBDcmlhbmRvIGNoYXRJZCB0ZW1wb3LDoXJpbyBwYXJhIHVwbG9hZDogJHtjaGF0SWRGb3JVcGxvYWR9YCk7XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coYOKchSBQcm9jZXNzYW5kbyAke2ZpbGVzLmxlbmd0aH0gYXJxdWl2byhzKWApO1xuICAgIHNldElzVXBsb2FkaW5nKHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIFByaW1laXJvLCBhZGljaW9uYXIgYXJxdWl2b3MgbG9jYWxtZW50ZSBwYXJhIHByZXZpZXdcbiAgICAgIGNvbnN0IGxvY2FsQXR0YWNobWVudHM6IEF0dGFjaG1lbnRbXSA9IFtdO1xuICAgICAgZm9yIChjb25zdCBmaWxlIG9mIEFycmF5LmZyb20oZmlsZXMpKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKGDwn5OBIFByb2Nlc3NhbmRvIGFycXVpdm86ICR7ZmlsZS5uYW1lfSAoJHtmaWxlLnR5cGV9LCAke2ZpbGUuc2l6ZX0gYnl0ZXMpYCk7XG5cbiAgICAgICAgY29uc3QgYXR0YWNobWVudDogQXR0YWNobWVudCA9IHtcbiAgICAgICAgICBpZDogRGF0ZS5ub3coKS50b1N0cmluZygpICsgTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyaW5nKDIsIDkpLFxuICAgICAgICAgIGZpbGVuYW1lOiBmaWxlLm5hbWUsXG4gICAgICAgICAgdHlwZTogZmlsZS50eXBlLnN0YXJ0c1dpdGgoJ2ltYWdlLycpID8gJ2ltYWdlJyA6ICdkb2N1bWVudCcsXG4gICAgICAgICAgZmlsZVxuICAgICAgICB9O1xuICAgICAgICBsb2NhbEF0dGFjaG1lbnRzLnB1c2goYXR0YWNobWVudCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKGDwn5OOIEFkaWNpb25hbmRvICR7bG9jYWxBdHRhY2htZW50cy5sZW5ndGh9IGFuZXhvcyBsb2NhaXMgcGFyYSBwcmV2aWV3YCk7XG4gICAgICBzZXRBdHRhY2htZW50cyhwcmV2ID0+IFsuLi5wcmV2LCAuLi5sb2NhbEF0dGFjaG1lbnRzXSk7XG5cbiAgICAgIC8vIEZhemVyIHVwbG9hZCBkb3MgYXJxdWl2b3NcbiAgICAgIGNvbnNvbGUubG9nKCfwn5qAIEluaWNpYW5kbyB1cGxvYWQgZG9zIGFycXVpdm9zLi4uJyk7XG4gICAgICBjb25zdCB1cGxvYWRlZEF0dGFjaG1lbnRzID0gYXdhaXQgYXR0YWNobWVudFNlcnZpY2UudXBsb2FkTXVsdGlwbGVBdHRhY2htZW50cyhcbiAgICAgICAgQXJyYXkuZnJvbShmaWxlcyksXG4gICAgICAgIHVzZXJuYW1lLFxuICAgICAgICBjaGF0SWRGb3JVcGxvYWRcbiAgICAgICk7XG5cbiAgICAgIGNvbnNvbGUubG9nKGDinIUgVXBsb2FkIGNvbmNsdcOtZG8hICR7dXBsb2FkZWRBdHRhY2htZW50cy5sZW5ndGh9IGFycXVpdm9zIHByb2Nlc3NhZG9zYCk7XG4gICAgICBjb25zb2xlLmxvZygnQW5leG9zIHByb2Nlc3NhZG9zOicsIHVwbG9hZGVkQXR0YWNobWVudHMpO1xuXG4gICAgICAvLyBBdHVhbGl6YXIgY29tIG1ldGFkYWRvcyBkb3MgYXJxdWl2b3MgcHJvY2Vzc2Fkb3NcbiAgICAgIGNvbnN0IHByb2Nlc3NlZE1ldGFkYXRhID0gdXBsb2FkZWRBdHRhY2htZW50cy5tYXAoYXR0ID0+IGF0dC5tZXRhZGF0YSk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TiyBNZXRhZGFkb3MgZG9zIGFuZXhvczonLCBwcm9jZXNzZWRNZXRhZGF0YSk7XG5cbiAgICAgIHNldFByb2Nlc3NlZEF0dGFjaG1lbnRzKHByZXYgPT4gW1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICAuLi5wcm9jZXNzZWRNZXRhZGF0YVxuICAgICAgXSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm8gYW8gcHJvY2Vzc2FyIGFycXVpdm9zOicsIGVycm9yKTtcbiAgICAgIC8vIFJlbW92ZXIgYW5leG9zIHF1ZSBmYWxoYXJhbVxuICAgICAgc2V0QXR0YWNobWVudHMocHJldiA9PiBwcmV2LmZpbHRlcihhdHQgPT5cbiAgICAgICAgIUFycmF5LmZyb20oZmlsZXMpLnNvbWUoZmlsZSA9PiBmaWxlLm5hbWUgPT09IGF0dC5maWxlbmFtZSlcbiAgICAgICkpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1VwbG9hZGluZyhmYWxzZSk7XG4gICAgICBpZiAoZmlsZUlucHV0UmVmLmN1cnJlbnQpIHtcbiAgICAgICAgZmlsZUlucHV0UmVmLmN1cnJlbnQudmFsdWUgPSAnJztcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgY29uc3QgcmVtb3ZlQXR0YWNobWVudCA9IChpZDogc3RyaW5nKSA9PiB7XG4gICAgLy8gRW5jb250cmFyIG8gYW5leG8gcGFyYSBvYnRlciBvIGZpbGVuYW1lXG4gICAgY29uc3QgYXR0YWNobWVudCA9IGF0dGFjaG1lbnRzLmZpbmQoYXR0ID0+IGF0dC5pZCA9PT0gaWQpO1xuICAgIGlmIChhdHRhY2htZW50KSB7XG4gICAgICAvLyBSZW1vdmVyIGRvIGVzdGFkbyBsb2NhbFxuICAgICAgc2V0QXR0YWNobWVudHMocHJldiA9PiBwcmV2LmZpbHRlcihhdHQgPT4gYXR0LmlkICE9PSBpZCkpO1xuICAgICAgLy8gUmVtb3ZlciBkb3MgYW5leG9zIHByb2Nlc3NhZG9zIHRhbWLDqW1cbiAgICAgIHNldFByb2Nlc3NlZEF0dGFjaG1lbnRzKHByZXYgPT4gcHJldi5maWx0ZXIoYXR0ID0+IGF0dC5maWxlbmFtZSAhPT0gYXR0YWNobWVudC5maWxlbmFtZSkpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVXZWJTZWFyY2ggPSAoKSA9PiB7XG4gICAgc2V0V2ViU2VhcmNoRW5hYmxlZCghd2ViU2VhcmNoRW5hYmxlZCk7XG4gIH07XG5cbiAgY29uc3QgYWRqdXN0VGV4dGFyZWFIZWlnaHQgPSAoKSA9PiB7XG4gICAgY29uc3QgdGV4dGFyZWEgPSB0ZXh0YXJlYVJlZi5jdXJyZW50O1xuICAgIGlmICghdGV4dGFyZWEpIHJldHVybjtcblxuICAgIC8vIFN0b3JlIGN1cnJlbnQgc2Nyb2xsIHBvc2l0aW9uIHRvIHByZXZlbnQganVtcGluZ1xuICAgIGNvbnN0IHNjcm9sbFRvcCA9IHRleHRhcmVhLnNjcm9sbFRvcDtcblxuICAgIC8vIFRlbXBvcmFyaWx5IHNldCBoZWlnaHQgdG8gYXV0byB0byBnZXQgYWNjdXJhdGUgc2Nyb2xsSGVpZ2h0XG4gICAgdGV4dGFyZWEuc3R5bGUuaGVpZ2h0ID0gJ2F1dG8nO1xuXG4gICAgLy8gQ2FsY3VsYXRlIG5ldyBoZWlnaHQgd2l0aCBwcm9wZXIgY29uc3RyYWludHNcbiAgICBjb25zdCBzY3JvbGxIZWlnaHQgPSB0ZXh0YXJlYS5zY3JvbGxIZWlnaHQ7XG4gICAgY29uc3QgbWluSGVpZ2h0ID0gNDQ7IC8vIE1pbmltdW0gaGVpZ2h0IGluIHBpeGVsc1xuICAgIGNvbnN0IG1heEhlaWdodCA9IDEyMDsgLy8gTWF4aW11bSBoZWlnaHQgaW4gcGl4ZWxzXG5cbiAgICAvLyBTZXQgdGhlIG5ldyBoZWlnaHQgd2l0aGluIGJvdW5kc1xuICAgIGNvbnN0IG5ld0hlaWdodCA9IE1hdGgubWF4KG1pbkhlaWdodCwgTWF0aC5taW4oc2Nyb2xsSGVpZ2h0LCBtYXhIZWlnaHQpKTtcbiAgICB0ZXh0YXJlYS5zdHlsZS5oZWlnaHQgPSBuZXdIZWlnaHQgKyAncHgnO1xuXG4gICAgLy8gSGFuZGxlIG92ZXJmbG93IGFuZCBzY3JvbGxpbmdcbiAgICBpZiAoc2Nyb2xsSGVpZ2h0ID4gbWF4SGVpZ2h0KSB7XG4gICAgICB0ZXh0YXJlYS5zdHlsZS5vdmVyZmxvd1kgPSAnYXV0byc7XG4gICAgICAvLyBSZXN0b3JlIHNjcm9sbCBwb3NpdGlvbiBpZiBjb250ZW50IHdhcyBzY3JvbGxlZFxuICAgICAgaWYgKHNjcm9sbFRvcCA+IDApIHtcbiAgICAgICAgdGV4dGFyZWEuc2Nyb2xsVG9wID0gc2Nyb2xsVG9wO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICB0ZXh0YXJlYS5zdHlsZS5vdmVyZmxvd1kgPSAnaGlkZGVuJztcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaXNXZWJTZWFyY2hFbmFibGVkID0gKCkgPT4ge1xuICAgIC8vIFdlYiBzZWFyY2ggZXN0w6EgZGlzcG9uw612ZWwgcGFyYSB0b2RvcyBvcyBtb2RlbG9zIHZpYSBPcGVuUm91dGVyIHBsdWdpbnNcbiAgICAvLyBBcGVuYXMgb2N1bHRhciBwYXJhIG1vZGVsb3MgbG9jYWlzIG91IGVzcGVjw61maWNvcyBxdWUgbsOjbyBzdXBvcnRhbVxuICAgIGNvbnN0IHVuc3VwcG9ydGVkTW9kZWxzID0gWydsb2NhbC8nLCAnb2ZmbGluZS8nXTtcbiAgICByZXR1cm4gIXVuc3VwcG9ydGVkTW9kZWxzLnNvbWUocHJlZml4ID0+IHNlbGVjdGVkTW9kZWwuc3RhcnRzV2l0aChwcmVmaXgpKTtcbiAgfTtcblxuICBjb25zdCBjdXJyZW50TW9kZWwgPSBBSV9NT0RFTFMuZmluZChtb2RlbCA9PiBtb2RlbC5pZCA9PT0gc2VsZWN0ZWRNb2RlbCk7XG4gIGNvbnN0IG1vZGVsTmFtZSA9IGN1cnJlbnRNb2RlbCA/IGN1cnJlbnRNb2RlbC5uYW1lIDogc2VsZWN0ZWRNb2RlbDtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgIHsvKiBGbG9hdGluZyBlZmZlY3Qgc2hhZG93IC0gY3JlYXRlcyBkZXB0aCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQteC0wIC10b3AtOCBoLTggYmctZ3JhZGllbnQtdG8tdCBmcm9tLWJsdWUtOTUwLzMwIHZpYS1ibHVlLTk1MC8xMCB0by10cmFuc3BhcmVudCBwb2ludGVyLWV2ZW50cy1ub25lIGJsdXItc21cIj48L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgc206cC00IGxnOnAtNiBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS05NTAvOTggdmlhLWJsdWUtOTAwLzk4IHRvLWJsdWUtOTUwLzk4IGJhY2tkcm9wLWJsdXIteGwgcmVsYXRpdmUgc2hhZG93LTJ4bCBzaGFkb3ctYmx1ZS05MDAvNjBcIj5cbiAgICAgICAgey8qIEVmZWl0byBkZSBicmlsaG8gc3V0aWwgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAvOCB2aWEtdHJhbnNwYXJlbnQgdG8tY3lhbi01MDAvOCBwb2ludGVyLWV2ZW50cy1ub25lXCI+PC9kaXY+XG5cbiAgICAgICAgey8qIEZsb2F0aW5nIGdsb3cgZWZmZWN0IC0gaW5uZXIgZ2xvdyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLXQgZnJvbS1ibHVlLTYwMC8xNSB2aWEtYmx1ZS01MDAvNSB0by10cmFuc3BhcmVudCBwb2ludGVyLWV2ZW50cy1ub25lXCI+PC9kaXY+XG5cbiAgICAgICAgey8qIFRvcCBoaWdobGlnaHQgZm9yIGZsb2F0aW5nIGVmZmVjdCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMCBpbnNldC14LTAgaC1weCBiZy1ncmFkaWVudC10by1yIGZyb20tdHJhbnNwYXJlbnQgdmlhLWJsdWUtNDAwLzMwIHRvLXRyYW5zcGFyZW50IHBvaW50ZXItZXZlbnRzLW5vbmVcIj48L2Rpdj5cbiAgICAgIFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0byByZWxhdGl2ZSB6LTEwXCI+XG4gICAgICAgIHsvKiBBdHRhY2htZW50IFByZXZpZXcgKi99XG4gICAgICAgIHthdHRhY2htZW50cy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTMgc206bWItNCBwLTMgc206cC00IGJnLWJsdWUtOTAwLzMwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC1sZyBzbTpyb3VuZGVkLXhsIGJvcmRlciBib3JkZXItYmx1ZS02MDAvMzBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTIgc206Z2FwLTNcIj5cbiAgICAgICAgICAgICAge2F0dGFjaG1lbnRzLm1hcCgoYXR0YWNobWVudCkgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGtleT17YXR0YWNobWVudC5pZH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBzbTpzcGFjZS14LTMgYmctYmx1ZS04MDAvNDAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLWxnIHB4LTIgc206cHgtMyBweS0xLjUgc206cHktMiBib3JkZXIgYm9yZGVyLWJsdWUtNjAwLzIwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7YXR0YWNobWVudC50eXBlID09PSAnaW1hZ2UnID8gKFxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMy41IGgtMy41IHNtOnctNCBzbTpoLTQgdGV4dC1ibHVlLTQwMCBmbGV4LXNocmluay0wXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTQgMTZsNC41ODYtNC41ODZhMiAyIDAgMDEyLjgyOCAwTDE2IDE2bS0yLTJsMS41ODYtMS41ODZhMiAyIDAgMDEyLjgyOCAwTDIwIDE0bS02LTZoLjAxTTYgMjBoMTJhMiAyIDAgMDAyLTJWNmEyIDIgMCAwMC0yLTJINmEyIDIgMCAwMC0yIDJ2MTJhMiAyIDAgMDAyIDJ6XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMy41IGgtMy41IHNtOnctNCBzbTpoLTQgdGV4dC1yZWQtNDAwIGZsZXgtc2hyaW5rLTBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNOSAxMmg2bS02IDRoNm0yIDVIN2EyIDIgMCAwMS0yLTJWNWEyIDIgMCAwMTItMmg1LjU4NmExIDEgMCAwMS43MDcuMjkzbDUuNDE0IDUuNDE0YTEgMSAwIDAxLjI5My43MDdWMTlhMiAyIDAgMDEtMiAyelwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgc206dGV4dC1zbSB0ZXh0LWJsdWUtMTAwIHRydW5jYXRlIG1heC13LTIwIHNtOm1heC13LTMyIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgIHthdHRhY2htZW50LmZpbGVuYW1lfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVBdHRhY2htZW50KGF0dGFjaG1lbnQuaWQpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMzAwIGhvdmVyOnRleHQtcmVkLTQwMCB0cmFuc2l0aW9uLWNvbG9ycyBwLTAuNSBzbTpwLTEgcm91bmRlZC1mdWxsIGhvdmVyOmJnLXJlZC01MDAvMjAgZmxleC1zaHJpbmstMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy0zLjUgaC0zLjUgc206dy00IHNtOmgtNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk02IDE4TDE4IDZNNiA2bDEyIDEyXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogU2VsZWN0ZWQgTW9kZWwgYW5kIEF0dGFjaG1lbnRzIEluZGljYXRvcnMgKi99XG4gICAgICAgIHsoc2VsZWN0ZWRNb2RlbCB8fCBhY3RpdmVBdHRhY2htZW50c0NvdW50ID4gMCkgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LXN0YXJ0IHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAge3NlbGVjdGVkTW9kZWwgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsdWUtOTAwLzkwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC1sZyBweC0zIHB5LTEgYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8zMCBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS0zMDBcIj5Nb2RlbG86IDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtY3lhbi0zMDAgZm9udC1tZWRpdW1cIj57bW9kZWxOYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7YWN0aXZlQXR0YWNobWVudHNDb3VudCA+IDAgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTkwMC85MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtbGcgcHgtMyBweS0xIGJvcmRlciBib3JkZXItZ3JlZW4tNjAwLzMwIHNoYWRvdy1sZ1wiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmVlbi0zMDBcIj5BbmV4b3M6IDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JlZW4tMjAwIGZvbnQtbWVkaXVtXCI+e2FjdGl2ZUF0dGFjaG1lbnRzQ291bnR9PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWVuZCBzcGFjZS14LTIgc206c3BhY2UteC0zXCI+XG4gICAgICAgICAgey8qIEZsb2F0aW5nIGlucHV0IGNvbnRhaW5lciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSByZWxhdGl2ZSBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS05MDAvODAgdG8tYmx1ZS04MDAvODAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLXhsIHNtOnJvdW5kZWQtMnhsIGJvcmRlciBib3JkZXItYmx1ZS02MDAvMzAgc2hhZG93LXhsIHNoYWRvdy1ibHVlLTkwMC81MFwiPlxuICAgICAgICAgICAgey8qIE5ldW1vcnBoaWMgZ2xvdyBlZmZlY3QgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgcm91bmRlZC14bCBzbTpyb3VuZGVkLTJ4bCBzaGFkb3ctW2luc2V0XzBfMXB4XzBfcmdiYSg1OSwxMzAsMjQ2LDAuMSksMF8wXzIwcHhfcmdiYSg1OSwxMzAsMjQ2LDAuMTUpXVwiPjwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTAgZmxleCBpdGVtcy1lbmQgc3BhY2UteC0yIHNtOnNwYWNlLXgtMyBwLTMgc206cC00XCI+XG4gICAgICAgICAgICAgIHsvKiBMZWZ0IHNpZGUgYnV0dG9ucyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTEgc206c3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgey8qIE1vZGVsIFNlbGVjdGlvbiBCdXR0b24gKi99XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17b25PcGVuTW9kZWxNb2RhbH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiBzbTpwLTIuNSByb3VuZGVkLWxnIHNtOnJvdW5kZWQteGwgYmctYmx1ZS04MDAvNDAgaG92ZXI6YmctYmx1ZS03MDAvNTAgYmFja2Ryb3AtYmx1ci1zbSB0ZXh0LWJsdWUtMzAwIGhvdmVyOnRleHQtYmx1ZS0yMDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOnNoYWRvdy1sZyBob3ZlcjpzaGFkb3ctYmx1ZS01MDAvMjAgYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8yMCBob3ZlcjpzY2FsZS0xMDVcIlxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJTZWxlY2lvbmFyIG1vZGVsb1wiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHNtOnctNSBzbTpoLTVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTkuNzUgMTdMOSAyMGwtMSAxaDhsLTEtMS0uNzUtM00zIDEzaDE4TTUgMTdoMTRhMiAyIDAgMDAyLTJWNWEyIDIgMCAwMC0yLTJINWEyIDIgMCAwMC0yIDJ2MTBhMiAyIDAgMDAyIDJ6XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgey8qIEF0dGFjaG1lbnQgQnV0dG9uICovfVxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUF0dGFjaG1lbnR9XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNVcGxvYWRpbmd9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIuNSByb3VuZGVkLXhsIGJnLWJsdWUtODAwLzQwIGhvdmVyOmJnLWJsdWUtNzAwLzUwIGJhY2tkcm9wLWJsdXItc20gdGV4dC1ibHVlLTMwMCBob3Zlcjp0ZXh0LWJsdWUtMjAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBob3ZlcjpzaGFkb3ctbGcgaG92ZXI6c2hhZG93LWJsdWUtNTAwLzIwIGJvcmRlciBib3JkZXItYmx1ZS02MDAvMjAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgaG92ZXI6c2NhbGUtMTA1XCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlPVwiQW5leGFyIGFycXVpdm9cIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtpc1VwbG9hZGluZyA/IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNSB3LTUgYm9yZGVyLXQtMiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTQwMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTE1LjE3MiA3bC02LjU4NiA2LjU4NmEyIDIgMCAxMDIuODI4IDIuODI4bDYuNDE0LTYuNTg2YTQgNCAwIDAwLTUuNjU2LTUuNjU2bC02LjQxNSA2LjU4NWE2IDYgMCAxMDguNDg2IDguNDg2TDIwLjUgMTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICB7LyogV2ViIFNlYXJjaCBCdXR0b24gKi99XG4gICAgICAgICAgICAgICAge2lzV2ViU2VhcmNoRW5hYmxlZCgpICYmIChcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlV2ViU2VhcmNofVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTIuNSByb3VuZGVkLXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBob3ZlcjpzaGFkb3ctbGcgYm9yZGVyIGhvdmVyOnNjYWxlLTEwNSBiYWNrZHJvcC1ibHVyLXNtICR7XG4gICAgICAgICAgICAgICAgICAgICAgd2ViU2VhcmNoRW5hYmxlZFxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctY3lhbi02MDAvMzAgaG92ZXI6YmctY3lhbi02MDAvNDAgdGV4dC1jeWFuLTIwMCBob3Zlcjp0ZXh0LWN5YW4tMTAwIGJvcmRlci1jeWFuLTUwMC81MCBzaGFkb3ctbGcgc2hhZG93LWN5YW4tNTAwLzIwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctYmx1ZS04MDAvNDAgaG92ZXI6YmctYmx1ZS03MDAvNTAgdGV4dC1ibHVlLTMwMCBob3Zlcjp0ZXh0LWJsdWUtMjAwIGJvcmRlci1ibHVlLTYwMC8yMCBob3ZlcjpzaGFkb3ctYmx1ZS01MDAvMjAnXG4gICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICB0aXRsZT17YEJ1c2NhIG5hIHdlYiAtICR7d2ViU2VhcmNoRW5hYmxlZCA/ICdBdGl2YWRhJyA6ICdEZXNhdGl2YWRhJ31gfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTVcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMjEuNzIxIDEyLjc1MmE5LjcxMSA5LjcxMSAwIDAgMC0uOTQ1LTUuMDAzIDEyLjc1NCAxMi43NTQgMCAwIDEtNC4zMzkgMi43MDggMTguOTkxIDE4Ljk5MSAwIDAgMS0uMjE0IDQuNzcyIDE3LjE2NSAxNy4xNjUgMCAwIDAgNS40OTgtMi40NzdaTTE0LjYzNCAxNS41NWExNy4zMjQgMTcuMzI0IDAgMCAwIC4zMzItNC42NDdjLS45NTIuMjI3LTEuOTQ1LjM0Ny0yLjk2Ni4zNDctMS4wMjEgMC0yLjAxNC0uMTItMi45NjYtLjM0N2ExNy41MTUgMTcuNTE1IDAgMCAwIC4zMzIgNC42NDcgMTcuMzg1IDE3LjM4NSAwIDAgMCA1LjI2OCAwWk05Ljc3MiAxNy4xMTlhMTguOTYzIDE4Ljk2MyAwIDAgMCA0LjQ1NiAwQTE3LjE4MiAxNy4xODIgMCAwIDEgMTIgMjEuNzI0YTE3LjE4IDE3LjE4IDAgMCAxLTIuMjI4LTQuNjA1Wk03Ljc3NyAxNS4yM2ExOC44NyAxOC44NyAwIDAgMS0uMjE0LTQuNzc0IDEyLjc1MyAxMi43NTMgMCAwIDEtNC4zNC0yLjcwOCA5LjcxMSA5LjcxMSAwIDAgMC0uOTQ0IDUuMDA0IDE3LjE2NSAxNy4xNjUgMCAwIDAgNS40OTggMi40NzdaTTIxLjM1NiAxNC43NTJhOS43NjUgOS43NjUgMCAwIDEtNy40NzggNi44MTcgMTguNjQgMTguNjQgMCAwIDAgMS45ODgtNC43MTggMTguNjI3IDE4LjYyNyAwIDAgMCA1LjQ5LTIuMDk4Wk0yLjY0NCAxNC43NTJjMS42ODIuOTcxIDMuNTMgMS42ODggNS40OSAyLjA5OWExOC42NCAxOC42NCAwIDAgMCAxLjk4OCA0LjcxOCA5Ljc2NSA5Ljc2NSAwIDAgMS03LjQ3OC02LjgxNlpNMTMuODc4IDIuNDNhOS43NTUgOS43NTUgMCAwIDEgNi4xMTYgMy45ODYgMTEuMjY3IDExLjI2NyAwIDAgMS0zLjc0NiAyLjUwNCAxOC42MyAxOC42MyAwIDAgMC0yLjM3LTYuNDlaTTEyIDIuMjc2YTE3LjE1MiAxNy4xNTIgMCAwIDEgMi44MDUgNy4xMjFjLS44OTcuMjMtMS44MzcuMzUzLTIuODA1LjM1My0uOTY4IDAtMS45MDgtLjEyMi0yLjgwNS0uMzUzQTE3LjE1MSAxNy4xNTEgMCAwIDEgMTIgMi4yNzZaTTEwLjEyMiAyLjQzYTE4LjYyOSAxOC42MjkgMCAwIDAtMi4zNyA2LjQ5IDExLjI2NiAxMS4yNjYgMCAwIDEtMy43NDYtMi41MDQgOS43NTQgOS43NTQgMCAwIDEgNi4xMTYtMy45ODVaXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogVGV4dCBJbnB1dCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgIHJlZj17dGV4dGFyZWFSZWZ9XG4gICAgICAgICAgICAgICAgICB2YWx1ZT17bWVzc2FnZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICAgIG9uS2V5RG93bj17aGFuZGxlS2V5UHJlc3N9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctdHJhbnNwYXJlbnQgYm9yZGVyLW5vbmUgcm91bmRlZC14bCBweC00IHB5LTMgdGV4dC1ibHVlLTEwMCBwbGFjZWhvbGRlcjp0ZXh0LWJsdWUtMzAwLzYwIGZvY3VzOm91dGxpbmUtbm9uZSByZXNpemUtbm9uZSB0ZXh0LXNtIHNlbGVjdGlvbjpiZy1ibHVlLTUwMC8zMFwiXG4gICAgICAgICAgICAgICAgICByb3dzPXsxfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJEaWdpdGUgc3VhIG1lbnNhZ2VtIGFxdWkuLi5cIlxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZyB8fCBpc1N0cmVhbWluZ31cbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzQ0cHgnLFxuICAgICAgICAgICAgICAgICAgICBtaW5IZWlnaHQ6ICc0NHB4JyxcbiAgICAgICAgICAgICAgICAgICAgbWF4SGVpZ2h0OiAnMTIwcHgnLFxuICAgICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAnMS41JyxcbiAgICAgICAgICAgICAgICAgICAgd29yZFdyYXA6ICdicmVhay13b3JkJyxcbiAgICAgICAgICAgICAgICAgICAgd2hpdGVTcGFjZTogJ3ByZS13cmFwJyxcbiAgICAgICAgICAgICAgICAgICAgb3ZlcmZsb3dZOiAnaGlkZGVuJyxcbiAgICAgICAgICAgICAgICAgICAgb3ZlcmZsb3dYOiAnaGlkZGVuJyxcbiAgICAgICAgICAgICAgICAgICAgc2Nyb2xsYmFyV2lkdGg6ICd0aGluJ1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogU2VuZC9DYW5jZWwgQnV0dG9uICovfVxuICAgICAgICAgICAgICB7aXNTdHJlYW1pbmcgPyAoXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17b25DYW5jZWxTdHJlYW1pbmd9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTMgcm91bmRlZC14bCBiZy1ncmFkaWVudC10by1yIGZyb20tcmVkLTYwMCB2aWEtcmVkLTUwMCB0by1yZWQtNjAwIGhvdmVyOmZyb20tcmVkLTUwMCBob3Zlcjp2aWEtcmVkLTQwMCBob3Zlcjp0by1yZWQtNTAwIHRleHQtd2hpdGUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgaG92ZXI6c2NhbGUtMTA1IGJvcmRlciBib3JkZXItcmVkLTUwMC8zMFwiXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIlBhcmFyIGdlcmHDp8Ojb1wiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk02IDE4TDE4IDZNNiA2bDEyIDEyXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNlbmR9XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17KCFtZXNzYWdlPy50cmltKCkgJiYgYXR0YWNobWVudHMubGVuZ3RoID09PSAwKSB8fCBpc0xvYWRpbmcgfHwgaXNVcGxvYWRpbmcgfHwgaXNTdHJlYW1pbmd9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTMgcm91bmRlZC14bCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdmlhLWJsdWUtNTAwIHRvLWN5YW4tNjAwIGhvdmVyOmZyb20tYmx1ZS01MDAgaG92ZXI6dmlhLWJsdWUtNDAwIGhvdmVyOnRvLWN5YW4tNTAwIHRleHQtd2hpdGUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgaG92ZXI6c2NhbGUtMTA1IGRpc2FibGVkOmhvdmVyOnNjYWxlLTEwMCBib3JkZXIgYm9yZGVyLWJsdWUtNTAwLzMwXCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlPVwiRW52aWFyIG1lbnNhZ2VtXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC01IHctNSBib3JkZXItdC0yIGJvcmRlci1iLTIgYm9yZGVyLXdoaXRlXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8c3ZnIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiBjbGFzc05hbWU9XCJoLTUgdy01XCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTE0IDVsNyA3bTAgMGwtNyA3bTctN0gzXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG5cblxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFNjcm9sbCBCdXR0b25zIC0gU2lkZSBieSBzaWRlIG5leHQgdG8gaW5wdXQgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNwYWNlLXktMlwiPlxuICAgICAgICAgICAgey8qIFNjcm9sbCB0byBUb3AgQnV0dG9uICovfVxuICAgICAgICAgICAge29uU2Nyb2xsVG9Ub3AgJiYgKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17b25TY3JvbGxUb1RvcH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCByZWxhdGl2ZSBwLTIuNSByb3VuZGVkLXhsIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS04MDAvNDAgdG8tYmx1ZS05MDAvNDAgaG92ZXI6ZnJvbS1ibHVlLTcwMC81MCBob3Zlcjp0by1ibHVlLTgwMC81MCBiYWNrZHJvcC1ibHVyLXNtIHRleHQtYmx1ZS0zMDAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6c2hhZG93LWxnIGhvdmVyOnNoYWRvdy1ibHVlLTUwMC8zMCBib3JkZXIgYm9yZGVyLWJsdWUtNjAwLzIwIGhvdmVyOmJvcmRlci1ibHVlLTUwMC80MCBob3ZlcjpzY2FsZS0xMDUgYWN0aXZlOnNjYWxlLTk1XCJcbiAgICAgICAgICAgICAgICB0aXRsZT1cIklyIHBhcmEgbyB0b3BvXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHsvKiBHbG93IGVmZmVjdCAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgcm91bmRlZC14bCBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAwLzIwIHRvLWN5YW4tNTAwLzIwIG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwIGJsdXItc21cIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInJlbGF0aXZlIHctNSBoLTUgdHJhbnNmb3JtIGdyb3VwLWhvdmVyOi10cmFuc2xhdGUteS0wLjUgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMjAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezIuNX0gZD1cIk01IDEwbDctN20wIDBsNyA3bS03LTd2MThcIiAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBTY3JvbGwgdG8gQm90dG9tIEJ1dHRvbiAqL31cbiAgICAgICAgICAgIHtvblNjcm9sbFRvQm90dG9tICYmIChcbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29uU2Nyb2xsVG9Cb3R0b219XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgcmVsYXRpdmUgcC0yLjUgcm91bmRlZC14bCBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtODAwLzQwIHRvLWJsdWUtOTAwLzQwIGhvdmVyOmZyb20tYmx1ZS03MDAvNTAgaG92ZXI6dG8tYmx1ZS04MDAvNTAgYmFja2Ryb3AtYmx1ci1zbSB0ZXh0LWJsdWUtMzAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOnNoYWRvdy1sZyBob3ZlcjpzaGFkb3ctYmx1ZS01MDAvMzAgYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8yMCBob3Zlcjpib3JkZXItYmx1ZS01MDAvNDAgaG92ZXI6c2NhbGUtMTA1IGFjdGl2ZTpzY2FsZS05NVwiXG4gICAgICAgICAgICAgICAgdGl0bGU9XCJJciBwYXJhIG8gZmluYWxcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgey8qIEdsb3cgZWZmZWN0ICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCByb3VuZGVkLXhsIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MDAvMjAgdG8tY3lhbi01MDAvMjAgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0zMDAgYmx1ci1zbVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy01IGgtNSB0cmFuc2Zvcm0gZ3JvdXAtaG92ZXI6dHJhbnNsYXRlLXktMC41IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTIwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyLjV9IGQ9XCJNMTkgMTRsLTcgN20wIDBsLTctN203IDdWNFwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEhpZGRlbiBmaWxlIGlucHV0ICovfVxuICAgICAgICA8aW5wdXRcbiAgICAgICAgICByZWY9e2ZpbGVJbnB1dFJlZn1cbiAgICAgICAgICB0eXBlPVwiZmlsZVwiXG4gICAgICAgICAgbXVsdGlwbGVcbiAgICAgICAgICBhY2NlcHQ9XCJpbWFnZS9wbmcsaW1hZ2UvanBlZyxpbWFnZS93ZWJwLGFwcGxpY2F0aW9uL3BkZlwiXG4gICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUZpbGVTZWxlY3R9XG4gICAgICAgICAgY2xhc3NOYW1lPVwiaGlkZGVuXCJcbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiYXR0YWNobWVudFNlcnZpY2UiLCJBSV9NT0RFTFMiLCJpZCIsIm5hbWUiLCJkZXNjcmlwdGlvbiIsIklucHV0QmFyIiwibWVzc2FnZSIsInNldE1lc3NhZ2UiLCJvblNlbmRNZXNzYWdlIiwiaXNMb2FkaW5nIiwic2VsZWN0ZWRNb2RlbCIsIm9uTW9kZWxDaGFuZ2UiLCJvblNjcm9sbFRvVG9wIiwib25TY3JvbGxUb0JvdHRvbSIsImlzU3RyZWFtaW5nIiwib25DYW5jZWxTdHJlYW1pbmciLCJvbk9wZW5Nb2RlbE1vZGFsIiwidXNlcm5hbWUiLCJjaGF0SWQiLCJhY3RpdmVBdHRhY2htZW50c0NvdW50Iiwid2ViU2VhcmNoRW5hYmxlZCIsInNldFdlYlNlYXJjaEVuYWJsZWQiLCJhdHRhY2htZW50cyIsInNldEF0dGFjaG1lbnRzIiwicHJvY2Vzc2VkQXR0YWNobWVudHMiLCJzZXRQcm9jZXNzZWRBdHRhY2htZW50cyIsImlzVXBsb2FkaW5nIiwic2V0SXNVcGxvYWRpbmciLCJ0ZXh0YXJlYVJlZiIsImZpbGVJbnB1dFJlZiIsImFkanVzdEhlaWdodFRpbWVvdXRSZWYiLCJjdXJyZW50IiwiY2xlYXJUaW1lb3V0IiwiYWRqdXN0VGV4dGFyZWFIZWlnaHQiLCJoYW5kbGVLZXlQcmVzcyIsImUiLCJrZXkiLCJzaGlmdEtleSIsInByZXZlbnREZWZhdWx0IiwiaGFuZGxlU2VuZCIsImhhbmRsZUlucHV0Q2hhbmdlIiwidmFsdWUiLCJ0YXJnZXQiLCJyZXF1ZXN0QW5pbWF0aW9uRnJhbWUiLCJjb25zb2xlIiwibG9nIiwibGVuZ3RoIiwiaGFzTWVzc2FnZSIsInRyaW0iLCJoYXNBdHRhY2htZW50cyIsImhhc1Byb2Nlc3NlZEF0dGFjaG1lbnRzIiwiaGFuZGxlQXR0YWNobWVudCIsImNsaWNrIiwiaGFuZGxlRmlsZVNlbGVjdCIsImZpbGVzIiwiY2hhdElkRm9yVXBsb2FkIiwidGltZXN0YW1wIiwiRGF0ZSIsIm5vdyIsInJhbmRvbSIsIk1hdGgiLCJ0b1N0cmluZyIsInN1YnN0cmluZyIsImxvY2FsQXR0YWNobWVudHMiLCJmaWxlIiwiQXJyYXkiLCJmcm9tIiwidHlwZSIsInNpemUiLCJhdHRhY2htZW50IiwiZmlsZW5hbWUiLCJzdGFydHNXaXRoIiwicHVzaCIsInByZXYiLCJ1cGxvYWRlZEF0dGFjaG1lbnRzIiwidXBsb2FkTXVsdGlwbGVBdHRhY2htZW50cyIsInByb2Nlc3NlZE1ldGFkYXRhIiwibWFwIiwiYXR0IiwibWV0YWRhdGEiLCJlcnJvciIsImZpbHRlciIsInNvbWUiLCJyZW1vdmVBdHRhY2htZW50IiwiZmluZCIsImhhbmRsZVdlYlNlYXJjaCIsInRleHRhcmVhIiwic2Nyb2xsVG9wIiwic3R5bGUiLCJoZWlnaHQiLCJzY3JvbGxIZWlnaHQiLCJtaW5IZWlnaHQiLCJtYXhIZWlnaHQiLCJuZXdIZWlnaHQiLCJtYXgiLCJtaW4iLCJvdmVyZmxvd1kiLCJpc1dlYlNlYXJjaEVuYWJsZWQiLCJ1bnN1cHBvcnRlZE1vZGVscyIsInByZWZpeCIsImN1cnJlbnRNb2RlbCIsIm1vZGVsIiwibW9kZWxOYW1lIiwiZGl2IiwiY2xhc3NOYW1lIiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwic3BhbiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJ0aXRsZSIsImRpc2FibGVkIiwicmVmIiwib25DaGFuZ2UiLCJvbktleURvd24iLCJyb3dzIiwicGxhY2Vob2xkZXIiLCJsaW5lSGVpZ2h0Iiwid29yZFdyYXAiLCJ3aGl0ZVNwYWNlIiwib3ZlcmZsb3dYIiwic2Nyb2xsYmFyV2lkdGgiLCJ4bWxucyIsImlucHV0IiwibXVsdGlwbGUiLCJhY2NlcHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/InputBar.tsx\n"));

/***/ })

});