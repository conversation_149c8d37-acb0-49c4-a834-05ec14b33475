"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/attachmentService.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/attachmentService.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachmentService: function() { return /* binding */ attachmentService; }\n/* harmony export */ });\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\nclass AttachmentService {\n    /**\n   * Valida se o arquivo é suportado\n   */ validateFile(file) {\n        // Verificar tamanho\n        if (file.size > this.MAX_FILE_SIZE) {\n            return {\n                isValid: false,\n                error: \"Arquivo muito grande. M\\xe1ximo permitido: 10MB\"\n            };\n        }\n        // Verificar tipo\n        const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n        const isPdf = file.type === this.SUPPORTED_PDF_TYPE;\n        if (!isImage && !isPdf) {\n            return {\n                isValid: false,\n                error: \"Tipo de arquivo n\\xe3o suportado. Use PNG, JPEG, WebP ou PDF\"\n            };\n        }\n        return {\n            isValid: true\n        };\n    }\n    /**\n   * Converte arquivo para base64\n   */ async fileToBase64(file) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>{\n                const result = reader.result;\n                // Remover o prefixo data:type;base64,\n                const base64 = result.split(\",\")[1];\n                resolve(base64);\n            };\n            reader.onerror = reject;\n            reader.readAsDataURL(file);\n        });\n    }\n    /**\n   * Gera ID único para anexo\n   */ generateAttachmentId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Faz upload do arquivo para Firebase Storage\n   */ async uploadAttachment(params) {\n        const { file, username, chatId } = params;\n        console.log(\"\\uD83D\\uDD04 Iniciando upload do arquivo: \".concat(file.name));\n        console.log(\"\\uD83D\\uDCCA Tamanho: \".concat(file.size, \" bytes, Tipo: \").concat(file.type));\n        console.log(\"\\uD83D\\uDC64 Username: \".concat(username, \", \\uD83D\\uDCAC ChatId: \").concat(chatId));\n        // Verificar autenticação\n        const currentUser = _lib_firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser;\n        if (!currentUser) {\n            console.log(\"❌ Usu\\xe1rio n\\xe3o autenticado\");\n            throw new Error(\"Usu\\xe1rio n\\xe3o autenticado. Fa\\xe7a login novamente.\");\n        }\n        console.log(\"\\uD83D\\uDD10 Usu\\xe1rio autenticado: \".concat(currentUser.uid));\n        // Validar arquivo\n        const validation = this.validateFile(file);\n        if (!validation.isValid) {\n            console.log(\"❌ Valida\\xe7\\xe3o falhou: \".concat(validation.error));\n            throw new Error(validation.error);\n        }\n        console.log(\"✅ Arquivo validado com sucesso\");\n        try {\n            // Gerar ID único para o anexo\n            const attachmentId = this.generateAttachmentId();\n            console.log(\"\\uD83C\\uDD94 ID do anexo gerado: \".concat(attachmentId));\n            // Determinar tipo\n            const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n            const type = isImage ? \"image\" : \"pdf\";\n            console.log(\"\\uD83D\\uDCC4 Tipo determinado: \".concat(type));\n            // Criar caminho no Storage\n            const storagePath = \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/anexos/\").concat(attachmentId, \"_\").concat(file.name);\n            console.log(\"\\uD83D\\uDCC1 Caminho no Storage: \".concat(storagePath));\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.storage, storagePath);\n            // Upload do arquivo\n            console.log(\"\\uD83D\\uDE80 Iniciando upload para Firebase Storage...\");\n            console.log(\"\\uD83D\\uDCC1 StorageRef path:\", storageRef.fullPath);\n            console.log(\"\\uD83D\\uDD10 User UID:\", currentUser.uid);\n            const uploadResult = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.uploadBytes)(storageRef, file);\n            console.log(\"✅ Upload para Firebase Storage conclu\\xeddo\");\n            console.log(\"\\uD83D\\uDCCA Upload result:\", {\n                metadata: {\n                    name: uploadResult.metadata.name,\n                    size: uploadResult.metadata.size,\n                    contentType: uploadResult.metadata.contentType,\n                    fullPath: uploadResult.metadata.fullPath\n                }\n            });\n            // Obter URL de download\n            console.log(\"\\uD83D\\uDD17 Obtendo URL de download...\");\n            const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.getDownloadURL)(storageRef);\n            console.log(\"✅ URL de download obtida: \".concat(downloadURL.substring(0, 100), \"...\"));\n            // Preparar metadados\n            const metadata = {\n                id: attachmentId,\n                type,\n                filename: file.name,\n                url: downloadURL,\n                size: file.size,\n                uploadedAt: Date.now(),\n                storagePath,\n                isActive: true // Por padrão, anexos são ativos\n            };\n            console.log(\"\\uD83D\\uDCCB Metadados preparados:\", metadata);\n            // Para PDFs, também converter para base64 para envio ao OpenRouter\n            let base64Data;\n            if (type === \"pdf\") {\n                console.log(\"\\uD83D\\uDCC4 Convertendo PDF para base64...\");\n                base64Data = await this.fileToBase64(file);\n                metadata.base64Data = base64Data;\n                console.log(\"✅ PDF convertido para base64 (\".concat(base64Data.length, \" caracteres)\"));\n            }\n            console.log(\"\\uD83C\\uDF89 Upload do arquivo \".concat(file.name, \" conclu\\xeddo com sucesso!\"));\n            return {\n                metadata,\n                base64Data\n            };\n        } catch (error) {\n            console.error(\"❌ Erro ao fazer upload do anexo \".concat(file.name, \":\"), error);\n            throw new Error(\"Falha no upload do arquivo \".concat(file.name, \". Tente novamente.\"));\n        }\n    }\n    /**\n   * Processa múltiplos arquivos\n   */ async uploadMultipleAttachments(files, username, chatId) {\n        console.log(\"\\uD83D\\uDCE6 Iniciando upload de \".concat(files.length, \" arquivo(s)\"));\n        const results = [];\n        for(let i = 0; i < files.length; i++){\n            const file = files[i];\n            console.log(\"\\uD83D\\uDCC1 Processando arquivo \".concat(i + 1, \"/\").concat(files.length, \": \").concat(file.name));\n            try {\n                const result = await this.uploadAttachment({\n                    file,\n                    username,\n                    chatId\n                });\n                results.push(result);\n                console.log(\"✅ Arquivo \".concat(i + 1, \"/\").concat(files.length, \" processado com sucesso\"));\n            } catch (error) {\n                console.error(\"❌ Erro ao processar arquivo \".concat(file.name, \":\"), error);\n            // Continuar com os outros arquivos\n            }\n        }\n        console.log(\"\\uD83C\\uDFAF Upload m\\xfaltiplo conclu\\xeddo: \".concat(results.length, \"/\").concat(files.length, \" arquivos processados\"));\n        return results;\n    }\n    /**\n   * Prepara anexos para envio ao OpenRouter\n   */ prepareAttachmentsForOpenRouter(attachments) {\n        const openRouterContent = [];\n        for (const attachment of attachments){\n            if (attachment.type === \"image\") {\n                // Para imagens, usar URL\n                openRouterContent.push({\n                    type: \"image_url\",\n                    image_url: {\n                        url: attachment.url\n                    }\n                });\n            } else if (attachment.type === \"pdf\" && attachment.base64Data) {\n                // Para PDFs, usar base64 com formato file\n                const dataUrl = \"data:application/pdf;base64,\".concat(attachment.base64Data);\n                openRouterContent.push({\n                    type: \"file\",\n                    file: {\n                        filename: attachment.filename,\n                        file_data: dataUrl\n                    }\n                });\n            }\n        }\n        return openRouterContent;\n    }\n    /**\n   * Prepara plugins para PDFs (engine mistral-ocr)\n   */ preparePDFPlugins(attachments) {\n        const hasPDF = attachments.some((att)=>att.type === \"pdf\");\n        if (!hasPDF) {\n            return [];\n        }\n        return [\n            {\n                id: \"file-parser\",\n                pdf: {\n                    engine: \"mistral-ocr\"\n                }\n            }\n        ];\n    }\n    /**\n   * Move anexos de um chatId temporário para um chatId real\n   */ async moveAttachmentsToRealChat(attachments, username, tempChatId, realChatId) {\n        console.log(\"\\uD83D\\uDD04 Movendo \".concat(attachments.length, \" anexos de \").concat(tempChatId, \" para \").concat(realChatId));\n        const updatedAttachments = [];\n        for (const attachment of attachments){\n            try {\n                var _attachment_storagePath;\n                // Verificar se o anexo é de um chat temporário\n                if (!((_attachment_storagePath = attachment.storagePath) === null || _attachment_storagePath === void 0 ? void 0 : _attachment_storagePath.includes(tempChatId))) {\n                    // Se não é do chat temporário, manter como está\n                    updatedAttachments.push(attachment);\n                    continue;\n                }\n                // Criar novo caminho no Storage\n                const newStoragePath = \"usuarios/\".concat(username, \"/conversas/\").concat(realChatId, \"/anexos/\").concat(attachment.id, \"_\").concat(attachment.filename);\n                const oldStorageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.storage, attachment.storagePath);\n                const newStorageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.storage, newStoragePath);\n                // Baixar o arquivo do local temporário\n                const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.getDownloadURL)(oldStorageRef);\n                const response = await fetch(downloadURL);\n                const blob = await response.blob();\n                // Upload para o novo local\n                await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.uploadBytes)(newStorageRef, blob);\n                // Obter nova URL de download\n                const newDownloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.getDownloadURL)(newStorageRef);\n                // Criar metadados atualizados\n                const updatedAttachment = {\n                    ...attachment,\n                    url: newDownloadURL,\n                    storagePath: newStoragePath\n                };\n                updatedAttachments.push(updatedAttachment);\n                console.log(\"✅ Anexo \".concat(attachment.filename, \" movido com sucesso\"));\n            // Opcional: deletar arquivo temporário (comentado por segurança)\n            // await deleteObject(oldStorageRef);\n            } catch (error) {\n                console.error(\"❌ Erro ao mover anexo \".concat(attachment.filename, \":\"), error);\n                // Em caso de erro, manter o anexo original\n                updatedAttachments.push(attachment);\n            }\n        }\n        console.log(\"\\uD83C\\uDFAF Movimento conclu\\xeddo: \".concat(updatedAttachments.length, \" anexos processados\"));\n        return updatedAttachments;\n    }\n    /**\n   * Limpa anexos temporários (se necessário)\n   */ async cleanupTemporaryAttachments(attachments) {\n        // Implementar limpeza se necessário\n        // Por enquanto, mantemos os arquivos no Storage\n        console.log(\"Cleanup de anexos tempor\\xe1rios:\", attachments.length);\n    }\n    constructor(){\n        this.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB\n        ;\n        this.SUPPORTED_IMAGE_TYPES = [\n            \"image/png\",\n            \"image/jpeg\",\n            \"image/webp\"\n        ];\n        this.SUPPORTED_PDF_TYPE = \"application/pdf\";\n    }\n}\n// Exportar instância singleton\nconst attachmentService = new AttachmentService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (attachmentService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/attachmentService.ts\n"));

/***/ })

});