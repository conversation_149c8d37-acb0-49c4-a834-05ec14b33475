"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/attachmentService.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/attachmentService.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachmentService: function() { return /* binding */ attachmentService; }\n/* harmony export */ });\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\nclass AttachmentService {\n    /**\n   * Valida se o arquivo é suportado\n   */ validateFile(file) {\n        // Verificar tamanho\n        if (file.size > this.MAX_FILE_SIZE) {\n            return {\n                isValid: false,\n                error: \"Arquivo muito grande. M\\xe1ximo permitido: 10MB\"\n            };\n        }\n        // Verificar tipo\n        const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n        const isPdf = file.type === this.SUPPORTED_PDF_TYPE;\n        if (!isImage && !isPdf) {\n            return {\n                isValid: false,\n                error: \"Tipo de arquivo n\\xe3o suportado. Use PNG, JPEG, WebP ou PDF\"\n            };\n        }\n        return {\n            isValid: true\n        };\n    }\n    /**\n   * Converte arquivo para base64\n   */ async fileToBase64(file) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>{\n                const result = reader.result;\n                // Remover o prefixo data:type;base64,\n                const base64 = result.split(\",\")[1];\n                resolve(base64);\n            };\n            reader.onerror = reject;\n            reader.readAsDataURL(file);\n        });\n    }\n    /**\n   * Gera ID único para anexo\n   */ generateAttachmentId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Faz upload do arquivo para Firebase Storage\n   */ async uploadAttachment(params) {\n        const { file, username, chatId } = params;\n        console.log(\"\\uD83D\\uDD04 Iniciando upload do arquivo: \".concat(file.name));\n        console.log(\"\\uD83D\\uDCCA Tamanho: \".concat(file.size, \" bytes, Tipo: \").concat(file.type));\n        console.log(\"\\uD83D\\uDC64 Username: \".concat(username, \", \\uD83D\\uDCAC ChatId: \").concat(chatId));\n        // Verificar autenticação\n        const currentUser = _lib_firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser;\n        if (!currentUser) {\n            console.log(\"❌ Usu\\xe1rio n\\xe3o autenticado\");\n            throw new Error(\"Usu\\xe1rio n\\xe3o autenticado. Fa\\xe7a login novamente.\");\n        }\n        console.log(\"\\uD83D\\uDD10 Usu\\xe1rio autenticado: \".concat(currentUser.uid));\n        // Validar arquivo\n        const validation = this.validateFile(file);\n        if (!validation.isValid) {\n            console.log(\"❌ Valida\\xe7\\xe3o falhou: \".concat(validation.error));\n            throw new Error(validation.error);\n        }\n        console.log(\"✅ Arquivo validado com sucesso\");\n        try {\n            // Gerar ID único para o anexo\n            const attachmentId = this.generateAttachmentId();\n            console.log(\"\\uD83C\\uDD94 ID do anexo gerado: \".concat(attachmentId));\n            // Determinar tipo\n            const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n            const type = isImage ? \"image\" : \"pdf\";\n            console.log(\"\\uD83D\\uDCC4 Tipo determinado: \".concat(type));\n            // Criar caminho no Storage\n            const storagePath = \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/anexos/\").concat(attachmentId, \"_\").concat(file.name);\n            console.log(\"\\uD83D\\uDCC1 Caminho no Storage: \".concat(storagePath));\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.storage, storagePath);\n            // Upload do arquivo\n            console.log(\"\\uD83D\\uDE80 Iniciando upload para Firebase Storage...\");\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.uploadBytes)(storageRef, file);\n            console.log(\"✅ Upload para Firebase Storage conclu\\xeddo\");\n            // Obter URL de download\n            console.log(\"\\uD83D\\uDD17 Obtendo URL de download...\");\n            const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.getDownloadURL)(storageRef);\n            console.log(\"✅ URL de download obtida: \".concat(downloadURL.substring(0, 100), \"...\"));\n            // Preparar metadados\n            const metadata = {\n                id: attachmentId,\n                type,\n                filename: file.name,\n                url: downloadURL,\n                size: file.size,\n                uploadedAt: Date.now(),\n                storagePath,\n                isActive: true // Por padrão, anexos são ativos\n            };\n            console.log(\"\\uD83D\\uDCCB Metadados preparados:\", metadata);\n            // Para PDFs, também converter para base64 para envio ao OpenRouter\n            let base64Data;\n            if (type === \"pdf\") {\n                console.log(\"\\uD83D\\uDCC4 Convertendo PDF para base64...\");\n                base64Data = await this.fileToBase64(file);\n                metadata.base64Data = base64Data;\n                console.log(\"✅ PDF convertido para base64 (\".concat(base64Data.length, \" caracteres)\"));\n            }\n            console.log(\"\\uD83C\\uDF89 Upload do arquivo \".concat(file.name, \" conclu\\xeddo com sucesso!\"));\n            return {\n                metadata,\n                base64Data\n            };\n        } catch (error) {\n            console.error(\"❌ Erro ao fazer upload do anexo \".concat(file.name, \":\"), error);\n            throw new Error(\"Falha no upload do arquivo \".concat(file.name, \". Tente novamente.\"));\n        }\n    }\n    /**\n   * Processa múltiplos arquivos\n   */ async uploadMultipleAttachments(files, username, chatId) {\n        console.log(\"\\uD83D\\uDCE6 Iniciando upload de \".concat(files.length, \" arquivo(s)\"));\n        const results = [];\n        for(let i = 0; i < files.length; i++){\n            const file = files[i];\n            console.log(\"\\uD83D\\uDCC1 Processando arquivo \".concat(i + 1, \"/\").concat(files.length, \": \").concat(file.name));\n            try {\n                const result = await this.uploadAttachment({\n                    file,\n                    username,\n                    chatId\n                });\n                results.push(result);\n                console.log(\"✅ Arquivo \".concat(i + 1, \"/\").concat(files.length, \" processado com sucesso\"));\n            } catch (error) {\n                console.error(\"❌ Erro ao processar arquivo \".concat(file.name, \":\"), error);\n            // Continuar com os outros arquivos\n            }\n        }\n        console.log(\"\\uD83C\\uDFAF Upload m\\xfaltiplo conclu\\xeddo: \".concat(results.length, \"/\").concat(files.length, \" arquivos processados\"));\n        return results;\n    }\n    /**\n   * Prepara anexos para envio ao OpenRouter\n   */ prepareAttachmentsForOpenRouter(attachments) {\n        const openRouterContent = [];\n        for (const attachment of attachments){\n            if (attachment.type === \"image\") {\n                // Para imagens, usar URL\n                openRouterContent.push({\n                    type: \"image_url\",\n                    image_url: {\n                        url: attachment.url\n                    }\n                });\n            } else if (attachment.type === \"pdf\" && attachment.base64Data) {\n                // Para PDFs, usar base64 com formato file\n                const dataUrl = \"data:application/pdf;base64,\".concat(attachment.base64Data);\n                openRouterContent.push({\n                    type: \"file\",\n                    file: {\n                        filename: attachment.filename,\n                        file_data: dataUrl\n                    }\n                });\n            }\n        }\n        return openRouterContent;\n    }\n    /**\n   * Prepara plugins para PDFs (engine mistral-ocr)\n   */ preparePDFPlugins(attachments) {\n        const hasPDF = attachments.some((att)=>att.type === \"pdf\");\n        if (!hasPDF) {\n            return [];\n        }\n        return [\n            {\n                id: \"file-parser\",\n                pdf: {\n                    engine: \"mistral-ocr\"\n                }\n            }\n        ];\n    }\n    /**\n   * Limpa anexos temporários (se necessário)\n   */ async cleanupTemporaryAttachments(attachments) {\n        // Implementar limpeza se necessário\n        // Por enquanto, mantemos os arquivos no Storage\n        console.log(\"Cleanup de anexos tempor\\xe1rios:\", attachments.length);\n    }\n    constructor(){\n        this.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB\n        ;\n        this.SUPPORTED_IMAGE_TYPES = [\n            \"image/png\",\n            \"image/jpeg\",\n            \"image/webp\"\n        ];\n        this.SUPPORTED_PDF_TYPE = \"application/pdf\";\n    }\n}\n// Exportar instância singleton\nconst attachmentService = new AttachmentService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (attachmentService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/attachmentService.ts\n"));

/***/ })

});