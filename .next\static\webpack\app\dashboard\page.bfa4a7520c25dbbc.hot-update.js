"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/InputBar.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/InputBar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InputBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst AI_MODELS = [\n    {\n        id: \"gpt-4.1-nano\",\n        name: \"GPT-4.1 Nano\",\n        description: \"R\\xe1pido e eficiente\"\n    },\n    {\n        id: \"gpt-4-turbo\",\n        name: \"GPT-4 Turbo\",\n        description: \"Mais poderoso\"\n    },\n    {\n        id: \"claude-3-sonnet\",\n        name: \"Claude 3 Sonnet\",\n        description: \"Criativo e preciso\"\n    },\n    {\n        id: \"gemini-pro\",\n        name: \"Gemini Pro\",\n        description: \"Multimodal\"\n    }\n];\nfunction InputBar(param) {\n    let { message, setMessage, onSendMessage, isLoading, selectedModel, onModelChange, onScrollToTop, onScrollToBottom, isStreaming = false, onCancelStreaming, onOpenModelModal, username, chatId, activeAttachmentsCount = 0 } = param;\n    _s();\n    const [webSearchEnabled, setWebSearchEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [processedAttachments, setProcessedAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const adjustHeightTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (adjustHeightTimeoutRef.current) {\n                clearTimeout(adjustHeightTimeoutRef.current);\n            }\n        };\n    }, []);\n    // Adjust textarea height when message changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        adjustTextareaHeight();\n    }, [\n        message\n    ]);\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const handleInputChange = (e)=>{\n        setMessage(e.target.value);\n        // Debounce o ajuste de altura para melhor performance\n        if (adjustHeightTimeoutRef.current) {\n            clearTimeout(adjustHeightTimeoutRef.current);\n        }\n        adjustHeightTimeoutRef.current = setTimeout(()=>{\n            adjustTextareaHeight();\n        }, 16); // ~60fps\n    };\n    const handleSend = ()=>{\n        if (((message === null || message === void 0 ? void 0 : message.trim()) || attachments.length > 0) && !isLoading && !isUploading) {\n            console.log(\"=== DEBUG: ENVIANDO MENSAGEM ===\");\n            console.log(\"Mensagem:\", message);\n            console.log(\"Anexos locais:\", attachments.length);\n            console.log(\"Anexos processados:\", processedAttachments.length);\n            console.log(\"Anexos processados detalhes:\", processedAttachments);\n            console.log(\"Web Search Enabled:\", webSearchEnabled);\n            onSendMessage(processedAttachments, webSearchEnabled);\n            // Limpar anexos após envio\n            setAttachments([]);\n            setProcessedAttachments([]);\n        }\n    };\n    const handleAttachment = ()=>{\n        if (fileInputRef.current) {\n            fileInputRef.current.click();\n        }\n    };\n    const handleFileSelect = async (e)=>{\n        const files = e.target.files;\n        if (!files || !username || !chatId) return;\n        setIsUploading(true);\n        try {\n            // Primeiro, adicionar arquivos localmente para preview\n            const localAttachments = [];\n            for (const file of Array.from(files)){\n                const attachment = {\n                    id: Date.now().toString() + Math.random().toString(36).substring(2, 9),\n                    filename: file.name,\n                    type: file.type.startsWith(\"image/\") ? \"image\" : \"document\",\n                    file\n                };\n                localAttachments.push(attachment);\n            }\n            setAttachments((prev)=>[\n                    ...prev,\n                    ...localAttachments\n                ]);\n            // Fazer upload dos arquivos\n            const uploadedAttachments = await _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].uploadMultipleAttachments(Array.from(files), username, chatId);\n            // Atualizar com metadados dos arquivos processados\n            setProcessedAttachments((prev)=>[\n                    ...prev,\n                    ...uploadedAttachments.map((att)=>att.metadata)\n                ]);\n        } catch (error) {\n            console.error(\"Erro ao processar arquivos:\", error);\n            // Remover anexos que falharam\n            setAttachments((prev)=>prev.filter((att)=>!Array.from(files).some((file)=>file.name === att.filename)));\n        } finally{\n            setIsUploading(false);\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n        }\n    };\n    const removeAttachment = (id)=>{\n        // Encontrar o anexo para obter o filename\n        const attachment = attachments.find((att)=>att.id === id);\n        if (attachment) {\n            // Remover do estado local\n            setAttachments((prev)=>prev.filter((att)=>att.id !== id));\n            // Remover dos anexos processados também\n            setProcessedAttachments((prev)=>prev.filter((att)=>att.filename !== attachment.filename));\n        }\n    };\n    const handleWebSearch = ()=>{\n        setWebSearchEnabled(!webSearchEnabled);\n    };\n    const adjustTextareaHeight = ()=>{\n        const textarea = textareaRef.current;\n        if (textarea) {\n            // Reset height to auto to get the correct scrollHeight\n            textarea.style.height = \"auto\";\n            // Calculate new height with proper constraints\n            const scrollHeight = textarea.scrollHeight;\n            const minHeight = 44; // Minimum height in pixels\n            const maxHeight = 120; // Maximum height in pixels\n            // Set the new height within bounds\n            const newHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));\n            textarea.style.height = newHeight + \"px\";\n            // Ensure proper overflow handling\n            textarea.style.overflowY = scrollHeight > maxHeight ? \"auto\" : \"hidden\";\n        }\n    };\n    const isWebSearchEnabled = ()=>{\n        // Web search está disponível para todos os modelos via OpenRouter plugins\n        // Apenas ocultar para modelos locais ou específicos que não suportam\n        const unsupportedModels = [\n            \"local/\",\n            \"offline/\"\n        ];\n        return !unsupportedModels.some((prefix)=>selectedModel.startsWith(prefix));\n    };\n    const currentModel = AI_MODELS.find((model)=>model.id === selectedModel);\n    const modelName = currentModel ? currentModel.name : selectedModel;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-3 sm:p-4 lg:p-6 border-t border-blue-700/30 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 flex items-center justify-start space-x-2\",\n                        children: [\n                            selectedModel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-blue-300\",\n                                        children: \"Modelo: \"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-cyan-300 font-medium\",\n                                        children: modelName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this),\n                            activeAttachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-green-600/30 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-green-300\",\n                                        children: \"Anexos: \"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-green-200 font-medium\",\n                                        children: activeAttachmentsCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3 sm:mb-4 p-3 sm:p-4 bg-blue-900/30 backdrop-blur-sm rounded-lg sm:rounded-xl border border-blue-600/30\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 sm:gap-3\",\n                            children: attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-800/40 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 border border-blue-600/20\",\n                                    children: [\n                                        attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-blue-400 flex-shrink-0\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-red-400 flex-shrink-0\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs sm:text-sm text-blue-100 truncate max-w-20 sm:max-w-32 font-medium\",\n                                            children: attachment.filename\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>removeAttachment(attachment.id),\n                                            className: \"text-blue-300 hover:text-red-400 transition-colors p-0.5 sm:p-1 rounded-full hover:bg-red-500/20 flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3.5 h-3.5 sm:w-4 sm:h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, attachment.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-2 sm:space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative bg-gradient-to-r from-blue-900/80 to-blue-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-blue-600/30 shadow-xl shadow-blue-900/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-xl sm:rounded-2xl shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10 flex items-end space-x-2 sm:space-x-3 p-3 sm:p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 sm:space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: onOpenModelModal,\n                                                        className: \"p-2 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 hover:scale-105\",\n                                                        title: \"Selecionar modelo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleAttachment,\n                                                        disabled: isUploading,\n                                                        className: \"p-2.5 rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105\",\n                                                        title: \"Anexar arquivo\",\n                                                        children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isWebSearchEnabled() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleWebSearch,\n                                                        className: \"p-2.5 rounded-xl transition-all duration-200 hover:shadow-lg border hover:scale-105 backdrop-blur-sm \".concat(webSearchEnabled ? \"bg-cyan-600/30 hover:bg-cyan-600/40 text-cyan-200 hover:text-cyan-100 border-cyan-500/50 shadow-lg shadow-cyan-500/20\" : \"bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 border-blue-600/20 hover:shadow-blue-500/20\"),\n                                                        title: \"Busca na web - \".concat(webSearchEnabled ? \"Ativada\" : \"Desativada\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    ref: textareaRef,\n                                                    value: message,\n                                                    onChange: handleInputChange,\n                                                    onKeyDown: handleKeyPress,\n                                                    className: \"w-full bg-transparent border-none rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none resize-none text-sm leading-relaxed min-h-[44px] max-h-[120px] selection:bg-blue-500/30 overflow-hidden\",\n                                                    rows: 1,\n                                                    placeholder: \"Digite sua mensagem aqui...\",\n                                                    disabled: isLoading || isStreaming,\n                                                    style: {\n                                                        height: \"44px\",\n                                                        lineHeight: \"1.5\",\n                                                        wordWrap: \"break-word\",\n                                                        whiteSpace: \"pre-wrap\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, this),\n                                            isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onCancelStreaming,\n                                                className: \"p-3 rounded-xl bg-gradient-to-r from-red-600 via-red-500 to-red-600 hover:from-red-500 hover:via-red-400 hover:to-red-500 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-red-500/30\",\n                                                title: \"Parar gera\\xe7\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSend,\n                                                disabled: !(message === null || message === void 0 ? void 0 : message.trim()) && attachments.length === 0 || isLoading || isUploading || isStreaming,\n                                                className: \"p-3 rounded-xl bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105 disabled:hover:scale-100 border border-blue-500/30\",\n                                                title: \"Enviar mensagem\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M14 5l7 7m0 0l-7 7m7-7H3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-2\",\n                                children: [\n                                    onScrollToTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onScrollToTop,\n                                        className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                        title: \"Ir para o topo\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"relative w-5 h-5 transform group-hover:-translate-y-0.5 transition-transform duration-200\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2.5,\n                                                    d: \"M5 10l7-7m0 0l7 7m-7-7v18\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this),\n                                    onScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onScrollToBottom,\n                                        className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                        title: \"Ir para o final\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"relative w-5 h-5 transform group-hover:translate-y-0.5 transition-transform duration-200\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2.5,\n                                                    d: \"M19 14l-7 7m0 0l-7-7m7 7V4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        multiple: true,\n                        accept: \"image/png,image/jpeg,image/webp,application/pdf\",\n                        onChange: handleFileSelect,\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, this);\n}\n_s(InputBar, \"P23FsoqKhoN2XSImOuRpWlk/+tg=\");\n_c = InputBar;\nvar _c;\n$RefreshReg$(_c, \"InputBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/InputBar.tsx\n"));

/***/ })

});