"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/InputBar.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/InputBar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InputBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst AI_MODELS = [\n    {\n        id: \"gpt-4.1-nano\",\n        name: \"GPT-4.1 Nano\",\n        description: \"R\\xe1pido e eficiente\"\n    },\n    {\n        id: \"gpt-4-turbo\",\n        name: \"GPT-4 Turbo\",\n        description: \"Mais poderoso\"\n    },\n    {\n        id: \"claude-3-sonnet\",\n        name: \"Claude 3 Sonnet\",\n        description: \"Criativo e preciso\"\n    },\n    {\n        id: \"gemini-pro\",\n        name: \"Gemini Pro\",\n        description: \"Multimodal\"\n    }\n];\nfunction InputBar(param) {\n    let { message, setMessage, onSendMessage, isLoading, selectedModel, onModelChange, onScrollToTop, onScrollToBottom, isStreaming = false, onCancelStreaming, onOpenModelModal, username, chatId, activeAttachmentsCount = 0 } = param;\n    _s();\n    const [webSearchEnabled, setWebSearchEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [processedAttachments, setProcessedAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const adjustHeightTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (adjustHeightTimeoutRef.current) {\n                clearTimeout(adjustHeightTimeoutRef.current);\n            }\n        };\n    }, []);\n    // Adjust textarea height when message changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        adjustTextareaHeight();\n    }, [\n        message\n    ]);\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        // Ajustar altura imediatamente para evitar flickering\n        requestAnimationFrame(()=>{\n            adjustTextareaHeight();\n        });\n    };\n    const handleSend = ()=>{\n        if (((message === null || message === void 0 ? void 0 : message.trim()) || attachments.length > 0) && !isLoading && !isUploading) {\n            console.log(\"=== DEBUG: ENVIANDO MENSAGEM ===\");\n            console.log(\"Mensagem:\", message);\n            console.log(\"Anexos locais:\", attachments.length);\n            console.log(\"Anexos processados:\", processedAttachments.length);\n            console.log(\"Anexos processados detalhes:\", processedAttachments);\n            console.log(\"Web Search Enabled:\", webSearchEnabled);\n            onSendMessage(processedAttachments, webSearchEnabled);\n            // Limpar anexos após envio\n            setAttachments([]);\n            setProcessedAttachments([]);\n        }\n    };\n    const handleAttachment = ()=>{\n        if (fileInputRef.current) {\n            fileInputRef.current.click();\n        }\n    };\n    const handleFileSelect = async (e)=>{\n        const files = e.target.files;\n        if (!files || !username || !chatId) return;\n        setIsUploading(true);\n        try {\n            // Primeiro, adicionar arquivos localmente para preview\n            const localAttachments = [];\n            for (const file of Array.from(files)){\n                const attachment = {\n                    id: Date.now().toString() + Math.random().toString(36).substring(2, 9),\n                    filename: file.name,\n                    type: file.type.startsWith(\"image/\") ? \"image\" : \"document\",\n                    file\n                };\n                localAttachments.push(attachment);\n            }\n            setAttachments((prev)=>[\n                    ...prev,\n                    ...localAttachments\n                ]);\n            // Fazer upload dos arquivos\n            const uploadedAttachments = await _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].uploadMultipleAttachments(Array.from(files), username, chatId);\n            // Atualizar com metadados dos arquivos processados\n            setProcessedAttachments((prev)=>[\n                    ...prev,\n                    ...uploadedAttachments.map((att)=>att.metadata)\n                ]);\n        } catch (error) {\n            console.error(\"Erro ao processar arquivos:\", error);\n            // Remover anexos que falharam\n            setAttachments((prev)=>prev.filter((att)=>!Array.from(files).some((file)=>file.name === att.filename)));\n        } finally{\n            setIsUploading(false);\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n        }\n    };\n    const removeAttachment = (id)=>{\n        // Encontrar o anexo para obter o filename\n        const attachment = attachments.find((att)=>att.id === id);\n        if (attachment) {\n            // Remover do estado local\n            setAttachments((prev)=>prev.filter((att)=>att.id !== id));\n            // Remover dos anexos processados também\n            setProcessedAttachments((prev)=>prev.filter((att)=>att.filename !== attachment.filename));\n        }\n    };\n    const handleWebSearch = ()=>{\n        setWebSearchEnabled(!webSearchEnabled);\n    };\n    const adjustTextareaHeight = ()=>{\n        const textarea = textareaRef.current;\n        if (!textarea) return;\n        // Store current scroll position to prevent jumping\n        const scrollTop = textarea.scrollTop;\n        // Temporarily set height to auto to get accurate scrollHeight\n        textarea.style.height = \"auto\";\n        // Calculate new height with proper constraints\n        const scrollHeight = textarea.scrollHeight;\n        const minHeight = 44; // Minimum height in pixels\n        const maxHeight = 120; // Maximum height in pixels\n        // Set the new height within bounds\n        const newHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));\n        textarea.style.height = newHeight + \"px\";\n        // Handle overflow and scrolling\n        if (scrollHeight > maxHeight) {\n            textarea.style.overflowY = \"auto\";\n            // Restore scroll position if content was scrolled\n            if (scrollTop > 0) {\n                textarea.scrollTop = scrollTop;\n            }\n        } else {\n            textarea.style.overflowY = \"hidden\";\n        }\n    };\n    const isWebSearchEnabled = ()=>{\n        // Web search está disponível para todos os modelos via OpenRouter plugins\n        // Apenas ocultar para modelos locais ou específicos que não suportam\n        const unsupportedModels = [\n            \"local/\",\n            \"offline/\"\n        ];\n        return !unsupportedModels.some((prefix)=>selectedModel.startsWith(prefix));\n    };\n    const currentModel = AI_MODELS.find((model)=>model.id === selectedModel);\n    const modelName = currentModel ? currentModel.name : selectedModel;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            selectedModel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-8 left-3 sm:left-4 lg:left-6 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-blue-300\",\n                            children: \"Modelo: \"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-cyan-300 font-medium\",\n                            children: modelName\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-x-0 -top-8 h-8 bg-gradient-to-t from-blue-950/30 via-blue-950/10 to-transparent pointer-events-none blur-sm\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 sm:p-4 lg:p-6 bg-gradient-to-r from-blue-950/98 via-blue-900/98 to-blue-950/98 backdrop-blur-xl relative shadow-2xl shadow-blue-900/60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-500/8 via-transparent to-cyan-500/8 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-blue-600/15 via-blue-500/5 to-transparent pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto relative z-10\",\n                        children: [\n                            attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3 sm:mb-4 p-3 sm:p-4 bg-blue-900/30 backdrop-blur-sm rounded-lg sm:rounded-xl border border-blue-600/30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 sm:gap-3\",\n                                    children: attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-800/40 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 border border-blue-600/20\",\n                                            children: [\n                                                attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-blue-400 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-red-400 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs sm:text-sm text-blue-100 truncate max-w-20 sm:max-w-32 font-medium\",\n                                                    children: attachment.filename\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>removeAttachment(attachment.id),\n                                                    className: \"text-blue-300 hover:text-red-400 transition-colors p-0.5 sm:p-1 rounded-full hover:bg-red-500/20 flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3.5 h-3.5 sm:w-4 sm:h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M6 18L18 6M6 6l12 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, attachment.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            (selectedModel || activeAttachmentsCount > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3 flex items-center justify-start space-x-2\",\n                                children: [\n                                    selectedModel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-blue-300\",\n                                                children: \"Modelo: \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-cyan-300 font-medium\",\n                                                children: modelName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeAttachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-green-600/30 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-300\",\n                                                children: \"Anexos: \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: activeAttachmentsCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end space-x-2 sm:space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 relative bg-gradient-to-r from-blue-900/80 to-blue-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-blue-600/30 shadow-xl shadow-blue-900/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl sm:rounded-2xl shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 flex items-end space-x-2 sm:space-x-3 p-3 sm:p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 sm:space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: onOpenModelModal,\n                                                                className: \"p-2 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 hover:scale-105\",\n                                                                title: \"Selecionar modelo\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleAttachment,\n                                                                disabled: isUploading,\n                                                                className: \"p-2.5 rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105\",\n                                                                title: \"Anexar arquivo\",\n                                                                children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 21\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            isWebSearchEnabled() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleWebSearch,\n                                                                className: \"p-2.5 rounded-xl transition-all duration-200 hover:shadow-lg border hover:scale-105 backdrop-blur-sm \".concat(webSearchEnabled ? \"bg-cyan-600/30 hover:bg-cyan-600/40 text-cyan-200 hover:text-cyan-100 border-cyan-500/50 shadow-lg shadow-cyan-500/20\" : \"bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 border-blue-600/20 hover:shadow-blue-500/20\"),\n                                                                title: \"Busca na web - \".concat(webSearchEnabled ? \"Ativada\" : \"Desativada\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            ref: textareaRef,\n                                                            value: message,\n                                                            onChange: handleInputChange,\n                                                            onKeyDown: handleKeyPress,\n                                                            className: \"w-full bg-transparent border-none rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none resize-none text-sm selection:bg-blue-500/30\",\n                                                            rows: 1,\n                                                            placeholder: \"Digite sua mensagem aqui...\",\n                                                            disabled: isLoading || isStreaming,\n                                                            style: {\n                                                                height: \"44px\",\n                                                                minHeight: \"44px\",\n                                                                maxHeight: \"120px\",\n                                                                lineHeight: \"1.5\",\n                                                                wordWrap: \"break-word\",\n                                                                whiteSpace: \"pre-wrap\",\n                                                                overflowY: \"hidden\",\n                                                                overflowX: \"hidden\",\n                                                                scrollbarWidth: \"thin\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: onCancelStreaming,\n                                                        className: \"p-3 rounded-xl bg-gradient-to-r from-red-600 via-red-500 to-red-600 hover:from-red-500 hover:via-red-400 hover:to-red-500 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-red-500/30\",\n                                                        title: \"Parar gera\\xe7\\xe3o\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 17\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleSend,\n                                                        disabled: !(message === null || message === void 0 ? void 0 : message.trim()) && attachments.length === 0 || isLoading || isUploading || isStreaming,\n                                                        className: \"p-3 rounded-xl bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105 disabled:hover:scale-100 border border-blue-500/30\",\n                                                        title: \"Enviar mensagem\",\n                                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M14 5l7 7m0 0l-7 7m7-7H3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-2\",\n                                        children: [\n                                            onScrollToTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onScrollToTop,\n                                                className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                                title: \"Ir para o topo\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"relative w-5 h-5 transform group-hover:-translate-y-0.5 transition-transform duration-200\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2.5,\n                                                            d: \"M5 10l7-7m0 0l7 7m-7-7v18\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, this),\n                                            onScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onScrollToBottom,\n                                                className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                                title: \"Ir para o final\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"relative w-5 h-5 transform group-hover:translate-y-0.5 transition-transform duration-200\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2.5,\n                                                            d: \"M19 14l-7 7m0 0l-7-7m7 7V4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: fileInputRef,\n                                type: \"file\",\n                                multiple: true,\n                                accept: \"image/png,image/jpeg,image/webp,application/pdf\",\n                                onChange: handleFileSelect,\n                                className: \"hidden\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n        lineNumber: 220,\n        columnNumber: 5\n    }, this);\n}\n_s(InputBar, \"P23FsoqKhoN2XSImOuRpWlk/+tg=\");\n_c = InputBar;\nvar _c;\n$RefreshReg$(_c, \"InputBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/InputBar.tsx\n"));

/***/ })

});