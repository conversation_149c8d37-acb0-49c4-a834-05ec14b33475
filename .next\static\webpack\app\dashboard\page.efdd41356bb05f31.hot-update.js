"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/attachmentService.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/attachmentService.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachmentService: function() { return /* binding */ attachmentService; }\n/* harmony export */ });\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\nclass AttachmentService {\n    /**\n   * Valida se o arquivo é suportado\n   */ validateFile(file) {\n        // Verificar tamanho\n        if (file.size > this.MAX_FILE_SIZE) {\n            return {\n                isValid: false,\n                error: \"Arquivo muito grande. M\\xe1ximo permitido: 10MB\"\n            };\n        }\n        // Verificar tipo\n        const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n        const isPdf = file.type === this.SUPPORTED_PDF_TYPE;\n        if (!isImage && !isPdf) {\n            return {\n                isValid: false,\n                error: \"Tipo de arquivo n\\xe3o suportado. Use PNG, JPEG, WebP ou PDF\"\n            };\n        }\n        return {\n            isValid: true\n        };\n    }\n    /**\n   * Converte arquivo para base64\n   */ async fileToBase64(file) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>{\n                const result = reader.result;\n                // Remover o prefixo data:type;base64,\n                const base64 = result.split(\",\")[1];\n                resolve(base64);\n            };\n            reader.onerror = reject;\n            reader.readAsDataURL(file);\n        });\n    }\n    /**\n   * Gera ID único para anexo\n   */ generateAttachmentId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Faz upload do arquivo para Firebase Storage\n   */ async uploadAttachment(params) {\n        const { file, username, chatId } = params;\n        console.log(\"\\uD83D\\uDD04 Iniciando upload do arquivo: \".concat(file.name));\n        console.log(\"\\uD83D\\uDCCA Tamanho: \".concat(file.size, \" bytes, Tipo: \").concat(file.type));\n        console.log(\"\\uD83D\\uDC64 Username: \".concat(username, \", \\uD83D\\uDCAC ChatId: \").concat(chatId));\n        // Validar arquivo\n        const validation = this.validateFile(file);\n        if (!validation.isValid) {\n            console.log(\"❌ Valida\\xe7\\xe3o falhou: \".concat(validation.error));\n            throw new Error(validation.error);\n        }\n        console.log(\"✅ Arquivo validado com sucesso\");\n        try {\n            // Gerar ID único para o anexo\n            const attachmentId = this.generateAttachmentId();\n            console.log(\"\\uD83C\\uDD94 ID do anexo gerado: \".concat(attachmentId));\n            // Determinar tipo\n            const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);\n            const type = isImage ? \"image\" : \"pdf\";\n            console.log(\"\\uD83D\\uDCC4 Tipo determinado: \".concat(type));\n            // Criar caminho no Storage\n            const storagePath = \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/anexos/\").concat(attachmentId, \"_\").concat(file.name);\n            console.log(\"\\uD83D\\uDCC1 Caminho no Storage: \".concat(storagePath));\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.storage, storagePath);\n            // Upload do arquivo\n            console.log(\"\\uD83D\\uDE80 Iniciando upload para Firebase Storage...\");\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.uploadBytes)(storageRef, file);\n            console.log(\"✅ Upload para Firebase Storage conclu\\xeddo\");\n            // Obter URL de download\n            console.log(\"\\uD83D\\uDD17 Obtendo URL de download...\");\n            const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_0__.getDownloadURL)(storageRef);\n            console.log(\"✅ URL de download obtida: \".concat(downloadURL.substring(0, 100), \"...\"));\n            // Preparar metadados\n            const metadata = {\n                id: attachmentId,\n                type,\n                filename: file.name,\n                url: downloadURL,\n                size: file.size,\n                uploadedAt: Date.now(),\n                storagePath,\n                isActive: true // Por padrão, anexos são ativos\n            };\n            console.log(\"\\uD83D\\uDCCB Metadados preparados:\", metadata);\n            // Para PDFs, também converter para base64 para envio ao OpenRouter\n            let base64Data;\n            if (type === \"pdf\") {\n                console.log(\"\\uD83D\\uDCC4 Convertendo PDF para base64...\");\n                base64Data = await this.fileToBase64(file);\n                metadata.base64Data = base64Data;\n                console.log(\"✅ PDF convertido para base64 (\".concat(base64Data.length, \" caracteres)\"));\n            }\n            console.log(\"\\uD83C\\uDF89 Upload do arquivo \".concat(file.name, \" conclu\\xeddo com sucesso!\"));\n            return {\n                metadata,\n                base64Data\n            };\n        } catch (error) {\n            console.error(\"❌ Erro ao fazer upload do anexo \".concat(file.name, \":\"), error);\n            throw new Error(\"Falha no upload do arquivo \".concat(file.name, \". Tente novamente.\"));\n        }\n    }\n    /**\n   * Processa múltiplos arquivos\n   */ async uploadMultipleAttachments(files, username, chatId) {\n        console.log(\"\\uD83D\\uDCE6 Iniciando upload de \".concat(files.length, \" arquivo(s)\"));\n        const results = [];\n        for(let i = 0; i < files.length; i++){\n            const file = files[i];\n            console.log(\"\\uD83D\\uDCC1 Processando arquivo \".concat(i + 1, \"/\").concat(files.length, \": \").concat(file.name));\n            try {\n                const result = await this.uploadAttachment({\n                    file,\n                    username,\n                    chatId\n                });\n                results.push(result);\n                console.log(\"✅ Arquivo \".concat(i + 1, \"/\").concat(files.length, \" processado com sucesso\"));\n            } catch (error) {\n                console.error(\"❌ Erro ao processar arquivo \".concat(file.name, \":\"), error);\n            // Continuar com os outros arquivos\n            }\n        }\n        console.log(\"\\uD83C\\uDFAF Upload m\\xfaltiplo conclu\\xeddo: \".concat(results.length, \"/\").concat(files.length, \" arquivos processados\"));\n        return results;\n    }\n    /**\n   * Prepara anexos para envio ao OpenRouter\n   */ prepareAttachmentsForOpenRouter(attachments) {\n        const openRouterContent = [];\n        for (const attachment of attachments){\n            if (attachment.type === \"image\") {\n                // Para imagens, usar URL\n                openRouterContent.push({\n                    type: \"image_url\",\n                    image_url: {\n                        url: attachment.url\n                    }\n                });\n            } else if (attachment.type === \"pdf\" && attachment.base64Data) {\n                // Para PDFs, usar base64 com formato file\n                const dataUrl = \"data:application/pdf;base64,\".concat(attachment.base64Data);\n                openRouterContent.push({\n                    type: \"file\",\n                    file: {\n                        filename: attachment.filename,\n                        file_data: dataUrl\n                    }\n                });\n            }\n        }\n        return openRouterContent;\n    }\n    /**\n   * Prepara plugins para PDFs (engine mistral-ocr)\n   */ preparePDFPlugins(attachments) {\n        const hasPDF = attachments.some((att)=>att.type === \"pdf\");\n        if (!hasPDF) {\n            return [];\n        }\n        return [\n            {\n                id: \"file-parser\",\n                pdf: {\n                    engine: \"mistral-ocr\"\n                }\n            }\n        ];\n    }\n    /**\n   * Limpa anexos temporários (se necessário)\n   */ async cleanupTemporaryAttachments(attachments) {\n        // Implementar limpeza se necessário\n        // Por enquanto, mantemos os arquivos no Storage\n        console.log(\"Cleanup de anexos tempor\\xe1rios:\", attachments.length);\n    }\n    constructor(){\n        this.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB\n        ;\n        this.SUPPORTED_IMAGE_TYPES = [\n            \"image/png\",\n            \"image/jpeg\",\n            \"image/webp\"\n        ];\n        this.SUPPORTED_PDF_TYPE = \"application/pdf\";\n    }\n}\n// Exportar instância singleton\nconst attachmentService = new AttachmentService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (attachmentService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/attachmentService.ts\n"));

/***/ })

});