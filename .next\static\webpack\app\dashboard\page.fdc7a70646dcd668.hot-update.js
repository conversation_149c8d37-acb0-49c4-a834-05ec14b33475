"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/InputBar.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/InputBar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InputBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst AI_MODELS = [\n    {\n        id: \"gpt-4.1-nano\",\n        name: \"GPT-4.1 Nano\",\n        description: \"R\\xe1pido e eficiente\"\n    },\n    {\n        id: \"gpt-4-turbo\",\n        name: \"GPT-4 Turbo\",\n        description: \"Mais poderoso\"\n    },\n    {\n        id: \"claude-3-sonnet\",\n        name: \"Claude 3 Sonnet\",\n        description: \"Criativo e preciso\"\n    },\n    {\n        id: \"gemini-pro\",\n        name: \"Gemini Pro\",\n        description: \"Multimodal\"\n    }\n];\nfunction InputBar(param) {\n    let { message, setMessage, onSendMessage, isLoading, selectedModel, onModelChange, onScrollToTop, onScrollToBottom, isStreaming = false, onCancelStreaming, onOpenModelModal, username, chatId, activeAttachmentsCount = 0 } = param;\n    _s();\n    const [webSearchEnabled, setWebSearchEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [processedAttachments, setProcessedAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const adjustHeightTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (adjustHeightTimeoutRef.current) {\n                clearTimeout(adjustHeightTimeoutRef.current);\n            }\n        };\n    }, []);\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const handleInputChange = (e)=>{\n        setMessage(e.target.value);\n        // Ajustar altura imediatamente após a mudança\n        setTimeout(()=>{\n            adjustTextareaHeight();\n        }, 0);\n    };\n    const handleSend = ()=>{\n        if (((message === null || message === void 0 ? void 0 : message.trim()) || attachments.length > 0) && !isLoading && !isUploading) {\n            console.log(\"=== DEBUG: ENVIANDO MENSAGEM ===\");\n            console.log(\"Mensagem:\", message);\n            console.log(\"Anexos locais:\", attachments.length);\n            console.log(\"Anexos processados:\", processedAttachments.length);\n            console.log(\"Anexos processados detalhes:\", processedAttachments);\n            console.log(\"Web Search Enabled:\", webSearchEnabled);\n            onSendMessage(processedAttachments, webSearchEnabled);\n            // Limpar anexos após envio\n            setAttachments([]);\n            setProcessedAttachments([]);\n        }\n    };\n    const handleAttachment = ()=>{\n        if (fileInputRef.current) {\n            fileInputRef.current.click();\n        }\n    };\n    const handleFileSelect = async (e)=>{\n        const files = e.target.files;\n        if (!files || !username || !chatId) return;\n        setIsUploading(true);\n        try {\n            // Primeiro, adicionar arquivos localmente para preview\n            const localAttachments = [];\n            for (const file of Array.from(files)){\n                const attachment = {\n                    id: Date.now().toString() + Math.random().toString(36).substring(2, 9),\n                    filename: file.name,\n                    type: file.type.startsWith(\"image/\") ? \"image\" : \"document\",\n                    file\n                };\n                localAttachments.push(attachment);\n            }\n            setAttachments((prev)=>[\n                    ...prev,\n                    ...localAttachments\n                ]);\n            // Fazer upload dos arquivos\n            const uploadedAttachments = await _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].uploadMultipleAttachments(Array.from(files), username, chatId);\n            // Atualizar com metadados dos arquivos processados\n            setProcessedAttachments((prev)=>[\n                    ...prev,\n                    ...uploadedAttachments.map((att)=>att.metadata)\n                ]);\n        } catch (error) {\n            console.error(\"Erro ao processar arquivos:\", error);\n            // Remover anexos que falharam\n            setAttachments((prev)=>prev.filter((att)=>!Array.from(files).some((file)=>file.name === att.filename)));\n        } finally{\n            setIsUploading(false);\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n        }\n    };\n    const removeAttachment = (id)=>{\n        // Encontrar o anexo para obter o filename\n        const attachment = attachments.find((att)=>att.id === id);\n        if (attachment) {\n            // Remover do estado local\n            setAttachments((prev)=>prev.filter((att)=>att.id !== id));\n            // Remover dos anexos processados também\n            setProcessedAttachments((prev)=>prev.filter((att)=>att.filename !== attachment.filename));\n        }\n    };\n    const handleWebSearch = ()=>{\n        setWebSearchEnabled(!webSearchEnabled);\n    };\n    const adjustTextareaHeight = ()=>{\n        const textarea = textareaRef.current;\n        if (textarea) {\n            // Salvar estado atual\n            const currentHeight = textarea.style.height;\n            const scrollTop = textarea.scrollTop;\n            // Temporariamente definir altura como auto para medir conteúdo\n            textarea.style.height = \"auto\";\n            // Calcular nova altura baseada no scrollHeight\n            const scrollHeight = textarea.scrollHeight;\n            const minHeight = 44;\n            const maxHeight = 120;\n            // Determinar altura final\n            const newHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));\n            // Só aplicar se realmente mudou para evitar flicker\n            if (currentHeight !== newHeight + \"px\") {\n                textarea.style.height = newHeight + \"px\";\n            }\n            // Restaurar scroll se necessário\n            if (scrollTop > 0) {\n                textarea.scrollTop = scrollTop;\n            }\n        }\n    };\n    const isWebSearchEnabled = ()=>{\n        // Web search está disponível para todos os modelos via OpenRouter plugins\n        // Apenas ocultar para modelos locais ou específicos que não suportam\n        const unsupportedModels = [\n            \"local/\",\n            \"offline/\"\n        ];\n        return !unsupportedModels.some((prefix)=>selectedModel.startsWith(prefix));\n    };\n    const currentModel = AI_MODELS.find((model)=>model.id === selectedModel);\n    const modelName = currentModel ? currentModel.name : selectedModel;\n    const selectedAttachmentsCount = attachments.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-3 sm:p-4 lg:p-6 border-t border-blue-700/30 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto relative z-10\",\n                children: [\n                    attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3 sm:mb-4 p-3 sm:p-4 bg-blue-900/30 backdrop-blur-sm rounded-lg sm:rounded-xl border border-blue-600/30\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 sm:gap-3\",\n                            children: attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-800/40 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 border border-blue-600/20\",\n                                    children: [\n                                        attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-blue-400 flex-shrink-0\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-red-400 flex-shrink-0\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs sm:text-sm text-blue-100 truncate max-w-20 sm:max-w-32 font-medium\",\n                                            children: attachment.filename\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>removeAttachment(attachment.id),\n                                            className: \"text-blue-300 hover:text-red-400 transition-colors p-0.5 sm:p-1 rounded-full hover:bg-red-500/20 flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3.5 h-3.5 sm:w-4 sm:h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, attachment.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-2 sm:space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative bg-gradient-to-r from-blue-900/80 to-blue-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-blue-600/30 shadow-xl shadow-blue-900/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-xl sm:rounded-2xl shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10 flex items-end space-x-2 sm:space-x-3 p-3 sm:p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 sm:space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: onOpenModelModal,\n                                                        className: \"p-2 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 hover:scale-105\",\n                                                        title: \"Selecionar modelo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleAttachment,\n                                                        disabled: isUploading,\n                                                        className: \"p-2.5 rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105\",\n                                                        title: \"Anexar arquivo\",\n                                                        children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    isWebSearchEnabled() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleWebSearch,\n                                                        className: \"p-2.5 rounded-xl transition-all duration-200 hover:shadow-lg border hover:scale-105 backdrop-blur-sm \".concat(webSearchEnabled ? \"bg-cyan-600/30 hover:bg-cyan-600/40 text-cyan-200 hover:text-cyan-100 border-cyan-500/50 shadow-lg shadow-cyan-500/20\" : \"bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 border-blue-600/20 hover:shadow-blue-500/20\"),\n                                                        title: \"Busca na web - \".concat(webSearchEnabled ? \"Ativada\" : \"Desativada\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 relative\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    ref: textareaRef,\n                                                    value: message,\n                                                    onChange: handleInputChange,\n                                                    onKeyDown: handleKeyPress,\n                                                    className: \"input-bar-textarea w-full bg-transparent border-none rounded-xl px-4 text-blue-100 placeholder:text-blue-300/60 focus:outline-none resize-none text-sm selection:bg-blue-500/30\",\n                                                    rows: 1,\n                                                    placeholder: \"Digite sua mensagem aqui... ✨\",\n                                                    disabled: isLoading || isStreaming,\n                                                    style: {\n                                                        height: \"44px\",\n                                                        paddingTop: \"10px\",\n                                                        paddingBottom: \"10px\",\n                                                        minHeight: \"44px\",\n                                                        maxHeight: \"120px\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 15\n                                            }, this),\n                                            isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onCancelStreaming,\n                                                className: \"p-3 rounded-xl bg-gradient-to-r from-red-600 via-red-500 to-red-600 hover:from-red-500 hover:via-red-400 hover:to-red-500 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-red-500/30\",\n                                                title: \"Parar gera\\xe7\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSend,\n                                                disabled: !(message === null || message === void 0 ? void 0 : message.trim()) && attachments.length === 0 || isLoading || isUploading || isStreaming,\n                                                className: \"p-3 rounded-xl bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105 disabled:hover:scale-100 border border-blue-500/30\",\n                                                title: \"Enviar mensagem\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M14 5l7 7m0 0l-7 7m7-7H3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-8 left-4 flex items-center space-x-2\",\n                                        children: [\n                                            selectedModel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-blue-300\",\n                                                        children: \"Modelo: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-cyan-300 font-medium\",\n                                                        children: modelName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this),\n                                            activeAttachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-green-600/30 shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-300\",\n                                                        children: \"Anexos: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-200 font-medium\",\n                                                        children: activeAttachmentsCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-2\",\n                                children: [\n                                    onScrollToTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onScrollToTop,\n                                        className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                        title: \"Ir para o topo\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"relative w-5 h-5 transform group-hover:-translate-y-0.5 transition-transform duration-200\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2.5,\n                                                    d: \"M5 10l7-7m0 0l7 7m-7-7v18\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this),\n                                    onScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onScrollToBottom,\n                                        className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                        title: \"Ir para o final\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"relative w-5 h-5 transform group-hover:translate-y-0.5 transition-transform duration-200\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2.5,\n                                                    d: \"M19 14l-7 7m0 0l-7-7m7 7V4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        multiple: true,\n                        accept: \"image/png,image/jpeg,image/webp,application/pdf\",\n                        onChange: handleFileSelect,\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, this);\n}\n_s(InputBar, \"NATbgEVliPa57PsWdc1QpV4trNc=\");\n_c = InputBar;\nvar _c;\n$RefreshReg$(_c, \"InputBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/InputBar.tsx\n"));

/***/ })

});