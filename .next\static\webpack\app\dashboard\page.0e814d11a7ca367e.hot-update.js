"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/InputBar.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/InputBar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InputBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst AI_MODELS = [\n    {\n        id: \"gpt-4.1-nano\",\n        name: \"GPT-4.1 Nano\",\n        description: \"R\\xe1pido e eficiente\"\n    },\n    {\n        id: \"gpt-4-turbo\",\n        name: \"GPT-4 Turbo\",\n        description: \"Mais poderoso\"\n    },\n    {\n        id: \"claude-3-sonnet\",\n        name: \"Claude 3 Sonnet\",\n        description: \"Criativo e preciso\"\n    },\n    {\n        id: \"gemini-pro\",\n        name: \"Gemini Pro\",\n        description: \"Multimodal\"\n    }\n];\nfunction InputBar(param) {\n    let { message, setMessage, onSendMessage, isLoading, selectedModel, onModelChange, onScrollToTop, onScrollToBottom, isStreaming = false, onCancelStreaming, onOpenModelModal, username, chatId, activeAttachmentsCount = 0 } = param;\n    _s();\n    const [webSearchEnabled, setWebSearchEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [processedAttachments, setProcessedAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const adjustHeightTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (adjustHeightTimeoutRef.current) {\n                clearTimeout(adjustHeightTimeoutRef.current);\n            }\n        };\n    }, []);\n    // Adjust textarea height when message changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        adjustTextareaHeight();\n    }, [\n        message\n    ]);\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        // Ajustar altura imediatamente para evitar flickering\n        requestAnimationFrame(()=>{\n            adjustTextareaHeight();\n        });\n    };\n    const handleSend = ()=>{\n        console.log(\"=== DEBUG: HANDLE SEND CHAMADO ===\");\n        console.log(\"Mensagem:\", message);\n        console.log(\"Mensagem trim:\", message === null || message === void 0 ? void 0 : message.trim());\n        console.log(\"Anexos locais:\", attachments.length);\n        console.log(\"Anexos processados:\", processedAttachments.length);\n        console.log(\"IsLoading:\", isLoading);\n        console.log(\"IsUploading:\", isUploading);\n        const hasMessage = message === null || message === void 0 ? void 0 : message.trim();\n        const hasAttachments = attachments.length > 0;\n        const canSend = (hasMessage || hasAttachments) && !isLoading && !isUploading;\n        console.log(\"=== DEBUG: CONDI\\xc7\\xd5ES DE ENVIO ===\");\n        console.log(\"Tem mensagem:\", !!hasMessage);\n        console.log(\"Tem anexos:\", hasAttachments);\n        console.log(\"Pode enviar:\", canSend);\n        if (canSend) {\n            console.log(\"=== DEBUG: ENVIANDO MENSAGEM ===\");\n            console.log(\"Mensagem:\", message);\n            console.log(\"Anexos locais:\", attachments.length);\n            console.log(\"Anexos processados:\", processedAttachments.length);\n            console.log(\"Anexos processados detalhes:\", processedAttachments);\n            console.log(\"Web Search Enabled:\", webSearchEnabled);\n            onSendMessage(processedAttachments, webSearchEnabled);\n            // Limpar anexos após envio\n            setAttachments([]);\n            setProcessedAttachments([]);\n        } else {\n            console.log(\"=== DEBUG: N\\xc3O PODE ENVIAR MENSAGEM ===\");\n            console.log(\"Raz\\xf5es:\", {\n                semMensagemESemAnexos: !hasMessage && !hasAttachments,\n                carregando: isLoading,\n                fazendoUpload: isUploading\n            });\n        }\n    };\n    const handleAttachment = ()=>{\n        if (fileInputRef.current) {\n            fileInputRef.current.click();\n        }\n    };\n    const handleFileSelect = async (e)=>{\n        const files = e.target.files;\n        if (!files || !username || !chatId) return;\n        setIsUploading(true);\n        try {\n            // Primeiro, adicionar arquivos localmente para preview\n            const localAttachments = [];\n            for (const file of Array.from(files)){\n                const attachment = {\n                    id: Date.now().toString() + Math.random().toString(36).substring(2, 9),\n                    filename: file.name,\n                    type: file.type.startsWith(\"image/\") ? \"image\" : \"document\",\n                    file\n                };\n                localAttachments.push(attachment);\n            }\n            setAttachments((prev)=>[\n                    ...prev,\n                    ...localAttachments\n                ]);\n            // Fazer upload dos arquivos\n            const uploadedAttachments = await _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].uploadMultipleAttachments(Array.from(files), username, chatId);\n            // Atualizar com metadados dos arquivos processados\n            setProcessedAttachments((prev)=>[\n                    ...prev,\n                    ...uploadedAttachments.map((att)=>att.metadata)\n                ]);\n        } catch (error) {\n            console.error(\"Erro ao processar arquivos:\", error);\n            // Remover anexos que falharam\n            setAttachments((prev)=>prev.filter((att)=>!Array.from(files).some((file)=>file.name === att.filename)));\n        } finally{\n            setIsUploading(false);\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n        }\n    };\n    const removeAttachment = (id)=>{\n        // Encontrar o anexo para obter o filename\n        const attachment = attachments.find((att)=>att.id === id);\n        if (attachment) {\n            // Remover do estado local\n            setAttachments((prev)=>prev.filter((att)=>att.id !== id));\n            // Remover dos anexos processados também\n            setProcessedAttachments((prev)=>prev.filter((att)=>att.filename !== attachment.filename));\n        }\n    };\n    const handleWebSearch = ()=>{\n        setWebSearchEnabled(!webSearchEnabled);\n    };\n    const adjustTextareaHeight = ()=>{\n        const textarea = textareaRef.current;\n        if (!textarea) return;\n        // Store current scroll position to prevent jumping\n        const scrollTop = textarea.scrollTop;\n        // Temporarily set height to auto to get accurate scrollHeight\n        textarea.style.height = \"auto\";\n        // Calculate new height with proper constraints\n        const scrollHeight = textarea.scrollHeight;\n        const minHeight = 44; // Minimum height in pixels\n        const maxHeight = 120; // Maximum height in pixels\n        // Set the new height within bounds\n        const newHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));\n        textarea.style.height = newHeight + \"px\";\n        // Handle overflow and scrolling\n        if (scrollHeight > maxHeight) {\n            textarea.style.overflowY = \"auto\";\n            // Restore scroll position if content was scrolled\n            if (scrollTop > 0) {\n                textarea.scrollTop = scrollTop;\n            }\n        } else {\n            textarea.style.overflowY = \"hidden\";\n        }\n    };\n    const isWebSearchEnabled = ()=>{\n        // Web search está disponível para todos os modelos via OpenRouter plugins\n        // Apenas ocultar para modelos locais ou específicos que não suportam\n        const unsupportedModels = [\n            \"local/\",\n            \"offline/\"\n        ];\n        return !unsupportedModels.some((prefix)=>selectedModel.startsWith(prefix));\n    };\n    const currentModel = AI_MODELS.find((model)=>model.id === selectedModel);\n    const modelName = currentModel ? currentModel.name : selectedModel;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-x-0 -top-8 h-8 bg-gradient-to-t from-blue-950/30 via-blue-950/10 to-transparent pointer-events-none blur-sm\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 sm:p-4 lg:p-6 bg-gradient-to-r from-blue-950/98 via-blue-900/98 to-blue-950/98 backdrop-blur-xl relative shadow-2xl shadow-blue-900/60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-500/8 via-transparent to-cyan-500/8 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-blue-600/15 via-blue-500/5 to-transparent pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 inset-x-0 h-px bg-gradient-to-r from-transparent via-blue-400/30 to-transparent pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto relative z-10\",\n                        children: [\n                            attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3 sm:mb-4 p-3 sm:p-4 bg-blue-900/30 backdrop-blur-sm rounded-lg sm:rounded-xl border border-blue-600/30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 sm:gap-3\",\n                                    children: attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-800/40 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 border border-blue-600/20\",\n                                            children: [\n                                                attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-blue-400 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-red-400 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs sm:text-sm text-blue-100 truncate max-w-20 sm:max-w-32 font-medium\",\n                                                    children: attachment.filename\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>removeAttachment(attachment.id),\n                                                    className: \"text-blue-300 hover:text-red-400 transition-colors p-0.5 sm:p-1 rounded-full hover:bg-red-500/20 flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3.5 h-3.5 sm:w-4 sm:h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M6 18L18 6M6 6l12 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, attachment.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this),\n                            (selectedModel || activeAttachmentsCount > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-1 flex items-center justify-start space-x-2\",\n                                children: [\n                                    selectedModel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-blue-300\",\n                                                children: \"Modelo: \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-cyan-300 font-medium\",\n                                                children: modelName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeAttachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-green-600/30 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-300\",\n                                                children: \"Anexos: \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: activeAttachmentsCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end space-x-2 sm:space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 relative bg-gradient-to-r from-blue-900/80 to-blue-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-blue-600/30 shadow-xl shadow-blue-900/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl sm:rounded-2xl shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 flex items-end space-x-2 sm:space-x-3 p-3 sm:p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 sm:space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: onOpenModelModal,\n                                                                className: \"p-2 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 hover:scale-105\",\n                                                                title: \"Selecionar modelo\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleAttachment,\n                                                                disabled: isUploading,\n                                                                className: \"p-2.5 rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105\",\n                                                                title: \"Anexar arquivo\",\n                                                                children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 21\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            isWebSearchEnabled() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleWebSearch,\n                                                                className: \"p-2.5 rounded-xl transition-all duration-200 hover:shadow-lg border hover:scale-105 backdrop-blur-sm \".concat(webSearchEnabled ? \"bg-cyan-600/30 hover:bg-cyan-600/40 text-cyan-200 hover:text-cyan-100 border-cyan-500/50 shadow-lg shadow-cyan-500/20\" : \"bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 border-blue-600/20 hover:shadow-blue-500/20\"),\n                                                                title: \"Busca na web - \".concat(webSearchEnabled ? \"Ativada\" : \"Desativada\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            ref: textareaRef,\n                                                            value: message,\n                                                            onChange: handleInputChange,\n                                                            onKeyDown: handleKeyPress,\n                                                            className: \"w-full bg-transparent border-none rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none resize-none text-sm selection:bg-blue-500/30\",\n                                                            rows: 1,\n                                                            placeholder: \"Digite sua mensagem aqui...\",\n                                                            disabled: isLoading || isStreaming,\n                                                            style: {\n                                                                height: \"44px\",\n                                                                minHeight: \"44px\",\n                                                                maxHeight: \"120px\",\n                                                                lineHeight: \"1.5\",\n                                                                wordWrap: \"break-word\",\n                                                                whiteSpace: \"pre-wrap\",\n                                                                overflowY: \"hidden\",\n                                                                overflowX: \"hidden\",\n                                                                scrollbarWidth: \"thin\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: onCancelStreaming,\n                                                        className: \"p-3 rounded-xl bg-gradient-to-r from-red-600 via-red-500 to-red-600 hover:from-red-500 hover:via-red-400 hover:to-red-500 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-red-500/30\",\n                                                        title: \"Parar gera\\xe7\\xe3o\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 17\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleSend,\n                                                        disabled: !(message === null || message === void 0 ? void 0 : message.trim()) && attachments.length === 0 || isLoading || isUploading || isStreaming,\n                                                        className: \"p-3 rounded-xl bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105 disabled:hover:scale-100 border border-blue-500/30\",\n                                                        title: \"Enviar mensagem\",\n                                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M14 5l7 7m0 0l-7 7m7-7H3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-2\",\n                                        children: [\n                                            onScrollToTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onScrollToTop,\n                                                className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                                title: \"Ir para o topo\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"relative w-5 h-5 transform group-hover:-translate-y-0.5 transition-transform duration-200\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2.5,\n                                                            d: \"M5 10l7-7m0 0l7 7m-7-7v18\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 15\n                                            }, this),\n                                            onScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onScrollToBottom,\n                                                className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                                title: \"Ir para o final\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"relative w-5 h-5 transform group-hover:translate-y-0.5 transition-transform duration-200\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2.5,\n                                                            d: \"M19 14l-7 7m0 0l-7-7m7 7V4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: fileInputRef,\n                                type: \"file\",\n                                multiple: true,\n                                accept: \"image/png,image/jpeg,image/webp,application/pdf\",\n                                onChange: handleFileSelect,\n                                className: \"hidden\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, this);\n}\n_s(InputBar, \"P23FsoqKhoN2XSImOuRpWlk/+tg=\");\n_c = InputBar;\nvar _c;\n$RefreshReg$(_c, \"InputBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/InputBar.tsx\n"));

/***/ })

});