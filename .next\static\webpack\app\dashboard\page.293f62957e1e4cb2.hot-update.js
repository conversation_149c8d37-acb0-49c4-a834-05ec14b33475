"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _StatisticsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./StatisticsModal */ \"(app-pages-browser)/./src/components/dashboard/StatisticsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated, onUpdateOpenRouterBalance } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStatisticsModalOpen, setIsStatisticsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Estados para drag-n-drop\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragCounter, setDragCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado para um chat específico\n    const saveLastUsedModelForChat = async (modelId, chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            // Atualizar o lastUsedModel no documento do chat\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                lastUsedModel: modelId,\n                lastModelUpdateAt: Date.now()\n            });\n            console.log(\"Last used model saved for chat:\", {\n                chatId,\n                modelId\n            });\n        } catch (error) {\n            console.error(\"Error saving last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado de um chat específico\n    const loadLastUsedModelForChat = async (chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(chatRef);\n            if (chatDoc.exists()) {\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded last used model for chat:\", {\n                        chatId,\n                        model: data.lastUsedModel\n                    });\n                } else {\n                    // Se o chat não tem modelo salvo, usar o modelo padrão\n                    setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n                    console.log(\"No saved model for chat, using default:\", chatId);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado globalmente (fallback)\n    const loadGlobalLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded global last used model:\", data.lastUsedModel);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading global last used model:\", error);\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        // Salvar no chat específico se houver um chat ativo\n        if (actualChatId) {\n            saveLastUsedModelForChat(modelId, actualChatId);\n        }\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            // Definir o nome do chat imediatamente após criação\n            setChatName(finalChatName);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments, webSearchEnabled)=>{\n        // Obter anexos históricos ativos\n        const historicalAttachments = getAllChatAttachments().filter((att)=>att.isActive !== false);\n        // Combinar anexos novos com anexos históricos ativos\n        const allAttachmentsToSend = [\n            ...attachments || [],\n            ...historicalAttachments // Anexos históricos ativos\n        ];\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachmentsToSend.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        console.log(\"\\uD83D\\uDE80 RESULTADO FINAL - ANEXOS QUE SER\\xc3O ENVIADOS PARA A IA:\", uniqueAttachments.length);\n        uniqueAttachments.forEach((att)=>{\n            console.log(\"\\uD83D\\uDE80 Enviando para IA: \".concat(att.filename, \" (ID: \").concat(att.id, \", isActive: \").concat(att.isActive, \")\"));\n        });\n        if (uniqueAttachments.length === 0) {\n            console.log(\"❌ NENHUM ANEXO SER\\xc1 ENVIADO PARA A IA\");\n        }\n        // Debug: verificar mensagens atuais no estado\n        console.log(\"=== DEBUG: MENSAGENS ATUAIS NO ESTADO ===\");\n        console.log(\"Total de mensagens no estado:\", messages.length);\n        const messagesWithAttachments = messages.filter((msg)=>msg.attachments && msg.attachments.length > 0);\n        console.log(\"Mensagens com anexos no estado:\", messagesWithAttachments.length);\n        messagesWithAttachments.forEach((msg, index)=>{\n            console.log(\"Mensagem \".concat(index + 1, \" com anexos:\"), {\n                id: msg.id,\n                sender: msg.sender,\n                attachmentsCount: msg.attachments && msg.attachments.length || 0,\n                attachments: msg.attachments\n            });\n        });\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            console.log(\"=== DEBUG: CONDI\\xc7\\xc3O DE RETORNO ===\");\n            console.log(\"Mensagem vazia:\", !message.trim());\n            console.log(\"Sem anexos:\", !attachments || attachments.length === 0);\n            console.log(\"Loading:\", isLoading);\n            console.log(\"Streaming:\", isStreaming);\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // O nome já foi definido na função createAutoChat, mas vamos garantir carregando também\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA (incluindo anexos históricos ativos + anexos novos)\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: uniqueAttachments,\n            webSearchEnabled: webSearchEnabled\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString(),\n                        hasWebSearch: webSearchEnabled\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            // Salvar o modelo usado no chat específico\n            if (chatIdToUse) {\n                saveLastUsedModelForChat(selectedModel, chatIdToUse);\n            }\n            // Atualizar saldo do OpenRouter após a resposta\n            if (onUpdateOpenRouterBalance) {\n                onUpdateOpenRouterBalance();\n            }\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                const chatName = chatData.name || \"Conversa sem nome\";\n                setChatName(chatName);\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        console.log(\"\\uD83D\\uDCE5 Carregando mensagens do chat:\", {\n            chatId,\n            timestamp: new Date().toISOString()\n        });\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, chatId);\n            // Debug: verificar mensagens carregadas\n            console.log(\"=== \\uD83D\\uDCE5 MENSAGENS CARREGADAS DO FIREBASE STORAGE ===\");\n            console.log(\"Total de mensagens:\", chatMessages.length);\n            chatMessages.forEach((msg, index)=>{\n                console.log(\"Mensagem \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    role: msg.role,\n                    contentPreview: msg.content.substring(0, 50) + \"...\",\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: msg.attachments && msg.attachments.length || 0,\n                    timestamp: msg.timestamp\n                });\n            });\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(chatMessages);\n            // Debug: verificar mensagens convertidas\n            console.log(\"=== \\uD83D\\uDD04 MENSAGENS CONVERTIDAS PARA ESTADO LOCAL ===\");\n            console.log(\"Total de mensagens convertidas:\", convertedMessages.length);\n            convertedMessages.forEach((msg, index)=>{\n                console.log(\"Mensagem convertida \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    sender: msg.sender,\n                    contentPreview: msg.content.substring(0, 50) + \"...\",\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: msg.attachments && msg.attachments.length || 0,\n                    timestamp: msg.timestamp\n                });\n            });\n            setMessages(convertedMessages);\n            console.log(\"✅ Estado das mensagens atualizado com sucesso\");\n        } catch (error) {\n            console.error(\"❌ Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado globalmente quando o componente montar (apenas se não há chat)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && !currentChat) {\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        user,\n        currentChat\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n            // Carregar o modelo específico do chat\n            loadLastUsedModelForChat(currentChat);\n        } else if (!currentChat && actualChatId) {\n            // Só resetar se realmente não há chat e havia um chat antes\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n            // Carregar modelo global quando não há chat específico\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // ✅ CORREÇÃO: Recarregar mensagens do Firebase Storage para garantir estado atualizado\n        console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o para garantir estado atualizado...\");\n        try {\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                setIsLoading(false);\n                setIsStreaming(false);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // Deletar apenas as mensagens POSTERIORES do Firebase Storage (não a mensagem atual)\n            console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n            for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                const msgToDelete = convertedFreshMessages[i];\n                console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração\n                if (onUpdateOpenRouterBalance) {\n                    onUpdateOpenRouterBalance();\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return false;\n        console.log(\"✏️ Iniciando edi\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            console.log(\"\\uD83D\\uDCE4 Enviando atualiza\\xe7\\xe3o para o servidor...\");\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                console.error(\"❌ Falha ao atualizar mensagem no servidor\");\n                loadChatMessages(actualChatId);\n                return false;\n            } else {\n                console.log(\"✅ Mensagem editada e salva com sucesso no Firebase Storage:\", {\n                    messageId,\n                    timestamp: new Date().toISOString()\n                });\n                return true;\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            console.error(\"❌ Erro ao atualizar mensagem:\", error);\n            loadChatMessages(actualChatId);\n            return false;\n        }\n    };\n    const handleEditAndRegenerate = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        console.log(\"✏️\\uD83D\\uDD04 Iniciando edi\\xe7\\xe3o e regenera\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        try {\n            // 1. Primeiro, salvar a edição\n            const editSuccess = await handleEditMessage(messageId, newContent);\n            if (!editSuccess) {\n                console.error(\"❌ Falha ao editar mensagem, cancelando regenera\\xe7\\xe3o\");\n                return;\n            }\n            console.log(\"✅ Mensagem editada com sucesso, iniciando regenera\\xe7\\xe3o...\");\n            // 2. Aguardar um pouco para garantir que a edição foi salva\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            // 3. Recarregar mensagens do Firebase Storage para garantir estado atualizado\n            console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o...\");\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // 4. Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // 5. Verificar se há mensagens após esta mensagem\n            const hasMessagesAfter = messageIndex < convertedFreshMessages.length - 1;\n            console.log(\"\\uD83D\\uDCCA Mensagens ap\\xf3s esta: \".concat(convertedFreshMessages.length - messageIndex - 1));\n            // 6. Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // 7. Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // 8. Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // 9. Deletar apenas as mensagens POSTERIORES do Firebase Storage (se houver)\n            if (hasMessagesAfter) {\n                console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n                for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                    const msgToDelete = convertedFreshMessages[i];\n                    console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                    await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n                }\n            }\n            // 10. Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração\n                if (onUpdateOpenRouterBalance) {\n                    onUpdateOpenRouterBalance();\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao editar e regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: scrollContainer.scrollHeight,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = ()=>{\n        setIsAttachmentsModalOpen(true);\n    };\n    // Função para abrir o modal de estatísticas\n    const handleStatisticsModal = ()=>{\n        setIsStatisticsModalOpen(true);\n    };\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para salvar o estado dos anexos no Firebase Storage\n    const saveAttachmentStates = async (updatedMessages)=>{\n        if (!currentUsername || !actualChatId) return;\n        try {\n            // Preparar dados do chat para salvar\n            const chatData = {\n                id: actualChatId,\n                name: chatName || \"Chat\",\n                messages: updatedMessages.map((msg)=>({\n                        id: msg.id,\n                        content: msg.content,\n                        role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                        timestamp: msg.timestamp,\n                        isFavorite: msg.isFavorite,\n                        attachments: msg.attachments\n                    })),\n                createdAt: new Date().toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            // Salvar no Firebase Storage\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(currentUsername, \"/conversas/\").concat(actualChatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(chatJsonRef, chatJsonBlob);\n            console.log(\"✅ Estado dos anexos salvo no Firebase Storage\");\n            console.log(\"\\uD83D\\uDCC1 Dados salvos:\", {\n                chatId: actualChatId,\n                totalMessages: chatData.messages.length,\n                messagesWithAttachments: chatData.messages.filter((msg)=>msg.attachments && msg.attachments.length > 0).length\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao salvar estado dos anexos:\", error);\n        }\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setMessages((prevMessages)=>{\n            const updatedMessages = prevMessages.map((message)=>{\n                if (message.attachments && message.attachments.length > 0) {\n                    const updatedAttachments = message.attachments.map((attachment)=>{\n                        if (attachment.id === attachmentId) {\n                            // Se isActive não está definido, considerar como true (ativo por padrão)\n                            const currentState = attachment.isActive !== false;\n                            return {\n                                ...attachment,\n                                isActive: !currentState\n                            };\n                        }\n                        return attachment;\n                    });\n                    return {\n                        ...message,\n                        attachments: updatedAttachments\n                    };\n                }\n                return message;\n            });\n            // Salvar o estado atualizado no Firebase Storage\n            saveAttachmentStates(updatedMessages);\n            return updatedMessages;\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        // Para anexos novos (sem isActive definido), incluir por padrão\n        // Para anexos existentes, verificar se isActive não é false\n        return attachments.filter((attachment)=>{\n            // Se isActive não está definido (anexo novo), incluir\n            if (attachment.isActive === undefined) return true;\n            // Se isActive está definido, incluir apenas se não for false\n            return attachment.isActive !== false;\n        });\n    };\n    // Funções para drag-n-drop de arquivos\n    const handleDragEnter = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragCounter((prev)=>prev + 1);\n        if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n            setIsDragOver(true);\n        }\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragCounter((prev)=>prev - 1);\n        if (dragCounter <= 1) {\n            setIsDragOver(false);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n    };\n    const handleDrop = async (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setIsDragOver(false);\n        setDragCounter(0);\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length === 0) return;\n        // Filtrar apenas arquivos suportados\n        const supportedFiles = files.filter((file)=>{\n            const isImage = file.type.startsWith(\"image/\");\n            const isPdf = file.type === \"application/pdf\";\n            const isWebp = file.type === \"image/webp\";\n            return isImage || isPdf || isWebp;\n        });\n        if (supportedFiles.length === 0) {\n            console.warn(\"Nenhum arquivo suportado foi encontrado\");\n            return;\n        }\n        // Simular o evento de seleção de arquivos no InputBar\n        // Criar um evento sintético para o input de arquivo\n        const inputElement = document.querySelector('input[type=\"file\"]');\n        if (inputElement) {\n            // Criar um DataTransfer para simular a seleção de arquivos\n            const dataTransfer = new DataTransfer();\n            supportedFiles.forEach((file)=>{\n                dataTransfer.items.add(file);\n            });\n            // Atribuir os arquivos ao input\n            inputElement.files = dataTransfer.files;\n            // Disparar o evento change\n            const changeEvent = new Event(\"change\", {\n                bubbles: true\n            });\n            inputElement.dispatchEvent(changeEvent);\n        }\n    };\n    // Função para obter IDs dos anexos ativos\n    const getActiveAttachmentIds = ()=>{\n        const allAttachments = getAllChatAttachments();\n        return allAttachments.filter((att)=>att.isActive !== false).map((att)=>att.id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen relative\",\n        onDragEnter: handleDragEnter,\n        onDragLeave: handleDragLeave,\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            isDragOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-50 bg-blue-900/80 backdrop-blur-sm flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center p-8 bg-blue-800/90 rounded-2xl border-2 border-dashed border-blue-400 shadow-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 mx-auto mb-4 bg-blue-600/50 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-8 h-8 text-blue-300\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                    lineNumber: 1133,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1132,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                            lineNumber: 1131,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-white mb-2\",\n                            children: \"Solte os arquivos aqui\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                            lineNumber: 1136,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-200 text-sm\",\n                            children: \"Suportamos imagens (PNG, JPEG, WebP) e documentos PDF\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                            lineNumber: 1137,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1130,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1129,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                onDownload: handleDownloadModal,\n                onAttachments: handleAttachmentsModal,\n                onStatistics: handleStatisticsModal,\n                isLoading: isLoading,\n                attachmentsCount: getAllChatAttachments().length,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    isStreaming: isStreaming,\n                    streamingMessageId: streamingMessageId || undefined,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onEditAndRegenerate: handleEditAndRegenerate,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1159,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined,\n                activeAttachmentsCount: getActiveAttachmentIds().length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1192,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: getActiveAttachmentIds(),\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatisticsModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: isStatisticsModalOpen,\n                onClose: ()=>setIsStatisticsModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1217,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 1120,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"0zM5LIRSkHCcjq7H4N6QPQswfJ8=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});