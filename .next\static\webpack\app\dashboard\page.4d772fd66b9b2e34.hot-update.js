"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _StatisticsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./StatisticsModal */ \"(app-pages-browser)/./src/components/dashboard/StatisticsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated, onUpdateOpenRouterBalance } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStatisticsModalOpen, setIsStatisticsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado para um chat específico\n    const saveLastUsedModelForChat = async (modelId, chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            // Atualizar o lastUsedModel no documento do chat\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                lastUsedModel: modelId,\n                lastModelUpdateAt: Date.now()\n            });\n            console.log(\"Last used model saved for chat:\", {\n                chatId,\n                modelId\n            });\n        } catch (error) {\n            console.error(\"Error saving last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado de um chat específico\n    const loadLastUsedModelForChat = async (chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(chatRef);\n            if (chatDoc.exists()) {\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded last used model for chat:\", {\n                        chatId,\n                        model: data.lastUsedModel\n                    });\n                } else {\n                    // Se o chat não tem modelo salvo, carregar o modelo padrão do endpoint ativo\n                    loadDefaultModelFromActiveEndpoint();\n                    console.log(\"No saved model for chat, loading default from active endpoint:\", chatId);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o modelo padrão do endpoint ativo\n    const loadDefaultModelFromActiveEndpoint = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                // Primeiro, tentar carregar o último modelo usado globalmente\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded global last used model:\", data.lastUsedModel);\n                    return;\n                }\n                // Se não há último modelo usado, buscar o modelo padrão do endpoint ativo\n                if (data.endpoints) {\n                    const activeEndpoint = Object.values(data.endpoints).find((endpoint)=>endpoint.ativo);\n                    if (activeEndpoint && activeEndpoint.modeloPadrao) {\n                        setSelectedModel(activeEndpoint.modeloPadrao);\n                        console.log(\"Loaded default model from active endpoint:\", activeEndpoint.modeloPadrao);\n                        return;\n                    }\n                }\n            }\n            // Fallback para o modelo padrão hardcoded\n            setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n            console.log(\"Using fallback default model\");\n        } catch (error) {\n            console.error(\"Error loading default model from active endpoint:\", error);\n            // Fallback para o modelo padrão hardcoded em caso de erro\n            setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n        }\n    };\n    // Função para validar se um modelo ainda existe/é válido\n    const isValidModel = async (modelId)=>{\n        // Lista de modelos conhecidos como inválidos ou removidos\n        const invalidModels = [\n            \"qwen/qwen3-235b-a22b-thinking-2507\"\n        ];\n        return !invalidModels.includes(modelId);\n    };\n    // Função para carregar o último modelo usado globalmente (fallback)\n    const loadGlobalLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    // Verificar se o modelo salvo ainda é válido\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (isValid) {\n                        setSelectedModel(data.lastUsedModel);\n                        console.log(\"Loaded global last used model:\", data.lastUsedModel);\n                    } else {\n                        console.log(\"Invalid model detected, clearing and using default:\", data.lastUsedModel);\n                        // Limpar o modelo inválido das configurações\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                            lastUsedModel: null\n                        });\n                        // Carregar o modelo padrão do endpoint ativo\n                        loadDefaultModelFromActiveEndpoint();\n                    }\n                } else {\n                    // Se não há último modelo usado, carregar o modelo padrão do endpoint ativo\n                    loadDefaultModelFromActiveEndpoint();\n                }\n            } else {\n                // Se não há configurações, carregar o modelo padrão do endpoint ativo\n                loadDefaultModelFromActiveEndpoint();\n            }\n        } catch (error) {\n            console.error(\"Error loading global last used model:\", error);\n            // Fallback para carregar o modelo padrão do endpoint ativo\n            loadDefaultModelFromActiveEndpoint();\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        // Salvar no chat específico se houver um chat ativo\n        if (actualChatId) {\n            saveLastUsedModelForChat(modelId, actualChatId);\n        }\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            // Definir o nome do chat imediatamente após criação\n            setChatName(finalChatName);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments, webSearchEnabled)=>{\n        // Obter anexos históricos ativos\n        const historicalAttachments = getAllChatAttachments().filter((att)=>att.isActive !== false);\n        // Combinar anexos novos com anexos históricos ativos\n        const allAttachmentsToSend = [\n            ...attachments || [],\n            ...historicalAttachments // Anexos históricos ativos\n        ];\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachmentsToSend.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        console.log(\"\\uD83D\\uDE80 RESULTADO FINAL - ANEXOS QUE SER\\xc3O ENVIADOS PARA A IA:\", uniqueAttachments.length);\n        uniqueAttachments.forEach((att)=>{\n            console.log(\"\\uD83D\\uDE80 Enviando para IA: \".concat(att.filename, \" (ID: \").concat(att.id, \", isActive: \").concat(att.isActive, \")\"));\n        });\n        if (uniqueAttachments.length === 0) {\n            console.log(\"❌ NENHUM ANEXO SER\\xc1 ENVIADO PARA A IA\");\n        }\n        // Debug: verificar mensagens atuais no estado\n        console.log(\"=== DEBUG: MENSAGENS ATUAIS NO ESTADO ===\");\n        console.log(\"Total de mensagens no estado:\", messages.length);\n        const messagesWithAttachments = messages.filter((msg)=>msg.attachments && msg.attachments.length > 0);\n        console.log(\"Mensagens com anexos no estado:\", messagesWithAttachments.length);\n        messagesWithAttachments.forEach((msg, index)=>{\n            console.log(\"Mensagem \".concat(index + 1, \" com anexos:\"), {\n                id: msg.id,\n                sender: msg.sender,\n                attachmentsCount: msg.attachments && msg.attachments.length || 0,\n                attachments: msg.attachments\n            });\n        });\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            console.log(\"=== DEBUG: CONDI\\xc7\\xc3O DE RETORNO ===\");\n            console.log(\"Mensagem vazia:\", !message.trim());\n            console.log(\"Sem anexos:\", !attachments || attachments.length === 0);\n            console.log(\"Loading:\", isLoading);\n            console.log(\"Streaming:\", isStreaming);\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // O nome já foi definido na função createAutoChat, mas vamos garantir carregando também\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA (incluindo anexos históricos ativos + anexos novos)\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: uniqueAttachments,\n            webSearchEnabled: webSearchEnabled\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString(),\n                        hasWebSearch: webSearchEnabled\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            // Salvar o modelo usado no chat específico\n            if (chatIdToUse) {\n                saveLastUsedModelForChat(selectedModel, chatIdToUse);\n            }\n            // Atualizar saldo do OpenRouter após a resposta com delay de 5 segundos\n            if (onUpdateOpenRouterBalance) {\n                setTimeout(()=>{\n                    onUpdateOpenRouterBalance();\n                }, 5000);\n            }\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                const chatName = chatData.name || \"Conversa sem nome\";\n                setChatName(chatName);\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        console.log(\"\\uD83D\\uDCE5 Carregando mensagens do chat:\", {\n            chatId,\n            timestamp: new Date().toISOString()\n        });\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, chatId);\n            // Debug: verificar mensagens carregadas\n            console.log(\"=== \\uD83D\\uDCE5 MENSAGENS CARREGADAS DO FIREBASE STORAGE ===\");\n            console.log(\"Total de mensagens:\", chatMessages.length);\n            chatMessages.forEach((msg, index)=>{\n                console.log(\"Mensagem \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    role: msg.role,\n                    contentPreview: msg.content.substring(0, 50) + \"...\",\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: msg.attachments && msg.attachments.length || 0,\n                    timestamp: msg.timestamp\n                });\n            });\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(chatMessages);\n            // Debug: verificar mensagens convertidas\n            console.log(\"=== \\uD83D\\uDD04 MENSAGENS CONVERTIDAS PARA ESTADO LOCAL ===\");\n            console.log(\"Total de mensagens convertidas:\", convertedMessages.length);\n            convertedMessages.forEach((msg, index)=>{\n                console.log(\"Mensagem convertida \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    sender: msg.sender,\n                    contentPreview: msg.content.substring(0, 50) + \"...\",\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: msg.attachments && msg.attachments.length || 0,\n                    timestamp: msg.timestamp\n                });\n            });\n            setMessages(convertedMessages);\n            console.log(\"✅ Estado das mensagens atualizado com sucesso\");\n        } catch (error) {\n            console.error(\"❌ Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado globalmente quando o componente montar (apenas se não há chat)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && !currentChat) {\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        user,\n        currentChat\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n            // Carregar o modelo específico do chat\n            loadLastUsedModelForChat(currentChat);\n        } else if (!currentChat && actualChatId) {\n            // Só resetar se realmente não há chat e havia um chat antes\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n            // Carregar modelo global quando não há chat específico\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // ✅ CORREÇÃO: Recarregar mensagens do Firebase Storage para garantir estado atualizado\n        console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o para garantir estado atualizado...\");\n        try {\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                setIsLoading(false);\n                setIsStreaming(false);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // Deletar apenas as mensagens POSTERIORES do Firebase Storage (não a mensagem atual)\n            console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n            for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                const msgToDelete = convertedFreshMessages[i];\n                console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos\n                if (onUpdateOpenRouterBalance) {\n                    setTimeout(()=>{\n                        onUpdateOpenRouterBalance();\n                    }, 5000);\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return false;\n        console.log(\"✏️ Iniciando edi\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            console.log(\"\\uD83D\\uDCE4 Enviando atualiza\\xe7\\xe3o para o servidor...\");\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                console.error(\"❌ Falha ao atualizar mensagem no servidor\");\n                loadChatMessages(actualChatId);\n                return false;\n            } else {\n                console.log(\"✅ Mensagem editada e salva com sucesso no Firebase Storage:\", {\n                    messageId,\n                    timestamp: new Date().toISOString()\n                });\n                return true;\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            console.error(\"❌ Erro ao atualizar mensagem:\", error);\n            loadChatMessages(actualChatId);\n            return false;\n        }\n    };\n    const handleEditAndRegenerate = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        console.log(\"✏️\\uD83D\\uDD04 Iniciando edi\\xe7\\xe3o e regenera\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        try {\n            // 1. Primeiro, salvar a edição\n            const editSuccess = await handleEditMessage(messageId, newContent);\n            if (!editSuccess) {\n                console.error(\"❌ Falha ao editar mensagem, cancelando regenera\\xe7\\xe3o\");\n                return;\n            }\n            console.log(\"✅ Mensagem editada com sucesso, iniciando regenera\\xe7\\xe3o...\");\n            // 2. Aguardar um pouco para garantir que a edição foi salva\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            // 3. Recarregar mensagens do Firebase Storage para garantir estado atualizado\n            console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o...\");\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // 4. Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // 5. Verificar se há mensagens após esta mensagem\n            const hasMessagesAfter = messageIndex < convertedFreshMessages.length - 1;\n            console.log(\"\\uD83D\\uDCCA Mensagens ap\\xf3s esta: \".concat(convertedFreshMessages.length - messageIndex - 1));\n            // 6. Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // 7. Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // 8. Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // 9. Deletar apenas as mensagens POSTERIORES do Firebase Storage (se houver)\n            if (hasMessagesAfter) {\n                console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n                for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                    const msgToDelete = convertedFreshMessages[i];\n                    console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                    await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n                }\n            }\n            // 10. Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos\n                if (onUpdateOpenRouterBalance) {\n                    setTimeout(()=>{\n                        onUpdateOpenRouterBalance();\n                    }, 5000);\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao editar e regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: scrollContainer.scrollHeight,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = ()=>{\n        setIsAttachmentsModalOpen(true);\n    };\n    // Função para abrir o modal de estatísticas\n    const handleStatisticsModal = ()=>{\n        setIsStatisticsModalOpen(true);\n    };\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para salvar o estado dos anexos no Firebase Storage\n    const saveAttachmentStates = async (updatedMessages)=>{\n        if (!currentUsername || !actualChatId) return;\n        try {\n            // Preparar dados do chat para salvar\n            const chatData = {\n                id: actualChatId,\n                name: chatName || \"Chat\",\n                messages: updatedMessages.map((msg)=>({\n                        id: msg.id,\n                        content: msg.content,\n                        role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                        timestamp: msg.timestamp,\n                        isFavorite: msg.isFavorite,\n                        attachments: msg.attachments\n                    })),\n                createdAt: new Date().toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            // Salvar no Firebase Storage\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(currentUsername, \"/conversas/\").concat(actualChatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(chatJsonRef, chatJsonBlob);\n            console.log(\"✅ Estado dos anexos salvo no Firebase Storage\");\n            console.log(\"\\uD83D\\uDCC1 Dados salvos:\", {\n                chatId: actualChatId,\n                totalMessages: chatData.messages.length,\n                messagesWithAttachments: chatData.messages.filter((msg)=>msg.attachments && msg.attachments.length > 0).length\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao salvar estado dos anexos:\", error);\n        }\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setMessages((prevMessages)=>{\n            const updatedMessages = prevMessages.map((message)=>{\n                if (message.attachments && message.attachments.length > 0) {\n                    const updatedAttachments = message.attachments.map((attachment)=>{\n                        if (attachment.id === attachmentId) {\n                            // Se isActive não está definido, considerar como true (ativo por padrão)\n                            const currentState = attachment.isActive !== false;\n                            return {\n                                ...attachment,\n                                isActive: !currentState\n                            };\n                        }\n                        return attachment;\n                    });\n                    return {\n                        ...message,\n                        attachments: updatedAttachments\n                    };\n                }\n                return message;\n            });\n            // Salvar o estado atualizado no Firebase Storage\n            saveAttachmentStates(updatedMessages);\n            return updatedMessages;\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        // Para anexos novos (sem isActive definido), incluir por padrão\n        // Para anexos existentes, verificar se isActive não é false\n        return attachments.filter((attachment)=>{\n            // Se isActive não está definido (anexo novo), incluir\n            if (attachment.isActive === undefined) return true;\n            // Se isActive está definido, incluir apenas se não for false\n            return attachment.isActive !== false;\n        });\n    };\n    // Função para obter IDs dos anexos ativos\n    const getActiveAttachmentIds = ()=>{\n        const allAttachments = getAllChatAttachments();\n        return allAttachments.filter((att)=>att.isActive !== false).map((att)=>att.id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                onDownload: handleDownloadModal,\n                onAttachments: handleAttachmentsModal,\n                onStatistics: handleStatisticsModal,\n                isLoading: isLoading,\n                attachmentsCount: getAllChatAttachments().length,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    isStreaming: isStreaming,\n                    streamingMessageId: streamingMessageId || undefined,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onEditAndRegenerate: handleEditAndRegenerate,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1148,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined,\n                activeAttachmentsCount: getActiveAttachmentIds().length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1163,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1181,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1189,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: getActiveAttachmentIds(),\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatisticsModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: isStatisticsModalOpen,\n                onClose: ()=>setIsStatisticsModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1206,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 1130,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"Hf7a+h7SnkMddq+yigBa/N2m+7g=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});