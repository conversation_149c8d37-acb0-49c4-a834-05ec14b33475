"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/InputBar.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/InputBar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InputBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst AI_MODELS = [\n    {\n        id: \"gpt-4.1-nano\",\n        name: \"GPT-4.1 Nano\",\n        description: \"R\\xe1pido e eficiente\"\n    },\n    {\n        id: \"gpt-4-turbo\",\n        name: \"GPT-4 Turbo\",\n        description: \"Mais poderoso\"\n    },\n    {\n        id: \"claude-3-sonnet\",\n        name: \"Claude 3 Sonnet\",\n        description: \"Criativo e preciso\"\n    },\n    {\n        id: \"gemini-pro\",\n        name: \"Gemini Pro\",\n        description: \"Multimodal\"\n    }\n];\nfunction InputBar(param) {\n    let { message, setMessage, onSendMessage, isLoading, selectedModel, onModelChange, onScrollToTop, onScrollToBottom, isStreaming = false, onCancelStreaming, onOpenModelModal, username, chatId, activeAttachmentsCount = 0 } = param;\n    _s();\n    const [webSearchEnabled, setWebSearchEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [processedAttachments, setProcessedAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const adjustHeightTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (adjustHeightTimeoutRef.current) {\n                clearTimeout(adjustHeightTimeoutRef.current);\n            }\n        };\n    }, []);\n    // Adjust textarea height when message changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        adjustTextareaHeight();\n    }, [\n        message\n    ]);\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        setMessage(value);\n        // Ajustar altura imediatamente para evitar flickering\n        requestAnimationFrame(()=>{\n            adjustTextareaHeight();\n        });\n    };\n    const handleSend = ()=>{\n        if (((message === null || message === void 0 ? void 0 : message.trim()) || attachments.length > 0) && !isLoading && !isUploading) {\n            console.log(\"=== DEBUG: ENVIANDO MENSAGEM ===\");\n            console.log(\"Mensagem:\", message);\n            console.log(\"Anexos locais:\", attachments.length);\n            console.log(\"Anexos processados:\", processedAttachments.length);\n            console.log(\"Anexos processados detalhes:\", processedAttachments);\n            console.log(\"Web Search Enabled:\", webSearchEnabled);\n            onSendMessage(processedAttachments, webSearchEnabled);\n            // Limpar anexos após envio\n            setAttachments([]);\n            setProcessedAttachments([]);\n        }\n    };\n    const handleAttachment = ()=>{\n        if (fileInputRef.current) {\n            fileInputRef.current.click();\n        }\n    };\n    const handleFileSelect = async (e)=>{\n        const files = e.target.files;\n        if (!files || !username || !chatId) return;\n        setIsUploading(true);\n        try {\n            // Primeiro, adicionar arquivos localmente para preview\n            const localAttachments = [];\n            for (const file of Array.from(files)){\n                const attachment = {\n                    id: Date.now().toString() + Math.random().toString(36).substring(2, 9),\n                    filename: file.name,\n                    type: file.type.startsWith(\"image/\") ? \"image\" : \"document\",\n                    file\n                };\n                localAttachments.push(attachment);\n            }\n            setAttachments((prev)=>[\n                    ...prev,\n                    ...localAttachments\n                ]);\n            // Fazer upload dos arquivos\n            const uploadedAttachments = await _lib_services_attachmentService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].uploadMultipleAttachments(Array.from(files), username, chatId);\n            // Atualizar com metadados dos arquivos processados\n            setProcessedAttachments((prev)=>[\n                    ...prev,\n                    ...uploadedAttachments.map((att)=>att.metadata)\n                ]);\n        } catch (error) {\n            console.error(\"Erro ao processar arquivos:\", error);\n            // Remover anexos que falharam\n            setAttachments((prev)=>prev.filter((att)=>!Array.from(files).some((file)=>file.name === att.filename)));\n        } finally{\n            setIsUploading(false);\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n        }\n    };\n    const removeAttachment = (id)=>{\n        // Encontrar o anexo para obter o filename\n        const attachment = attachments.find((att)=>att.id === id);\n        if (attachment) {\n            // Remover do estado local\n            setAttachments((prev)=>prev.filter((att)=>att.id !== id));\n            // Remover dos anexos processados também\n            setProcessedAttachments((prev)=>prev.filter((att)=>att.filename !== attachment.filename));\n        }\n    };\n    const handleWebSearch = ()=>{\n        setWebSearchEnabled(!webSearchEnabled);\n    };\n    const adjustTextareaHeight = ()=>{\n        const textarea = textareaRef.current;\n        if (!textarea) return;\n        // Store current scroll position to prevent jumping\n        const scrollTop = textarea.scrollTop;\n        // Temporarily set height to auto to get accurate scrollHeight\n        textarea.style.height = \"auto\";\n        // Calculate new height with proper constraints\n        const scrollHeight = textarea.scrollHeight;\n        const minHeight = 44; // Minimum height in pixels\n        const maxHeight = 120; // Maximum height in pixels\n        // Set the new height within bounds\n        const newHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));\n        textarea.style.height = newHeight + \"px\";\n        // Handle overflow and scrolling\n        if (scrollHeight > maxHeight) {\n            textarea.style.overflowY = \"auto\";\n            // Restore scroll position if content was scrolled\n            if (scrollTop > 0) {\n                textarea.scrollTop = scrollTop;\n            }\n        } else {\n            textarea.style.overflowY = \"hidden\";\n        }\n    };\n    const isWebSearchEnabled = ()=>{\n        // Web search está disponível para todos os modelos via OpenRouter plugins\n        // Apenas ocultar para modelos locais ou específicos que não suportam\n        const unsupportedModels = [\n            \"local/\",\n            \"offline/\"\n        ];\n        return !unsupportedModels.some((prefix)=>selectedModel.startsWith(prefix));\n    };\n    const currentModel = AI_MODELS.find((model)=>model.id === selectedModel);\n    const modelName = currentModel ? currentModel.name : selectedModel;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-x-0 -top-8 h-8 bg-gradient-to-t from-blue-950/30 via-blue-950/10 to-transparent pointer-events-none blur-sm\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 sm:p-4 lg:p-6 bg-gradient-to-r from-blue-950/98 via-blue-900/98 to-blue-950/98 backdrop-blur-xl relative shadow-2xl shadow-blue-900/60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-500/8 via-transparent to-cyan-500/8 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-blue-600/15 via-blue-500/5 to-transparent pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 inset-x-0 h-px bg-gradient-to-r from-transparent via-blue-400/30 to-transparent pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto relative z-10\",\n                        children: [\n                            attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3 sm:mb-4 p-3 sm:p-4 bg-blue-900/30 backdrop-blur-sm rounded-lg sm:rounded-xl border border-blue-600/30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 sm:gap-3\",\n                                    children: attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-800/40 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 border border-blue-600/20\",\n                                            children: [\n                                                attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-blue-400 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 text-red-400 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs sm:text-sm text-blue-100 truncate max-w-20 sm:max-w-32 font-medium\",\n                                                    children: attachment.filename\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>removeAttachment(attachment.id),\n                                                    className: \"text-blue-300 hover:text-red-400 transition-colors p-0.5 sm:p-1 rounded-full hover:bg-red-500/20 flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3.5 h-3.5 sm:w-4 sm:h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M6 18L18 6M6 6l12 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, attachment.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            (selectedModel || activeAttachmentsCount > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-1 flex items-center justify-start space-x-2\",\n                                children: [\n                                    selectedModel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-blue-300\",\n                                                children: \"Modelo: \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-cyan-300 font-medium\",\n                                                children: modelName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeAttachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-green-600/30 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-300\",\n                                                children: \"Anexos: \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: activeAttachmentsCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end space-x-2 sm:space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 relative bg-gradient-to-r from-blue-900/80 to-blue-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-blue-600/30 shadow-xl shadow-blue-900/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl sm:rounded-2xl shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 flex items-end space-x-2 sm:space-x-3 p-3 sm:p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 sm:space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: onOpenModelModal,\n                                                                className: \"p-2 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 hover:scale-105\",\n                                                                title: \"Selecionar modelo\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleAttachment,\n                                                                disabled: isUploading,\n                                                                className: \"p-2.5 rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105\",\n                                                                title: \"Anexar arquivo\",\n                                                                children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 21\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            isWebSearchEnabled() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleWebSearch,\n                                                                className: \"p-2.5 rounded-xl transition-all duration-200 hover:shadow-lg border hover:scale-105 backdrop-blur-sm \".concat(webSearchEnabled ? \"bg-cyan-600/30 hover:bg-cyan-600/40 text-cyan-200 hover:text-cyan-100 border-cyan-500/50 shadow-lg shadow-cyan-500/20\" : \"bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 border-blue-600/20 hover:shadow-blue-500/20\"),\n                                                                title: \"Busca na web - \".concat(webSearchEnabled ? \"Ativada\" : \"Desativada\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            ref: textareaRef,\n                                                            value: message,\n                                                            onChange: handleInputChange,\n                                                            onKeyDown: handleKeyPress,\n                                                            className: \"w-full bg-transparent border-none rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none resize-none text-sm selection:bg-blue-500/30\",\n                                                            rows: 1,\n                                                            placeholder: \"Digite sua mensagem aqui...\",\n                                                            disabled: isLoading || isStreaming,\n                                                            style: {\n                                                                height: \"44px\",\n                                                                minHeight: \"44px\",\n                                                                maxHeight: \"120px\",\n                                                                lineHeight: \"1.5\",\n                                                                wordWrap: \"break-word\",\n                                                                whiteSpace: \"pre-wrap\",\n                                                                overflowY: \"hidden\",\n                                                                overflowX: \"hidden\",\n                                                                scrollbarWidth: \"thin\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: onCancelStreaming,\n                                                        className: \"p-3 rounded-xl bg-gradient-to-r from-red-600 via-red-500 to-red-600 hover:from-red-500 hover:via-red-400 hover:to-red-500 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-red-500/30\",\n                                                        title: \"Parar gera\\xe7\\xe3o\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 17\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleSend,\n                                                        disabled: !(message === null || message === void 0 ? void 0 : message.trim()) && attachments.length === 0 || isLoading || isUploading || isStreaming,\n                                                        className: \"p-3 rounded-xl bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105 disabled:hover:scale-100 border border-blue-500/30\",\n                                                        title: \"Enviar mensagem\",\n                                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M14 5l7 7m0 0l-7 7m7-7H3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-2\",\n                                        children: [\n                                            onScrollToTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onScrollToTop,\n                                                className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                                title: \"Ir para o topo\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"relative w-5 h-5 transform group-hover:-translate-y-0.5 transition-transform duration-200\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2.5,\n                                                            d: \"M5 10l7-7m0 0l7 7m-7-7v18\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 15\n                                            }, this),\n                                            onScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onScrollToBottom,\n                                                className: \"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95\",\n                                                title: \"Ir para o final\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"relative w-5 h-5 transform group-hover:translate-y-0.5 transition-transform duration-200\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2.5,\n                                                            d: \"M19 14l-7 7m0 0l-7-7m7 7V4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: fileInputRef,\n                                type: \"file\",\n                                multiple: true,\n                                accept: \"image/png,image/jpeg,image/webp,application/pdf\",\n                                onChange: handleFileSelect,\n                                className: \"hidden\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\InputBar.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n_s(InputBar, \"P23FsoqKhoN2XSImOuRpWlk/+tg=\");\n_c = InputBar;\nvar _c;\n$RefreshReg$(_c, \"InputBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9JbnB1dEJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVvRDtBQUVhO0FBNEJqRSxNQUFNSSxZQUFZO0lBQ2hCO1FBQUVDLElBQUk7UUFBZ0JDLE1BQU07UUFBZ0JDLGFBQWE7SUFBcUI7SUFDOUU7UUFBRUYsSUFBSTtRQUFlQyxNQUFNO1FBQWVDLGFBQWE7SUFBZ0I7SUFDdkU7UUFBRUYsSUFBSTtRQUFtQkMsTUFBTTtRQUFtQkMsYUFBYTtJQUFxQjtJQUNwRjtRQUFFRixJQUFJO1FBQWNDLE1BQU07UUFBY0MsYUFBYTtJQUFhO0NBQ25FO0FBRWMsU0FBU0MsU0FBUyxLQWVqQjtRQWZpQixFQUMvQkMsT0FBTyxFQUNQQyxVQUFVLEVBQ1ZDLGFBQWEsRUFDYkMsU0FBUyxFQUNUQyxhQUFhLEVBQ2JDLGFBQWEsRUFDYkMsYUFBYSxFQUNiQyxnQkFBZ0IsRUFDaEJDLGNBQWMsS0FBSyxFQUNuQkMsaUJBQWlCLEVBQ2pCQyxnQkFBZ0IsRUFDaEJDLFFBQVEsRUFDUkMsTUFBTSxFQUNOQyx5QkFBeUIsQ0FBQyxFQUNaLEdBZmlCOztJQWlCL0IsTUFBTSxDQUFDQyxrQkFBa0JDLG9CQUFvQixHQUFHeEIsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDeUIsYUFBYUMsZUFBZSxHQUFHMUIsK0NBQVFBLENBQWUsRUFBRTtJQUMvRCxNQUFNLENBQUMyQixzQkFBc0JDLHdCQUF3QixHQUFHNUIsK0NBQVFBLENBQXVCLEVBQUU7SUFDekYsTUFBTSxDQUFDNkIsYUFBYUMsZUFBZSxHQUFHOUIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTStCLGNBQWM5Qiw2Q0FBTUEsQ0FBc0I7SUFDaEQsTUFBTStCLGVBQWUvQiw2Q0FBTUEsQ0FBbUI7SUFDOUMsTUFBTWdDLHlCQUF5QmhDLDZDQUFNQSxDQUF3QjtJQUU3RCw2QkFBNkI7SUFDN0JDLGdEQUFTQSxDQUFDO1FBQ1IsT0FBTztZQUNMLElBQUkrQix1QkFBdUJDLE9BQU8sRUFBRTtnQkFDbENDLGFBQWFGLHVCQUF1QkMsT0FBTztZQUM3QztRQUNGO0lBQ0YsR0FBRyxFQUFFO0lBRUwsOENBQThDO0lBQzlDaEMsZ0RBQVNBLENBQUM7UUFDUmtDO0lBQ0YsR0FBRztRQUFDM0I7S0FBUTtJQUVaLE1BQU00QixpQkFBaUIsQ0FBQ0M7UUFDdEIsSUFBSUEsRUFBRUMsR0FBRyxLQUFLLFdBQVcsQ0FBQ0QsRUFBRUUsUUFBUSxFQUFFO1lBQ3BDRixFQUFFRyxjQUFjO1lBQ2hCQztRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxvQkFBb0IsQ0FBQ0w7UUFDekIsTUFBTU0sUUFBUU4sRUFBRU8sTUFBTSxDQUFDRCxLQUFLO1FBQzVCbEMsV0FBV2tDO1FBRVgsc0RBQXNEO1FBQ3RERSxzQkFBc0I7WUFDcEJWO1FBQ0Y7SUFDRjtJQUVBLE1BQU1NLGFBQWE7UUFDakIsSUFBSSxDQUFDakMsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTc0MsSUFBSSxPQUFNdEIsWUFBWXVCLE1BQU0sR0FBRyxNQUFNLENBQUNwQyxhQUFhLENBQUNpQixhQUFhO1lBQzdFb0IsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxhQUFhekM7WUFDekJ3QyxRQUFRQyxHQUFHLENBQUMsa0JBQWtCekIsWUFBWXVCLE1BQU07WUFDaERDLFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUJ2QixxQkFBcUJxQixNQUFNO1lBQzlEQyxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDdkI7WUFDNUNzQixRQUFRQyxHQUFHLENBQUMsdUJBQXVCM0I7WUFFbkNaLGNBQWNnQixzQkFBc0JKO1lBQ3BDLDJCQUEyQjtZQUMzQkcsZUFBZSxFQUFFO1lBQ2pCRSx3QkFBd0IsRUFBRTtRQUM1QjtJQUNGO0lBRUEsTUFBTXVCLG1CQUFtQjtRQUN2QixJQUFJbkIsYUFBYUUsT0FBTyxFQUFFO1lBQ3hCRixhQUFhRSxPQUFPLENBQUNrQixLQUFLO1FBQzVCO0lBQ0Y7SUFFQSxNQUFNQyxtQkFBbUIsT0FBT2Y7UUFDOUIsTUFBTWdCLFFBQVFoQixFQUFFTyxNQUFNLENBQUNTLEtBQUs7UUFDNUIsSUFBSSxDQUFDQSxTQUFTLENBQUNsQyxZQUFZLENBQUNDLFFBQVE7UUFFcENTLGVBQWU7UUFDZixJQUFJO1lBQ0YsdURBQXVEO1lBQ3ZELE1BQU15QixtQkFBaUMsRUFBRTtZQUN6QyxLQUFLLE1BQU1DLFFBQVFDLE1BQU1DLElBQUksQ0FBQ0osT0FBUTtnQkFDcEMsTUFBTUssYUFBeUI7b0JBQzdCdEQsSUFBSXVELEtBQUtDLEdBQUcsR0FBR0MsUUFBUSxLQUFLQyxLQUFLQyxNQUFNLEdBQUdGLFFBQVEsQ0FBQyxJQUFJRyxTQUFTLENBQUMsR0FBRztvQkFDcEVDLFVBQVVWLEtBQUtsRCxJQUFJO29CQUNuQjZELE1BQU1YLEtBQUtXLElBQUksQ0FBQ0MsVUFBVSxDQUFDLFlBQVksVUFBVTtvQkFDakRaO2dCQUNGO2dCQUNBRCxpQkFBaUJjLElBQUksQ0FBQ1Y7WUFDeEI7WUFDQWpDLGVBQWU0QyxDQUFBQSxPQUFRO3VCQUFJQTt1QkFBU2Y7aUJBQWlCO1lBRXJELDRCQUE0QjtZQUM1QixNQUFNZ0Isc0JBQXNCLE1BQU1wRSx1RUFBaUJBLENBQUNxRSx5QkFBeUIsQ0FDM0VmLE1BQU1DLElBQUksQ0FBQ0osUUFDWGxDLFVBQ0FDO1lBR0YsbURBQW1EO1lBQ25ETyx3QkFBd0IwQyxDQUFBQSxPQUFRO3VCQUMzQkE7dUJBQ0FDLG9CQUFvQkUsR0FBRyxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxRQUFRO2lCQUMvQztRQUVILEVBQUUsT0FBT0MsT0FBTztZQUNkM0IsUUFBUTJCLEtBQUssQ0FBQywrQkFBK0JBO1lBQzdDLDhCQUE4QjtZQUM5QmxELGVBQWU0QyxDQUFBQSxPQUFRQSxLQUFLTyxNQUFNLENBQUNILENBQUFBLE1BQ2pDLENBQUNqQixNQUFNQyxJQUFJLENBQUNKLE9BQU93QixJQUFJLENBQUN0QixDQUFBQSxPQUFRQSxLQUFLbEQsSUFBSSxLQUFLb0UsSUFBSVIsUUFBUTtRQUU5RCxTQUFVO1lBQ1JwQyxlQUFlO1lBQ2YsSUFBSUUsYUFBYUUsT0FBTyxFQUFFO2dCQUN4QkYsYUFBYUUsT0FBTyxDQUFDVSxLQUFLLEdBQUc7WUFDL0I7UUFDRjtJQUNGO0lBRUEsTUFBTW1DLG1CQUFtQixDQUFDMUU7UUFDeEIsMENBQTBDO1FBQzFDLE1BQU1zRCxhQUFhbEMsWUFBWXVELElBQUksQ0FBQ04sQ0FBQUEsTUFBT0EsSUFBSXJFLEVBQUUsS0FBS0E7UUFDdEQsSUFBSXNELFlBQVk7WUFDZCwwQkFBMEI7WUFDMUJqQyxlQUFlNEMsQ0FBQUEsT0FBUUEsS0FBS08sTUFBTSxDQUFDSCxDQUFBQSxNQUFPQSxJQUFJckUsRUFBRSxLQUFLQTtZQUNyRCx3Q0FBd0M7WUFDeEN1Qix3QkFBd0IwQyxDQUFBQSxPQUFRQSxLQUFLTyxNQUFNLENBQUNILENBQUFBLE1BQU9BLElBQUlSLFFBQVEsS0FBS1AsV0FBV08sUUFBUTtRQUN6RjtJQUNGO0lBRUEsTUFBTWUsa0JBQWtCO1FBQ3RCekQsb0JBQW9CLENBQUNEO0lBQ3ZCO0lBRUEsTUFBTWEsdUJBQXVCO1FBQzNCLE1BQU04QyxXQUFXbkQsWUFBWUcsT0FBTztRQUNwQyxJQUFJLENBQUNnRCxVQUFVO1FBRWYsbURBQW1EO1FBQ25ELE1BQU1DLFlBQVlELFNBQVNDLFNBQVM7UUFFcEMsOERBQThEO1FBQzlERCxTQUFTRSxLQUFLLENBQUNDLE1BQU0sR0FBRztRQUV4QiwrQ0FBK0M7UUFDL0MsTUFBTUMsZUFBZUosU0FBU0ksWUFBWTtRQUMxQyxNQUFNQyxZQUFZLElBQUksMkJBQTJCO1FBQ2pELE1BQU1DLFlBQVksS0FBSywyQkFBMkI7UUFFbEQsbUNBQW1DO1FBQ25DLE1BQU1DLFlBQVkxQixLQUFLMkIsR0FBRyxDQUFDSCxXQUFXeEIsS0FBSzRCLEdBQUcsQ0FBQ0wsY0FBY0U7UUFDN0ROLFNBQVNFLEtBQUssQ0FBQ0MsTUFBTSxHQUFHSSxZQUFZO1FBRXBDLGdDQUFnQztRQUNoQyxJQUFJSCxlQUFlRSxXQUFXO1lBQzVCTixTQUFTRSxLQUFLLENBQUNRLFNBQVMsR0FBRztZQUMzQixrREFBa0Q7WUFDbEQsSUFBSVQsWUFBWSxHQUFHO2dCQUNqQkQsU0FBU0MsU0FBUyxHQUFHQTtZQUN2QjtRQUNGLE9BQU87WUFDTEQsU0FBU0UsS0FBSyxDQUFDUSxTQUFTLEdBQUc7UUFDN0I7SUFDRjtJQUVBLE1BQU1DLHFCQUFxQjtRQUN6QiwwRUFBMEU7UUFDMUUscUVBQXFFO1FBQ3JFLE1BQU1DLG9CQUFvQjtZQUFDO1lBQVU7U0FBVztRQUNoRCxPQUFPLENBQUNBLGtCQUFrQmhCLElBQUksQ0FBQ2lCLENBQUFBLFNBQVVsRixjQUFjdUQsVUFBVSxDQUFDMkI7SUFDcEU7SUFFQSxNQUFNQyxlQUFlNUYsVUFBVTRFLElBQUksQ0FBQ2lCLENBQUFBLFFBQVNBLE1BQU01RixFQUFFLEtBQUtRO0lBQzFELE1BQU1xRixZQUFZRixlQUFlQSxhQUFhMUYsSUFBSSxHQUFHTztJQUVyRCxxQkFDRSw4REFBQ3NGO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7Ozs7OzBCQUVmLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBR2YsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBR2YsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBRWpCLDhEQUFDRDt3QkFBSUMsV0FBVTs7NEJBRVozRSxZQUFZdUIsTUFBTSxHQUFHLG1CQUNwQiw4REFBQ21EO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWjNFLFlBQVlnRCxHQUFHLENBQUMsQ0FBQ2QsMkJBQ2hCLDhEQUFDd0M7NENBRUNDLFdBQVU7O2dEQUVUekMsV0FBV1EsSUFBSSxLQUFLLHdCQUNuQiw4REFBQ2tDO29EQUFJRCxXQUFVO29EQUF3REUsTUFBSztvREFBT0MsUUFBTztvREFBZUMsU0FBUTs4REFDL0csNEVBQUNDO3dEQUFLQyxlQUFjO3dEQUFRQyxnQkFBZTt3REFBUUMsYUFBYTt3REFBR0MsR0FBRTs7Ozs7Ozs7Ozt5RUFHdkUsOERBQUNSO29EQUFJRCxXQUFVO29EQUF1REUsTUFBSztvREFBT0MsUUFBTztvREFBZUMsU0FBUTs4REFDOUcsNEVBQUNDO3dEQUFLQyxlQUFjO3dEQUFRQyxnQkFBZTt3REFBUUMsYUFBYTt3REFBR0MsR0FBRTs7Ozs7Ozs7Ozs7OERBR3pFLDhEQUFDQztvREFBS1YsV0FBVTs4REFDYnpDLFdBQVdPLFFBQVE7Ozs7Ozs4REFFdEIsOERBQUM2QztvREFDQ0MsU0FBUyxJQUFNakMsaUJBQWlCcEIsV0FBV3RELEVBQUU7b0RBQzdDK0YsV0FBVTs4REFFViw0RUFBQ0M7d0RBQUlELFdBQVU7d0RBQTRCRSxNQUFLO3dEQUFPQyxRQUFPO3dEQUFlQyxTQUFRO2tFQUNuRiw0RUFBQ0M7NERBQUtDLGVBQWM7NERBQVFDLGdCQUFlOzREQUFRQyxhQUFhOzREQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzsyQ0FwQnBFbEQsV0FBV3RELEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs0QkE4QjFCUSxDQUFBQSxpQkFBaUJTLHlCQUF5QixvQkFDMUMsOERBQUM2RTtnQ0FBSUMsV0FBVTs7b0NBQ1p2RiwrQkFDQyw4REFBQ3NGO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ1U7Z0RBQUtWLFdBQVU7MERBQXdCOzs7Ozs7MERBQ3hDLDhEQUFDVTtnREFBS1YsV0FBVTswREFBcUNGOzs7Ozs7Ozs7Ozs7b0NBSXhENUUseUJBQXlCLG1CQUN4Qiw4REFBQzZFO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ1U7Z0RBQUtWLFdBQVU7MERBQXlCOzs7Ozs7MERBQ3pDLDhEQUFDVTtnREFBS1YsV0FBVTswREFBc0M5RTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU05RCw4REFBQzZFO2dDQUFJQyxXQUFVOztrREFFYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUViLDhEQUFDRDtnREFBSUMsV0FBVTs7Ozs7OzBEQUVmLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBRWIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFFYiw4REFBQ1c7Z0VBQ0NDLFNBQVM3RjtnRUFDVGlGLFdBQVU7Z0VBQ1ZhLE9BQU07MEVBRU4sNEVBQUNaO29FQUFJRCxXQUFVO29FQUF3QkUsTUFBSztvRUFBT0MsUUFBTztvRUFBZUMsU0FBUTs4RUFDL0UsNEVBQUNDO3dFQUFLQyxlQUFjO3dFQUFRQyxnQkFBZTt3RUFBUUMsYUFBYTt3RUFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OzswRUFLekUsOERBQUNFO2dFQUNDQyxTQUFTN0Q7Z0VBQ1QrRCxVQUFVckY7Z0VBQ1Z1RSxXQUFVO2dFQUNWYSxPQUFNOzBFQUVMcEYsNEJBQ0MsOERBQUNzRTtvRUFBSUMsV0FBVTs7Ozs7eUZBRWYsOERBQUNDO29FQUFJRCxXQUFVO29FQUFVRSxNQUFLO29FQUFPQyxRQUFPO29FQUFlQyxTQUFROzhFQUNqRSw0RUFBQ0M7d0VBQUtDLGVBQWM7d0VBQVFDLGdCQUFlO3dFQUFRQyxhQUFhO3dFQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzREQU0xRWhCLHNDQUNDLDhEQUFDa0I7Z0VBQ0NDLFNBQVMvQjtnRUFDVG1CLFdBQVcsd0dBSVYsT0FIQzdFLG1CQUNJLDBIQUNBO2dFQUVOMEYsT0FBTyxrQkFBOEQsT0FBNUMxRixtQkFBbUIsWUFBWTswRUFFeEQsNEVBQUM4RTtvRUFBSUQsV0FBVTtvRUFBVUUsTUFBSztvRUFBZUUsU0FBUTs4RUFDbkQsNEVBQUNDO3dFQUFLSSxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQU9oQiw4REFBQ1Y7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNsQjs0REFDQ2lDLEtBQUtwRjs0REFDTGEsT0FBT25DOzREQUNQMkcsVUFBVXpFOzREQUNWMEUsV0FBV2hGOzREQUNYK0QsV0FBVTs0REFDVmtCLE1BQU07NERBQ05DLGFBQVk7NERBQ1pMLFVBQVV0RyxhQUFhSzs0REFDdkJtRSxPQUFPO2dFQUNMQyxRQUFRO2dFQUNSRSxXQUFXO2dFQUNYQyxXQUFXO2dFQUNYZ0MsWUFBWTtnRUFDWkMsVUFBVTtnRUFDVkMsWUFBWTtnRUFDWjlCLFdBQVc7Z0VBQ1grQixXQUFXO2dFQUNYQyxnQkFBZ0I7NERBQ2xCOzs7Ozs7Ozs7OztvREFLSDNHLDRCQUNDLDhEQUFDOEY7d0RBQ0NDLFNBQVM5Rjt3REFDVGtGLFdBQVU7d0RBQ1ZhLE9BQU07a0VBRU4sNEVBQUNaOzREQUFJRCxXQUFVOzREQUFVRSxNQUFLOzREQUFPQyxRQUFPOzREQUFlQyxTQUFRO3NFQUNqRSw0RUFBQ0M7Z0VBQUtDLGVBQWM7Z0VBQVFDLGdCQUFlO2dFQUFRQyxhQUFhO2dFQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7NkVBSXpFLDhEQUFDRTt3REFDQ0MsU0FBU3RFO3dEQUNUd0UsVUFBVSxFQUFFekcsb0JBQUFBLDhCQUFBQSxRQUFTc0MsSUFBSSxPQUFNdEIsWUFBWXVCLE1BQU0sS0FBSyxLQUFNcEMsYUFBYWlCLGVBQWVaO3dEQUN4Rm1GLFdBQVU7d0RBQ1ZhLE9BQU07a0VBRUxyRywwQkFDQyw4REFBQ3VGOzREQUFJQyxXQUFVOzs7OztpRkFFZiw4REFBQ0M7NERBQUl3QixPQUFNOzREQUE2QnpCLFdBQVU7NERBQVVFLE1BQUs7NERBQU9FLFNBQVE7NERBQVlELFFBQU87c0VBQ2pHLDRFQUFDRTtnRUFBS0MsZUFBYztnRUFBUUMsZ0JBQWU7Z0VBQVFDLGFBQWE7Z0VBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBV2pGLDhEQUFDVjt3Q0FBSUMsV0FBVTs7NENBRVpyRiwrQkFDQyw4REFBQ2dHO2dEQUNDQyxTQUFTakc7Z0RBQ1RxRixXQUFVO2dEQUNWYSxPQUFNOztrRUFHTiw4REFBQ2Q7d0RBQUlDLFdBQVU7Ozs7OztrRUFDZiw4REFBQ0M7d0RBQUlELFdBQVU7d0RBQTRGRSxNQUFLO3dEQUFPQyxRQUFPO3dEQUFlQyxTQUFRO2tFQUNuSiw0RUFBQ0M7NERBQUtDLGVBQWM7NERBQVFDLGdCQUFlOzREQUFRQyxhQUFhOzREQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs0Q0FNNUU3RixrQ0FDQyw4REFBQytGO2dEQUNDQyxTQUFTaEc7Z0RBQ1RvRixXQUFVO2dEQUNWYSxPQUFNOztrRUFHTiw4REFBQ2Q7d0RBQUlDLFdBQVU7Ozs7OztrRUFDZiw4REFBQ0M7d0RBQUlELFdBQVU7d0RBQTJGRSxNQUFLO3dEQUFPQyxRQUFPO3dEQUFlQyxTQUFRO2tFQUNsSiw0RUFBQ0M7NERBQUtDLGVBQWM7NERBQVFDLGdCQUFlOzREQUFRQyxhQUFhOzREQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FRakYsOERBQUNpQjtnQ0FDQ1gsS0FBS25GO2dDQUNMbUMsTUFBSztnQ0FDTDRELFFBQVE7Z0NBQ1JDLFFBQU87Z0NBQ1BaLFVBQVUvRDtnQ0FDVitDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1wQjtHQXhad0I1RjtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvSW5wdXRCYXIudHN4P2NhYzciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBTZW5kLCBQYXBlcmNsaXAsIE1pYywgU3F1YXJlLCBBcnJvd1VwLCBBcnJvd0Rvd24sIEJvdCwgWmFwLCBTZXR0aW5ncyB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgYXR0YWNobWVudFNlcnZpY2UgZnJvbSAnQC9saWIvc2VydmljZXMvYXR0YWNobWVudFNlcnZpY2UnO1xuaW1wb3J0IHsgQXR0YWNobWVudE1ldGFkYXRhIH0gZnJvbSAnQC9saWIvdHlwZXMvY2hhdCc7XG5cbmludGVyZmFjZSBBdHRhY2htZW50IHtcbiAgaWQ6IHN0cmluZztcbiAgZmlsZW5hbWU6IHN0cmluZztcbiAgdHlwZTogJ2ltYWdlJyB8ICdkb2N1bWVudCc7XG4gIGZpbGU6IEZpbGU7XG59XG5cbmludGVyZmFjZSBJbnB1dEJhclByb3BzIHtcbiAgbWVzc2FnZTogc3RyaW5nO1xuICBzZXRNZXNzYWdlOiAobWVzc2FnZTogc3RyaW5nKSA9PiB2b2lkO1xuICBvblNlbmRNZXNzYWdlOiAoYXR0YWNobWVudHM/OiBpbXBvcnQoJ0AvbGliL3R5cGVzL2NoYXQnKS5BdHRhY2htZW50TWV0YWRhdGFbXSwgd2ViU2VhcmNoRW5hYmxlZD86IGJvb2xlYW4pID0+IHZvaWQ7XG4gIGlzTG9hZGluZzogYm9vbGVhbjtcbiAgc2VsZWN0ZWRNb2RlbDogc3RyaW5nO1xuICBvbk1vZGVsQ2hhbmdlOiAobW9kZWw6IHN0cmluZykgPT4gdm9pZDtcbiAgb25TY3JvbGxUb1RvcD86ICgpID0+IHZvaWQ7XG4gIG9uU2Nyb2xsVG9Cb3R0b20/OiAoKSA9PiB2b2lkO1xuICBpc1N0cmVhbWluZz86IGJvb2xlYW47XG4gIG9uQ2FuY2VsU3RyZWFtaW5nPzogKCkgPT4gdm9pZDtcbiAgb25PcGVuTW9kZWxNb2RhbD86ICgpID0+IHZvaWQ7XG4gIHVzZXJuYW1lPzogc3RyaW5nO1xuICBjaGF0SWQ/OiBzdHJpbmc7XG4gIGFjdGl2ZUF0dGFjaG1lbnRzQ291bnQ/OiBudW1iZXI7IC8vIE7Dum1lcm8gZGUgYW5leG9zIGF0aXZvcyBubyBjaGF0XG4gIGRyb3BwZWRGaWxlcz86IEZpbGVbXTsgLy8gQXJxdWl2b3MgYXJyYXN0YWRvcyBkbyBkcmFnLW4tZHJvcFxufVxuXG5jb25zdCBBSV9NT0RFTFMgPSBbXG4gIHsgaWQ6ICdncHQtNC4xLW5hbm8nLCBuYW1lOiAnR1BULTQuMSBOYW5vJywgZGVzY3JpcHRpb246ICdSw6FwaWRvIGUgZWZpY2llbnRlJyB9LFxuICB7IGlkOiAnZ3B0LTQtdHVyYm8nLCBuYW1lOiAnR1BULTQgVHVyYm8nLCBkZXNjcmlwdGlvbjogJ01haXMgcG9kZXJvc28nIH0sXG4gIHsgaWQ6ICdjbGF1ZGUtMy1zb25uZXQnLCBuYW1lOiAnQ2xhdWRlIDMgU29ubmV0JywgZGVzY3JpcHRpb246ICdDcmlhdGl2byBlIHByZWNpc28nIH0sXG4gIHsgaWQ6ICdnZW1pbmktcHJvJywgbmFtZTogJ0dlbWluaSBQcm8nLCBkZXNjcmlwdGlvbjogJ011bHRpbW9kYWwnIH0sXG5dO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBJbnB1dEJhcih7XG4gIG1lc3NhZ2UsXG4gIHNldE1lc3NhZ2UsXG4gIG9uU2VuZE1lc3NhZ2UsXG4gIGlzTG9hZGluZyxcbiAgc2VsZWN0ZWRNb2RlbCxcbiAgb25Nb2RlbENoYW5nZSxcbiAgb25TY3JvbGxUb1RvcCxcbiAgb25TY3JvbGxUb0JvdHRvbSxcbiAgaXNTdHJlYW1pbmcgPSBmYWxzZSxcbiAgb25DYW5jZWxTdHJlYW1pbmcsXG4gIG9uT3Blbk1vZGVsTW9kYWwsXG4gIHVzZXJuYW1lLFxuICBjaGF0SWQsXG4gIGFjdGl2ZUF0dGFjaG1lbnRzQ291bnQgPSAwXG59OiBJbnB1dEJhclByb3BzKSB7XG5cbiAgY29uc3QgW3dlYlNlYXJjaEVuYWJsZWQsIHNldFdlYlNlYXJjaEVuYWJsZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbYXR0YWNobWVudHMsIHNldEF0dGFjaG1lbnRzXSA9IHVzZVN0YXRlPEF0dGFjaG1lbnRbXT4oW10pO1xuICBjb25zdCBbcHJvY2Vzc2VkQXR0YWNobWVudHMsIHNldFByb2Nlc3NlZEF0dGFjaG1lbnRzXSA9IHVzZVN0YXRlPEF0dGFjaG1lbnRNZXRhZGF0YVtdPihbXSk7XG4gIGNvbnN0IFtpc1VwbG9hZGluZywgc2V0SXNVcGxvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCB0ZXh0YXJlYVJlZiA9IHVzZVJlZjxIVE1MVGV4dEFyZWFFbGVtZW50PihudWxsKTtcbiAgY29uc3QgZmlsZUlucHV0UmVmID0gdXNlUmVmPEhUTUxJbnB1dEVsZW1lbnQ+KG51bGwpO1xuICBjb25zdCBhZGp1c3RIZWlnaHRUaW1lb3V0UmVmID0gdXNlUmVmPE5vZGVKUy5UaW1lb3V0IHwgbnVsbD4obnVsbCk7XG5cbiAgLy8gQ2xlYW51cCB0aW1lb3V0IG9uIHVubW91bnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaWYgKGFkanVzdEhlaWdodFRpbWVvdXRSZWYuY3VycmVudCkge1xuICAgICAgICBjbGVhclRpbWVvdXQoYWRqdXN0SGVpZ2h0VGltZW91dFJlZi5jdXJyZW50KTtcbiAgICAgIH1cbiAgICB9O1xuICB9LCBbXSk7XG5cbiAgLy8gQWRqdXN0IHRleHRhcmVhIGhlaWdodCB3aGVuIG1lc3NhZ2UgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGFkanVzdFRleHRhcmVhSGVpZ2h0KCk7XG4gIH0sIFttZXNzYWdlXSk7XG5cbiAgY29uc3QgaGFuZGxlS2V5UHJlc3MgPSAoZTogUmVhY3QuS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJyAmJiAhZS5zaGlmdEtleSkge1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgaGFuZGxlU2VuZCgpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVJbnB1dENoYW5nZSA9IChlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MVGV4dEFyZWFFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IHZhbHVlID0gZS50YXJnZXQudmFsdWU7XG4gICAgc2V0TWVzc2FnZSh2YWx1ZSk7XG5cbiAgICAvLyBBanVzdGFyIGFsdHVyYSBpbWVkaWF0YW1lbnRlIHBhcmEgZXZpdGFyIGZsaWNrZXJpbmdcbiAgICByZXF1ZXN0QW5pbWF0aW9uRnJhbWUoKCkgPT4ge1xuICAgICAgYWRqdXN0VGV4dGFyZWFIZWlnaHQoKTtcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTZW5kID0gKCkgPT4ge1xuICAgIGlmICgobWVzc2FnZT8udHJpbSgpIHx8IGF0dGFjaG1lbnRzLmxlbmd0aCA+IDApICYmICFpc0xvYWRpbmcgJiYgIWlzVXBsb2FkaW5nKSB7XG4gICAgICBjb25zb2xlLmxvZygnPT09IERFQlVHOiBFTlZJQU5ETyBNRU5TQUdFTSA9PT0nKTtcbiAgICAgIGNvbnNvbGUubG9nKCdNZW5zYWdlbTonLCBtZXNzYWdlKTtcbiAgICAgIGNvbnNvbGUubG9nKCdBbmV4b3MgbG9jYWlzOicsIGF0dGFjaG1lbnRzLmxlbmd0aCk7XG4gICAgICBjb25zb2xlLmxvZygnQW5leG9zIHByb2Nlc3NhZG9zOicsIHByb2Nlc3NlZEF0dGFjaG1lbnRzLmxlbmd0aCk7XG4gICAgICBjb25zb2xlLmxvZygnQW5leG9zIHByb2Nlc3NhZG9zIGRldGFsaGVzOicsIHByb2Nlc3NlZEF0dGFjaG1lbnRzKTtcbiAgICAgIGNvbnNvbGUubG9nKCdXZWIgU2VhcmNoIEVuYWJsZWQ6Jywgd2ViU2VhcmNoRW5hYmxlZCk7XG5cbiAgICAgIG9uU2VuZE1lc3NhZ2UocHJvY2Vzc2VkQXR0YWNobWVudHMsIHdlYlNlYXJjaEVuYWJsZWQpO1xuICAgICAgLy8gTGltcGFyIGFuZXhvcyBhcMOzcyBlbnZpb1xuICAgICAgc2V0QXR0YWNobWVudHMoW10pO1xuICAgICAgc2V0UHJvY2Vzc2VkQXR0YWNobWVudHMoW10pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVBdHRhY2htZW50ID0gKCkgPT4ge1xuICAgIGlmIChmaWxlSW5wdXRSZWYuY3VycmVudCkge1xuICAgICAgZmlsZUlucHV0UmVmLmN1cnJlbnQuY2xpY2soKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRmlsZVNlbGVjdCA9IGFzeW5jIChlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IGZpbGVzID0gZS50YXJnZXQuZmlsZXM7XG4gICAgaWYgKCFmaWxlcyB8fCAhdXNlcm5hbWUgfHwgIWNoYXRJZCkgcmV0dXJuO1xuXG4gICAgc2V0SXNVcGxvYWRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIC8vIFByaW1laXJvLCBhZGljaW9uYXIgYXJxdWl2b3MgbG9jYWxtZW50ZSBwYXJhIHByZXZpZXdcbiAgICAgIGNvbnN0IGxvY2FsQXR0YWNobWVudHM6IEF0dGFjaG1lbnRbXSA9IFtdO1xuICAgICAgZm9yIChjb25zdCBmaWxlIG9mIEFycmF5LmZyb20oZmlsZXMpKSB7XG4gICAgICAgIGNvbnN0IGF0dGFjaG1lbnQ6IEF0dGFjaG1lbnQgPSB7XG4gICAgICAgICAgaWQ6IERhdGUubm93KCkudG9TdHJpbmcoKSArIE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLCA5KSxcbiAgICAgICAgICBmaWxlbmFtZTogZmlsZS5uYW1lLFxuICAgICAgICAgIHR5cGU6IGZpbGUudHlwZS5zdGFydHNXaXRoKCdpbWFnZS8nKSA/ICdpbWFnZScgOiAnZG9jdW1lbnQnLFxuICAgICAgICAgIGZpbGVcbiAgICAgICAgfTtcbiAgICAgICAgbG9jYWxBdHRhY2htZW50cy5wdXNoKGF0dGFjaG1lbnQpO1xuICAgICAgfVxuICAgICAgc2V0QXR0YWNobWVudHMocHJldiA9PiBbLi4ucHJldiwgLi4ubG9jYWxBdHRhY2htZW50c10pO1xuXG4gICAgICAvLyBGYXplciB1cGxvYWQgZG9zIGFycXVpdm9zXG4gICAgICBjb25zdCB1cGxvYWRlZEF0dGFjaG1lbnRzID0gYXdhaXQgYXR0YWNobWVudFNlcnZpY2UudXBsb2FkTXVsdGlwbGVBdHRhY2htZW50cyhcbiAgICAgICAgQXJyYXkuZnJvbShmaWxlcyksXG4gICAgICAgIHVzZXJuYW1lLFxuICAgICAgICBjaGF0SWRcbiAgICAgICk7XG5cbiAgICAgIC8vIEF0dWFsaXphciBjb20gbWV0YWRhZG9zIGRvcyBhcnF1aXZvcyBwcm9jZXNzYWRvc1xuICAgICAgc2V0UHJvY2Vzc2VkQXR0YWNobWVudHMocHJldiA9PiBbXG4gICAgICAgIC4uLnByZXYsXG4gICAgICAgIC4uLnVwbG9hZGVkQXR0YWNobWVudHMubWFwKGF0dCA9PiBhdHQubWV0YWRhdGEpXG4gICAgICBdKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIHByb2Nlc3NhciBhcnF1aXZvczonLCBlcnJvcik7XG4gICAgICAvLyBSZW1vdmVyIGFuZXhvcyBxdWUgZmFsaGFyYW1cbiAgICAgIHNldEF0dGFjaG1lbnRzKHByZXYgPT4gcHJldi5maWx0ZXIoYXR0ID0+XG4gICAgICAgICFBcnJheS5mcm9tKGZpbGVzKS5zb21lKGZpbGUgPT4gZmlsZS5uYW1lID09PSBhdHQuZmlsZW5hbWUpXG4gICAgICApKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNVcGxvYWRpbmcoZmFsc2UpO1xuICAgICAgaWYgKGZpbGVJbnB1dFJlZi5jdXJyZW50KSB7XG4gICAgICAgIGZpbGVJbnB1dFJlZi5jdXJyZW50LnZhbHVlID0gJyc7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHJlbW92ZUF0dGFjaG1lbnQgPSAoaWQ6IHN0cmluZykgPT4ge1xuICAgIC8vIEVuY29udHJhciBvIGFuZXhvIHBhcmEgb2J0ZXIgbyBmaWxlbmFtZVxuICAgIGNvbnN0IGF0dGFjaG1lbnQgPSBhdHRhY2htZW50cy5maW5kKGF0dCA9PiBhdHQuaWQgPT09IGlkKTtcbiAgICBpZiAoYXR0YWNobWVudCkge1xuICAgICAgLy8gUmVtb3ZlciBkbyBlc3RhZG8gbG9jYWxcbiAgICAgIHNldEF0dGFjaG1lbnRzKHByZXYgPT4gcHJldi5maWx0ZXIoYXR0ID0+IGF0dC5pZCAhPT0gaWQpKTtcbiAgICAgIC8vIFJlbW92ZXIgZG9zIGFuZXhvcyBwcm9jZXNzYWRvcyB0YW1iw6ltXG4gICAgICBzZXRQcm9jZXNzZWRBdHRhY2htZW50cyhwcmV2ID0+IHByZXYuZmlsdGVyKGF0dCA9PiBhdHQuZmlsZW5hbWUgIT09IGF0dGFjaG1lbnQuZmlsZW5hbWUpKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlV2ViU2VhcmNoID0gKCkgPT4ge1xuICAgIHNldFdlYlNlYXJjaEVuYWJsZWQoIXdlYlNlYXJjaEVuYWJsZWQpO1xuICB9O1xuXG4gIGNvbnN0IGFkanVzdFRleHRhcmVhSGVpZ2h0ID0gKCkgPT4ge1xuICAgIGNvbnN0IHRleHRhcmVhID0gdGV4dGFyZWFSZWYuY3VycmVudDtcbiAgICBpZiAoIXRleHRhcmVhKSByZXR1cm47XG5cbiAgICAvLyBTdG9yZSBjdXJyZW50IHNjcm9sbCBwb3NpdGlvbiB0byBwcmV2ZW50IGp1bXBpbmdcbiAgICBjb25zdCBzY3JvbGxUb3AgPSB0ZXh0YXJlYS5zY3JvbGxUb3A7XG5cbiAgICAvLyBUZW1wb3JhcmlseSBzZXQgaGVpZ2h0IHRvIGF1dG8gdG8gZ2V0IGFjY3VyYXRlIHNjcm9sbEhlaWdodFxuICAgIHRleHRhcmVhLnN0eWxlLmhlaWdodCA9ICdhdXRvJztcblxuICAgIC8vIENhbGN1bGF0ZSBuZXcgaGVpZ2h0IHdpdGggcHJvcGVyIGNvbnN0cmFpbnRzXG4gICAgY29uc3Qgc2Nyb2xsSGVpZ2h0ID0gdGV4dGFyZWEuc2Nyb2xsSGVpZ2h0O1xuICAgIGNvbnN0IG1pbkhlaWdodCA9IDQ0OyAvLyBNaW5pbXVtIGhlaWdodCBpbiBwaXhlbHNcbiAgICBjb25zdCBtYXhIZWlnaHQgPSAxMjA7IC8vIE1heGltdW0gaGVpZ2h0IGluIHBpeGVsc1xuXG4gICAgLy8gU2V0IHRoZSBuZXcgaGVpZ2h0IHdpdGhpbiBib3VuZHNcbiAgICBjb25zdCBuZXdIZWlnaHQgPSBNYXRoLm1heChtaW5IZWlnaHQsIE1hdGgubWluKHNjcm9sbEhlaWdodCwgbWF4SGVpZ2h0KSk7XG4gICAgdGV4dGFyZWEuc3R5bGUuaGVpZ2h0ID0gbmV3SGVpZ2h0ICsgJ3B4JztcblxuICAgIC8vIEhhbmRsZSBvdmVyZmxvdyBhbmQgc2Nyb2xsaW5nXG4gICAgaWYgKHNjcm9sbEhlaWdodCA+IG1heEhlaWdodCkge1xuICAgICAgdGV4dGFyZWEuc3R5bGUub3ZlcmZsb3dZID0gJ2F1dG8nO1xuICAgICAgLy8gUmVzdG9yZSBzY3JvbGwgcG9zaXRpb24gaWYgY29udGVudCB3YXMgc2Nyb2xsZWRcbiAgICAgIGlmIChzY3JvbGxUb3AgPiAwKSB7XG4gICAgICAgIHRleHRhcmVhLnNjcm9sbFRvcCA9IHNjcm9sbFRvcDtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgdGV4dGFyZWEuc3R5bGUub3ZlcmZsb3dZID0gJ2hpZGRlbic7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGlzV2ViU2VhcmNoRW5hYmxlZCA9ICgpID0+IHtcbiAgICAvLyBXZWIgc2VhcmNoIGVzdMOhIGRpc3BvbsOtdmVsIHBhcmEgdG9kb3Mgb3MgbW9kZWxvcyB2aWEgT3BlblJvdXRlciBwbHVnaW5zXG4gICAgLy8gQXBlbmFzIG9jdWx0YXIgcGFyYSBtb2RlbG9zIGxvY2FpcyBvdSBlc3BlY8OtZmljb3MgcXVlIG7Do28gc3Vwb3J0YW1cbiAgICBjb25zdCB1bnN1cHBvcnRlZE1vZGVscyA9IFsnbG9jYWwvJywgJ29mZmxpbmUvJ107XG4gICAgcmV0dXJuICF1bnN1cHBvcnRlZE1vZGVscy5zb21lKHByZWZpeCA9PiBzZWxlY3RlZE1vZGVsLnN0YXJ0c1dpdGgocHJlZml4KSk7XG4gIH07XG5cbiAgY29uc3QgY3VycmVudE1vZGVsID0gQUlfTU9ERUxTLmZpbmQobW9kZWwgPT4gbW9kZWwuaWQgPT09IHNlbGVjdGVkTW9kZWwpO1xuICBjb25zdCBtb2RlbE5hbWUgPSBjdXJyZW50TW9kZWwgPyBjdXJyZW50TW9kZWwubmFtZSA6IHNlbGVjdGVkTW9kZWw7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICB7LyogRmxvYXRpbmcgZWZmZWN0IHNoYWRvdyAtIGNyZWF0ZXMgZGVwdGggKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LXgtMCAtdG9wLTggaC04IGJnLWdyYWRpZW50LXRvLXQgZnJvbS1ibHVlLTk1MC8zMCB2aWEtYmx1ZS05NTAvMTAgdG8tdHJhbnNwYXJlbnQgcG9pbnRlci1ldmVudHMtbm9uZSBibHVyLXNtXCI+PC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIHNtOnAtNCBsZzpwLTYgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtOTUwLzk4IHZpYS1ibHVlLTkwMC85OCB0by1ibHVlLTk1MC85OCBiYWNrZHJvcC1ibHVyLXhsIHJlbGF0aXZlIHNoYWRvdy0yeGwgc2hhZG93LWJsdWUtOTAwLzYwXCI+XG4gICAgICAgIHsvKiBFZmVpdG8gZGUgYnJpbGhvIHN1dGlsICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAwLzggdmlhLXRyYW5zcGFyZW50IHRvLWN5YW4tNTAwLzggcG9pbnRlci1ldmVudHMtbm9uZVwiPjwvZGl2PlxuXG4gICAgICAgIHsvKiBGbG9hdGluZyBnbG93IGVmZmVjdCAtIGlubmVyIGdsb3cgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by10IGZyb20tYmx1ZS02MDAvMTUgdmlhLWJsdWUtNTAwLzUgdG8tdHJhbnNwYXJlbnQgcG9pbnRlci1ldmVudHMtbm9uZVwiPjwvZGl2PlxuXG4gICAgICAgIHsvKiBUb3AgaGlnaGxpZ2h0IGZvciBmbG9hdGluZyBlZmZlY3QgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgaW5zZXQteC0wIGgtcHggYmctZ3JhZGllbnQtdG8tciBmcm9tLXRyYW5zcGFyZW50IHZpYS1ibHVlLTQwMC8zMCB0by10cmFuc3BhcmVudCBwb2ludGVyLWV2ZW50cy1ub25lXCI+PC9kaXY+XG4gICAgICBcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG8gcmVsYXRpdmUgei0xMFwiPlxuICAgICAgICB7LyogQXR0YWNobWVudCBQcmV2aWV3ICovfVxuICAgICAgICB7YXR0YWNobWVudHMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0zIHNtOm1iLTQgcC0zIHNtOnAtNCBiZy1ibHVlLTkwMC8zMCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtbGcgc206cm91bmRlZC14bCBib3JkZXIgYm9yZGVyLWJsdWUtNjAwLzMwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yIHNtOmdhcC0zXCI+XG4gICAgICAgICAgICAgIHthdHRhY2htZW50cy5tYXAoKGF0dGFjaG1lbnQpID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBrZXk9e2F0dGFjaG1lbnQuaWR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgc206c3BhY2UteC0zIGJnLWJsdWUtODAwLzQwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC1sZyBweC0yIHNtOnB4LTMgcHktMS41IHNtOnB5LTIgYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8yMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2F0dGFjaG1lbnQudHlwZSA9PT0gJ2ltYWdlJyA/IChcbiAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTMuNSBoLTMuNSBzbTp3LTQgc206aC00IHRleHQtYmx1ZS00MDAgZmxleC1zaHJpbmstMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk00IDE2bDQuNTg2LTQuNTg2YTIgMiAwIDAxMi44MjggMEwxNiAxNm0tMi0ybDEuNTg2LTEuNTg2YTIgMiAwIDAxMi44MjggMEwyMCAxNG0tNi02aC4wMU02IDIwaDEyYTIgMiAwIDAwMi0yVjZhMiAyIDAgMDAtMi0ySDZhMiAyIDAgMDAtMiAydjEyYTIgMiAwIDAwMiAyelwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTMuNSBoLTMuNSBzbTp3LTQgc206aC00IHRleHQtcmVkLTQwMCBmbGV4LXNocmluay0wXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTkgMTJoNm0tNiA0aDZtMiA1SDdhMiAyIDAgMDEtMi0yVjVhMiAyIDAgMDEyLTJoNS41ODZhMSAxIDAgMDEuNzA3LjI5M2w1LjQxNCA1LjQxNGExIDEgMCAwMS4yOTMuNzA3VjE5YTIgMiAwIDAxLTIgMnpcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHNtOnRleHQtc20gdGV4dC1ibHVlLTEwMCB0cnVuY2F0ZSBtYXgtdy0yMCBzbTptYXgtdy0zMiBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICB7YXR0YWNobWVudC5maWxlbmFtZX1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcmVtb3ZlQXR0YWNobWVudChhdHRhY2htZW50LmlkKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTMwMCBob3Zlcjp0ZXh0LXJlZC00MDAgdHJhbnNpdGlvbi1jb2xvcnMgcC0wLjUgc206cC0xIHJvdW5kZWQtZnVsbCBob3ZlcjpiZy1yZWQtNTAwLzIwIGZsZXgtc2hyaW5rLTBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMy41IGgtMy41IHNtOnctNCBzbTpoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNiAxOEwxOCA2TTYgNmwxMiAxMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIFNlbGVjdGVkIE1vZGVsIGFuZCBBdHRhY2htZW50cyBJbmRpY2F0b3JzICovfVxuICAgICAgICB7KHNlbGVjdGVkTW9kZWwgfHwgYWN0aXZlQXR0YWNobWVudHNDb3VudCA+IDApICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTEgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1zdGFydCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIHtzZWxlY3RlZE1vZGVsICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTkwMC85MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtbGcgcHgtMyBweS0xIGJvcmRlciBib3JkZXItYmx1ZS02MDAvMzAgc2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtMzAwXCI+TW9kZWxvOiA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWN5YW4tMzAwIGZvbnQtbWVkaXVtXCI+e21vZGVsTmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAge2FjdGl2ZUF0dGFjaG1lbnRzQ291bnQgPiAwICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi05MDAvOTAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLWxnIHB4LTMgcHktMSBib3JkZXIgYm9yZGVyLWdyZWVuLTYwMC8zMCBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JlZW4tMzAwXCI+QW5leG9zOiA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyZWVuLTIwMCBmb250LW1lZGl1bVwiPnthY3RpdmVBdHRhY2htZW50c0NvdW50fTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1lbmQgc3BhY2UteC0yIHNtOnNwYWNlLXgtM1wiPlxuICAgICAgICAgIHsvKiBGbG9hdGluZyBpbnB1dCBjb250YWluZXIgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcmVsYXRpdmUgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtOTAwLzgwIHRvLWJsdWUtODAwLzgwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC14bCBzbTpyb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLWJsdWUtNjAwLzMwIHNoYWRvdy14bCBzaGFkb3ctYmx1ZS05MDAvNTBcIj5cbiAgICAgICAgICAgIHsvKiBOZXVtb3JwaGljIGdsb3cgZWZmZWN0ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHJvdW5kZWQteGwgc206cm91bmRlZC0yeGwgc2hhZG93LVtpbnNldF8wXzFweF8wX3JnYmEoNTksMTMwLDI0NiwwLjEpLDBfMF8yMHB4X3JnYmEoNTksMTMwLDI0NiwwLjE1KV1cIj48L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwIGZsZXggaXRlbXMtZW5kIHNwYWNlLXgtMiBzbTpzcGFjZS14LTMgcC0zIHNtOnAtNFwiPlxuICAgICAgICAgICAgICB7LyogTGVmdCBzaWRlIGJ1dHRvbnMgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xIHNtOnNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIHsvKiBNb2RlbCBTZWxlY3Rpb24gQnV0dG9uICovfVxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29uT3Blbk1vZGVsTW9kYWx9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgc206cC0yLjUgcm91bmRlZC1sZyBzbTpyb3VuZGVkLXhsIGJnLWJsdWUtODAwLzQwIGhvdmVyOmJnLWJsdWUtNzAwLzUwIGJhY2tkcm9wLWJsdXItc20gdGV4dC1ibHVlLTMwMCBob3Zlcjp0ZXh0LWJsdWUtMjAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBob3ZlcjpzaGFkb3ctbGcgaG92ZXI6c2hhZG93LWJsdWUtNTAwLzIwIGJvcmRlciBib3JkZXItYmx1ZS02MDAvMjAgaG92ZXI6c2NhbGUtMTA1XCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlPVwiU2VsZWNpb25hciBtb2RlbG9cIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCBzbTp3LTUgc206aC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk05Ljc1IDE3TDkgMjBsLTEgMWg4bC0xLTEtLjc1LTNNMyAxM2gxOE01IDE3aDE0YTIgMiAwIDAwMi0yVjVhMiAyIDAgMDAtMi0ySDVhMiAyIDAgMDAtMiAydjEwYTIgMiAwIDAwMiAyelwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgIHsvKiBBdHRhY2htZW50IEJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVBdHRhY2htZW50fVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzVXBsb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yLjUgcm91bmRlZC14bCBiZy1ibHVlLTgwMC80MCBob3ZlcjpiZy1ibHVlLTcwMC81MCBiYWNrZHJvcC1ibHVyLXNtIHRleHQtYmx1ZS0zMDAgaG92ZXI6dGV4dC1ibHVlLTIwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2hhZG93LWxnIGhvdmVyOnNoYWRvdy1ibHVlLTUwMC8yMCBib3JkZXIgYm9yZGVyLWJsdWUtNjAwLzIwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGhvdmVyOnNjYWxlLTEwNVwiXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIkFuZXhhciBhcnF1aXZvXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7aXNVcGxvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTUgdy01IGJvcmRlci10LTIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS00MDBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xNS4xNzIgN2wtNi41ODYgNi41ODZhMiAyIDAgMTAyLjgyOCAyLjgyOGw2LjQxNC02LjU4NmE0IDQgMCAwMC01LjY1Ni01LjY1NmwtNi40MTUgNi41ODVhNiA2IDAgMTA4LjQ4NiA4LjQ4NkwyMC41IDEzXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgey8qIFdlYiBTZWFyY2ggQnV0dG9uICovfVxuICAgICAgICAgICAgICAgIHtpc1dlYlNlYXJjaEVuYWJsZWQoKSAmJiAoXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVdlYlNlYXJjaH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0yLjUgcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2hhZG93LWxnIGJvcmRlciBob3ZlcjpzY2FsZS0xMDUgYmFja2Ryb3AtYmx1ci1zbSAke1xuICAgICAgICAgICAgICAgICAgICAgIHdlYlNlYXJjaEVuYWJsZWRcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWN5YW4tNjAwLzMwIGhvdmVyOmJnLWN5YW4tNjAwLzQwIHRleHQtY3lhbi0yMDAgaG92ZXI6dGV4dC1jeWFuLTEwMCBib3JkZXItY3lhbi01MDAvNTAgc2hhZG93LWxnIHNoYWRvdy1jeWFuLTUwMC8yMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWJsdWUtODAwLzQwIGhvdmVyOmJnLWJsdWUtNzAwLzUwIHRleHQtYmx1ZS0zMDAgaG92ZXI6dGV4dC1ibHVlLTIwMCBib3JkZXItYmx1ZS02MDAvMjAgaG92ZXI6c2hhZG93LWJsdWUtNTAwLzIwJ1xuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9e2BCdXNjYSBuYSB3ZWIgLSAke3dlYlNlYXJjaEVuYWJsZWQgPyAnQXRpdmFkYScgOiAnRGVzYXRpdmFkYSd9YH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTIxLjcyMSAxMi43NTJhOS43MTEgOS43MTEgMCAwIDAtLjk0NS01LjAwMyAxMi43NTQgMTIuNzU0IDAgMCAxLTQuMzM5IDIuNzA4IDE4Ljk5MSAxOC45OTEgMCAwIDEtLjIxNCA0Ljc3MiAxNy4xNjUgMTcuMTY1IDAgMCAwIDUuNDk4LTIuNDc3Wk0xNC42MzQgMTUuNTVhMTcuMzI0IDE3LjMyNCAwIDAgMCAuMzMyLTQuNjQ3Yy0uOTUyLjIyNy0xLjk0NS4zNDctMi45NjYuMzQ3LTEuMDIxIDAtMi4wMTQtLjEyLTIuOTY2LS4zNDdhMTcuNTE1IDE3LjUxNSAwIDAgMCAuMzMyIDQuNjQ3IDE3LjM4NSAxNy4zODUgMCAwIDAgNS4yNjggMFpNOS43NzIgMTcuMTE5YTE4Ljk2MyAxOC45NjMgMCAwIDAgNC40NTYgMEExNy4xODIgMTcuMTgyIDAgMCAxIDEyIDIxLjcyNGExNy4xOCAxNy4xOCAwIDAgMS0yLjIyOC00LjYwNVpNNy43NzcgMTUuMjNhMTguODcgMTguODcgMCAwIDEtLjIxNC00Ljc3NCAxMi43NTMgMTIuNzUzIDAgMCAxLTQuMzQtMi43MDggOS43MTEgOS43MTEgMCAwIDAtLjk0NCA1LjAwNCAxNy4xNjUgMTcuMTY1IDAgMCAwIDUuNDk4IDIuNDc3Wk0yMS4zNTYgMTQuNzUyYTkuNzY1IDkuNzY1IDAgMCAxLTcuNDc4IDYuODE3IDE4LjY0IDE4LjY0IDAgMCAwIDEuOTg4LTQuNzE4IDE4LjYyNyAxOC42MjcgMCAwIDAgNS40OS0yLjA5OFpNMi42NDQgMTQuNzUyYzEuNjgyLjk3MSAzLjUzIDEuNjg4IDUuNDkgMi4wOTlhMTguNjQgMTguNjQgMCAwIDAgMS45ODggNC43MTggOS43NjUgOS43NjUgMCAwIDEtNy40NzgtNi44MTZaTTEzLjg3OCAyLjQzYTkuNzU1IDkuNzU1IDAgMCAxIDYuMTE2IDMuOTg2IDExLjI2NyAxMS4yNjcgMCAwIDEtMy43NDYgMi41MDQgMTguNjMgMTguNjMgMCAwIDAtMi4zNy02LjQ5Wk0xMiAyLjI3NmExNy4xNTIgMTcuMTUyIDAgMCAxIDIuODA1IDcuMTIxYy0uODk3LjIzLTEuODM3LjM1My0yLjgwNS4zNTMtLjk2OCAwLTEuOTA4LS4xMjItMi44MDUtLjM1M0ExNy4xNTEgMTcuMTUxIDAgMCAxIDEyIDIuMjc2Wk0xMC4xMjIgMi40M2ExOC42MjkgMTguNjI5IDAgMCAwLTIuMzcgNi40OSAxMS4yNjYgMTEuMjY2IDAgMCAxLTMuNzQ2LTIuNTA0IDkuNzU0IDkuNzU0IDAgMCAxIDYuMTE2LTMuOTg1WlwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFRleHQgSW5wdXQgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgICByZWY9e3RleHRhcmVhUmVmfVxuICAgICAgICAgICAgICAgICAgdmFsdWU9e21lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICBvbktleURvd249e2hhbmRsZUtleVByZXNzfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXRyYW5zcGFyZW50IGJvcmRlci1ub25lIHJvdW5kZWQteGwgcHgtNCBweS0zIHRleHQtYmx1ZS0xMDAgcGxhY2Vob2xkZXI6dGV4dC1ibHVlLTMwMC82MCBmb2N1czpvdXRsaW5lLW5vbmUgcmVzaXplLW5vbmUgdGV4dC1zbSBzZWxlY3Rpb246YmctYmx1ZS01MDAvMzBcIlxuICAgICAgICAgICAgICAgICAgcm93cz17MX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRGlnaXRlIHN1YSBtZW5zYWdlbSBhcXVpLi4uXCJcbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmcgfHwgaXNTdHJlYW1pbmd9XG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICc0NHB4JyxcbiAgICAgICAgICAgICAgICAgICAgbWluSGVpZ2h0OiAnNDRweCcsXG4gICAgICAgICAgICAgICAgICAgIG1heEhlaWdodDogJzEyMHB4JyxcbiAgICAgICAgICAgICAgICAgICAgbGluZUhlaWdodDogJzEuNScsXG4gICAgICAgICAgICAgICAgICAgIHdvcmRXcmFwOiAnYnJlYWstd29yZCcsXG4gICAgICAgICAgICAgICAgICAgIHdoaXRlU3BhY2U6ICdwcmUtd3JhcCcsXG4gICAgICAgICAgICAgICAgICAgIG92ZXJmbG93WTogJ2hpZGRlbicsXG4gICAgICAgICAgICAgICAgICAgIG92ZXJmbG93WDogJ2hpZGRlbicsXG4gICAgICAgICAgICAgICAgICAgIHNjcm9sbGJhcldpZHRoOiAndGhpbidcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFNlbmQvQ2FuY2VsIEJ1dHRvbiAqL31cbiAgICAgICAgICAgICAge2lzU3RyZWFtaW5nID8gKFxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29uQ2FuY2VsU3RyZWFtaW5nfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0zIHJvdW5kZWQteGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLXJlZC02MDAgdmlhLXJlZC01MDAgdG8tcmVkLTYwMCBob3Zlcjpmcm9tLXJlZC01MDAgaG92ZXI6dmlhLXJlZC00MDAgaG92ZXI6dG8tcmVkLTUwMCB0ZXh0LXdoaXRlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIGhvdmVyOnNjYWxlLTEwNSBib3JkZXIgYm9yZGVyLXJlZC01MDAvMzBcIlxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJQYXJhciBnZXJhw6fDo29cIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNiAxOEwxOCA2TTYgNmwxMiAxMlwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTZW5kfVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyghbWVzc2FnZT8udHJpbSgpICYmIGF0dGFjaG1lbnRzLmxlbmd0aCA9PT0gMCkgfHwgaXNMb2FkaW5nIHx8IGlzVXBsb2FkaW5nIHx8IGlzU3RyZWFtaW5nfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0zIHJvdW5kZWQteGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHZpYS1ibHVlLTUwMCB0by1jeWFuLTYwMCBob3Zlcjpmcm9tLWJsdWUtNTAwIGhvdmVyOnZpYS1ibHVlLTQwMCBob3Zlcjp0by1jeWFuLTUwMCB0ZXh0LXdoaXRlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIGhvdmVyOnNjYWxlLTEwNSBkaXNhYmxlZDpob3ZlcjpzY2FsZS0xMDAgYm9yZGVyIGJvcmRlci1ibHVlLTUwMC8zMFwiXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIkVudmlhciBtZW5zYWdlbVwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2lzTG9hZGluZyA/IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNSB3LTUgYm9yZGVyLXQtMiBib3JkZXItYi0yIGJvcmRlci13aGl0ZVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgY2xhc3NOYW1lPVwiaC01IHctNVwiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xNCA1bDcgN20wIDBsLTcgN203LTdIM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBTY3JvbGwgQnV0dG9ucyAtIFNpZGUgYnkgc2lkZSBuZXh0IHRvIGlucHV0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzcGFjZS15LTJcIj5cbiAgICAgICAgICAgIHsvKiBTY3JvbGwgdG8gVG9wIEJ1dHRvbiAqL31cbiAgICAgICAgICAgIHtvblNjcm9sbFRvVG9wICYmIChcbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29uU2Nyb2xsVG9Ub3B9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgcmVsYXRpdmUgcC0yLjUgcm91bmRlZC14bCBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtODAwLzQwIHRvLWJsdWUtOTAwLzQwIGhvdmVyOmZyb20tYmx1ZS03MDAvNTAgaG92ZXI6dG8tYmx1ZS04MDAvNTAgYmFja2Ryb3AtYmx1ci1zbSB0ZXh0LWJsdWUtMzAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOnNoYWRvdy1sZyBob3ZlcjpzaGFkb3ctYmx1ZS01MDAvMzAgYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8yMCBob3Zlcjpib3JkZXItYmx1ZS01MDAvNDAgaG92ZXI6c2NhbGUtMTA1IGFjdGl2ZTpzY2FsZS05NVwiXG4gICAgICAgICAgICAgICAgdGl0bGU9XCJJciBwYXJhIG8gdG9wb1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7LyogR2xvdyBlZmZlY3QgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHJvdW5kZWQteGwgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwMC8yMCB0by1jeWFuLTUwMC8yMCBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMCBibHVyLXNtXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LTUgaC01IHRyYW5zZm9ybSBncm91cC1ob3ZlcjotdHJhbnNsYXRlLXktMC41IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTIwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyLjV9IGQ9XCJNNSAxMGw3LTdtMCAwbDcgN20tNy03djE4XCIgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7LyogU2Nyb2xsIHRvIEJvdHRvbSBCdXR0b24gKi99XG4gICAgICAgICAgICB7b25TY3JvbGxUb0JvdHRvbSAmJiAoXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtvblNjcm9sbFRvQm90dG9tfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIHJlbGF0aXZlIHAtMi41IHJvdW5kZWQteGwgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTgwMC80MCB0by1ibHVlLTkwMC80MCBob3Zlcjpmcm9tLWJsdWUtNzAwLzUwIGhvdmVyOnRvLWJsdWUtODAwLzUwIGJhY2tkcm9wLWJsdXItc20gdGV4dC1ibHVlLTMwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBob3ZlcjpzaGFkb3ctbGcgaG92ZXI6c2hhZG93LWJsdWUtNTAwLzMwIGJvcmRlciBib3JkZXItYmx1ZS02MDAvMjAgaG92ZXI6Ym9yZGVyLWJsdWUtNTAwLzQwIGhvdmVyOnNjYWxlLTEwNSBhY3RpdmU6c2NhbGUtOTVcIlxuICAgICAgICAgICAgICAgIHRpdGxlPVwiSXIgcGFyYSBvIGZpbmFsXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHsvKiBHbG93IGVmZmVjdCAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgcm91bmRlZC14bCBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAwLzIwIHRvLWN5YW4tNTAwLzIwIG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwIGJsdXItc21cIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInJlbGF0aXZlIHctNSBoLTUgdHJhbnNmb3JtIGdyb3VwLWhvdmVyOnRyYW5zbGF0ZS15LTAuNSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0yMDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mi41fSBkPVwiTTE5IDE0bC03IDdtMCAwbC03LTdtNyA3VjRcIiAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBIaWRkZW4gZmlsZSBpbnB1dCAqL31cbiAgICAgICAgPGlucHV0XG4gICAgICAgICAgcmVmPXtmaWxlSW5wdXRSZWZ9XG4gICAgICAgICAgdHlwZT1cImZpbGVcIlxuICAgICAgICAgIG11bHRpcGxlXG4gICAgICAgICAgYWNjZXB0PVwiaW1hZ2UvcG5nLGltYWdlL2pwZWcsaW1hZ2Uvd2VicCxhcHBsaWNhdGlvbi9wZGZcIlxuICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVGaWxlU2VsZWN0fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImhpZGRlblwiXG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsImF0dGFjaG1lbnRTZXJ2aWNlIiwiQUlfTU9ERUxTIiwiaWQiLCJuYW1lIiwiZGVzY3JpcHRpb24iLCJJbnB1dEJhciIsIm1lc3NhZ2UiLCJzZXRNZXNzYWdlIiwib25TZW5kTWVzc2FnZSIsImlzTG9hZGluZyIsInNlbGVjdGVkTW9kZWwiLCJvbk1vZGVsQ2hhbmdlIiwib25TY3JvbGxUb1RvcCIsIm9uU2Nyb2xsVG9Cb3R0b20iLCJpc1N0cmVhbWluZyIsIm9uQ2FuY2VsU3RyZWFtaW5nIiwib25PcGVuTW9kZWxNb2RhbCIsInVzZXJuYW1lIiwiY2hhdElkIiwiYWN0aXZlQXR0YWNobWVudHNDb3VudCIsIndlYlNlYXJjaEVuYWJsZWQiLCJzZXRXZWJTZWFyY2hFbmFibGVkIiwiYXR0YWNobWVudHMiLCJzZXRBdHRhY2htZW50cyIsInByb2Nlc3NlZEF0dGFjaG1lbnRzIiwic2V0UHJvY2Vzc2VkQXR0YWNobWVudHMiLCJpc1VwbG9hZGluZyIsInNldElzVXBsb2FkaW5nIiwidGV4dGFyZWFSZWYiLCJmaWxlSW5wdXRSZWYiLCJhZGp1c3RIZWlnaHRUaW1lb3V0UmVmIiwiY3VycmVudCIsImNsZWFyVGltZW91dCIsImFkanVzdFRleHRhcmVhSGVpZ2h0IiwiaGFuZGxlS2V5UHJlc3MiLCJlIiwia2V5Iiwic2hpZnRLZXkiLCJwcmV2ZW50RGVmYXVsdCIsImhhbmRsZVNlbmQiLCJoYW5kbGVJbnB1dENoYW5nZSIsInZhbHVlIiwidGFyZ2V0IiwicmVxdWVzdEFuaW1hdGlvbkZyYW1lIiwidHJpbSIsImxlbmd0aCIsImNvbnNvbGUiLCJsb2ciLCJoYW5kbGVBdHRhY2htZW50IiwiY2xpY2siLCJoYW5kbGVGaWxlU2VsZWN0IiwiZmlsZXMiLCJsb2NhbEF0dGFjaG1lbnRzIiwiZmlsZSIsIkFycmF5IiwiZnJvbSIsImF0dGFjaG1lbnQiLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJNYXRoIiwicmFuZG9tIiwic3Vic3RyaW5nIiwiZmlsZW5hbWUiLCJ0eXBlIiwic3RhcnRzV2l0aCIsInB1c2giLCJwcmV2IiwidXBsb2FkZWRBdHRhY2htZW50cyIsInVwbG9hZE11bHRpcGxlQXR0YWNobWVudHMiLCJtYXAiLCJhdHQiLCJtZXRhZGF0YSIsImVycm9yIiwiZmlsdGVyIiwic29tZSIsInJlbW92ZUF0dGFjaG1lbnQiLCJmaW5kIiwiaGFuZGxlV2ViU2VhcmNoIiwidGV4dGFyZWEiLCJzY3JvbGxUb3AiLCJzdHlsZSIsImhlaWdodCIsInNjcm9sbEhlaWdodCIsIm1pbkhlaWdodCIsIm1heEhlaWdodCIsIm5ld0hlaWdodCIsIm1heCIsIm1pbiIsIm92ZXJmbG93WSIsImlzV2ViU2VhcmNoRW5hYmxlZCIsInVuc3VwcG9ydGVkTW9kZWxzIiwicHJlZml4IiwiY3VycmVudE1vZGVsIiwibW9kZWwiLCJtb2RlbE5hbWUiLCJkaXYiLCJjbGFzc05hbWUiLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJzcGFuIiwiYnV0dG9uIiwib25DbGljayIsInRpdGxlIiwiZGlzYWJsZWQiLCJyZWYiLCJvbkNoYW5nZSIsIm9uS2V5RG93biIsInJvd3MiLCJwbGFjZWhvbGRlciIsImxpbmVIZWlnaHQiLCJ3b3JkV3JhcCIsIndoaXRlU3BhY2UiLCJvdmVyZmxvd1giLCJzY3JvbGxiYXJXaWR0aCIsInhtbG5zIiwiaW5wdXQiLCJtdWx0aXBsZSIsImFjY2VwdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/InputBar.tsx\n"));

/***/ })

});