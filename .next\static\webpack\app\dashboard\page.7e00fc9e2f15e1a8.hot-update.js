"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/ModelSelectionModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/settingsService */ \"(app-pages-browser)/./src/lib/services/settingsService.ts\");\n/* harmony import */ var _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/openRouterService */ \"(app-pages-browser)/./src/lib/services/openRouterService.ts\");\n/* harmony import */ var _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/deepSeekService */ \"(app-pages-browser)/./src/lib/services/deepSeekService.ts\");\n/* harmony import */ var _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/modelFavoritesService */ \"(app-pages-browser)/./src/lib/services/modelFavoritesService.ts\");\n/* harmony import */ var _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAdvancedSearch */ \"(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\");\n/* harmony import */ var _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/advancedFiltersService */ \"(app-pages-browser)/./src/lib/services/advancedFiltersService.ts\");\n/* harmony import */ var _components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/AdvancedSearchInput */ \"(app-pages-browser)/./src/components/AdvancedSearchInput.tsx\");\n/* harmony import */ var _ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ExpensiveModelConfirmationModal */ \"(app-pages-browser)/./src/components/dashboard/ExpensiveModelConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Constantes para cache\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutos\nconst ENDPOINTS_CACHE_DURATION = 10 * 60 * 1000; // 10 minutos\nconst ModelSelectionModal = (param)=>{\n    let { isOpen, onClose, currentModel, onModelSelect } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [endpoints, setEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedEndpoint, setSelectedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteModelIds, setFavoriteModelIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [displayedModelsCount, setDisplayedModelsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(4);\n    const MODELS_PER_PAGE = 4;\n    const [customModelId, setCustomModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showExpensiveModelModal, setShowExpensiveModelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingExpensiveModel, setPendingExpensiveModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [smartCategories, setSmartCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteToggling, setFavoriteToggling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [lastLoadedEndpoint, setLastLoadedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelsCache, setModelsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [endpointsCache, setEndpointsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"paid\",\n        sortBy: \"newest\",\n        searchTerm: \"\"\n    });\n    // Hook de busca avançada\n    const { searchTerm, setSearchTerm, searchResults, suggestions, isSearching, hasSearched, clearSearch, trackModelSelection } = (0,_hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch)(models, {\n        debounceMs: 300,\n        enableSuggestions: false,\n        cacheResults: true,\n        fuzzyThreshold: 0.6,\n        maxResults: 50,\n        boostFavorites: true,\n        userId: (user === null || user === void 0 ? void 0 : user.email) || null,\n        trackAnalytics: true\n    });\n    // Load user endpoints apenas se necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && isOpen) {\n            // Verificar se temos cache válido\n            if (endpointsCache && Date.now() - endpointsCache.timestamp < ENDPOINTS_CACHE_DURATION) {\n                setEndpoints(endpointsCache.endpoints);\n                // Selecionar endpoint se ainda não tiver um selecionado\n                if (!selectedEndpoint && endpointsCache.endpoints.length > 0) {\n                    const openRouterEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"OpenRouter\");\n                    const deepSeekEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"DeepSeek\");\n                    if (openRouterEndpoint) {\n                        setSelectedEndpoint(openRouterEndpoint);\n                    } else if (deepSeekEndpoint) {\n                        setSelectedEndpoint(deepSeekEndpoint);\n                    } else {\n                        setSelectedEndpoint(endpointsCache.endpoints[0]);\n                    }\n                }\n            } else {\n                loadEndpoints();\n            }\n        }\n    }, [\n        user,\n        isOpen\n    ]);\n    // Load models when endpoint changes ou não há cache\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            const cachedData = modelsCache.get(cacheKey);\n            // Verificar se temos cache válido para este endpoint\n            if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {\n                setModels(cachedData.models);\n                setLastLoadedEndpoint(selectedEndpoint.id);\n            } else {\n                // Só carregar se mudou de endpoint ou não há cache válido\n                if (lastLoadedEndpoint !== selectedEndpoint.id || !cachedData) {\n                    if (selectedEndpoint.name === \"OpenRouter\") {\n                        loadOpenRouterModels();\n                    } else if (selectedEndpoint.name === \"DeepSeek\") {\n                        loadDeepSeekModels();\n                    }\n                }\n            }\n        }\n    }, [\n        selectedEndpoint,\n        lastLoadedEndpoint,\n        modelsCache\n    ]);\n    // Load smart categories\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSmartCategories(_lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getSmartCategories());\n    }, []);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                return userDoc.data().username || userDoc.id;\n            }\n            return \"unknown\";\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return \"unknown\";\n        }\n    };\n    const loadEndpoints = async ()=>{\n        if (!user) {\n            console.log(\"No user found\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const username = await getUsernameFromFirestore();\n            const userEndpoints = await (0,_lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__.getUserAPIEndpoints)(username);\n            // Salvar no cache\n            setEndpointsCache({\n                endpoints: userEndpoints,\n                timestamp: Date.now()\n            });\n            setEndpoints(userEndpoints);\n            // Select first available endpoint by default (OpenRouter or DeepSeek)\n            const openRouterEndpoint = userEndpoints.find((ep)=>ep.name === \"OpenRouter\");\n            const deepSeekEndpoint = userEndpoints.find((ep)=>ep.name === \"DeepSeek\");\n            if (openRouterEndpoint) {\n                setSelectedEndpoint(openRouterEndpoint);\n            } else if (deepSeekEndpoint) {\n                setSelectedEndpoint(deepSeekEndpoint);\n            } else if (userEndpoints.length > 0) {\n                setSelectedEndpoint(userEndpoints[0]);\n            }\n        } catch (error) {\n            console.error(\"Error loading endpoints:\", error);\n            setError(\"Erro ao carregar endpoints: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadOpenRouterModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from OpenRouter\n            const openRouterModels = await _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.fetchModels();\n            // Load favorite model IDs\n            const username = await getUsernameFromFirestore();\n            const favoriteIds = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);\n            // Mark favorite models\n            const modelsWithFavorites = openRouterModels.map((model)=>({\n                    ...model,\n                    isFavorite: favoriteIds.has(model.id)\n                }));\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: modelsWithFavorites,\n                    timestamp: Date.now()\n                }));\n            setModels(modelsWithFavorites);\n            setFavoriteModelIds(favoriteIds);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading models:\", error);\n            setError(\"Erro ao carregar modelos\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadDeepSeekModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from DeepSeek\n            const deepSeekModels = await _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.fetchModels();\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: deepSeekModels,\n                    timestamp: Date.now()\n                }));\n            setModels(deepSeekModels);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading DeepSeek models:\", error);\n            setError(\"Erro ao carregar modelos DeepSeek\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Função para obter modelos filtrados\n    const getFilteredModels = ()=>{\n        let filtered = [\n            ...models\n        ];\n        // Primeiro, aplicar filtros de categoria base (paid/free/favorites)\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\") {\n            if (filters.category === \"favorites\") {\n                filtered = [];\n            } else {\n                filtered = _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.filterByCategory(filtered, filters.category);\n            }\n        } else {\n            filtered = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.filterByCategory(filtered, filters.category);\n        }\n        // Se há busca ativa, usar resultados da busca avançada (mas ainda respeitando a categoria base)\n        if (hasSearched && searchTerm.trim()) {\n            const searchResultModels = searchResults.map((result)=>result.model);\n            // Filtrar os resultados de busca para manter apenas os que passam pelo filtro de categoria base\n            filtered = searchResultModels.filter((model)=>filtered.some((f)=>f.id === model.id));\n        } else if (selectedCategory) {\n            // Se há categoria inteligente selecionada, aplicar filtro adicional\n            const categoryFiltered = _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getModelsByCategory(filtered, selectedCategory);\n            filtered = categoryFiltered;\n        }\n        // Aplicar ordenação se não há busca ativa\n        if (!hasSearched || !searchTerm.trim()) {\n            const service = (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" ? _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService : _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService;\n            filtered = service.sortModels(filtered, filters.sortBy);\n        }\n        return filtered;\n    };\n    const filteredModels = getFilteredModels();\n    const handleToggleFavorite = async (model)=>{\n        if (!user || !selectedEndpoint) return;\n        // Prevenir múltiplas chamadas simultâneas para o mesmo modelo\n        if (favoriteToggling.has(model.id)) {\n            console.log(\"Already toggling favorite for model:\", model.id);\n            return;\n        }\n        console.log(\"Toggling favorite for model:\", model.id, \"Current status:\", model.isFavorite);\n        try {\n            // Marcar como em processo\n            setFavoriteToggling((prev)=>new Set(prev).add(model.id));\n            const username = await getUsernameFromFirestore();\n            const newFavoriteStatus = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.toggleFavorite(username, selectedEndpoint.id, model.id, model.name);\n            console.log(\"New favorite status:\", newFavoriteStatus);\n            // Update local state\n            const updatedFavoriteIds = new Set(favoriteModelIds);\n            if (newFavoriteStatus) {\n                updatedFavoriteIds.add(model.id);\n            } else {\n                updatedFavoriteIds.delete(model.id);\n            }\n            setFavoriteModelIds(updatedFavoriteIds);\n            // Update models array\n            setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: newFavoriteStatus\n                    } : m));\n            // Update cache with the new favorite status\n            if (selectedEndpoint) {\n                const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n                setModelsCache((prev)=>{\n                    const currentCache = prev.get(cacheKey);\n                    if (currentCache) {\n                        const updatedModels = currentCache.models.map((m)=>m.id === model.id ? {\n                                ...m,\n                                isFavorite: newFavoriteStatus\n                            } : m);\n                        return new Map(prev).set(cacheKey, {\n                            models: updatedModels,\n                            timestamp: currentCache.timestamp\n                        });\n                    }\n                    return prev;\n                });\n            }\n        } catch (error) {\n            console.error(\"Error toggling favorite:\", error);\n        } finally{\n            // Remover do estado de processamento\n            setFavoriteToggling((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(model.id);\n                return newSet;\n            });\n        }\n    };\n    // Function to check if a model is expensive (over $20 per million tokens)\n    const isExpensiveModel = (model)=>{\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\") return false;\n        const totalPrice = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model);\n        return totalPrice > 0.00002; // $20 por 1M tokens = $0.00002 por token\n    };\n    const handleSelectModel = (model)=>{\n        // Rastrear seleção de modelo\n        trackModelSelection(model.id);\n        // Check if it's an expensive OpenRouter model\n        if (isExpensiveModel(model)) {\n            setPendingExpensiveModel(model);\n            setShowExpensiveModelModal(true);\n        } else {\n            onModelSelect(model.id);\n            onClose();\n        }\n    };\n    const handleConfirmExpensiveModel = ()=>{\n        if (pendingExpensiveModel) {\n            // Rastrear seleção de modelo caro\n            trackModelSelection(pendingExpensiveModel.id);\n            onModelSelect(pendingExpensiveModel.id);\n            setShowExpensiveModelModal(false);\n            setPendingExpensiveModel(null);\n            onClose();\n        }\n    };\n    const handleCancelExpensiveModel = ()=>{\n        setShowExpensiveModelModal(false);\n        setPendingExpensiveModel(null);\n    };\n    const handleLoadMoreModels = ()=>{\n        setDisplayedModelsCount((prev)=>prev + MODELS_PER_PAGE);\n    };\n    const handleUseCustomModel = ()=>{\n        if (customModelId.trim()) {\n            onModelSelect(customModelId.trim());\n            onClose();\n        }\n    };\n    // Função para forçar refresh dos modelos\n    const handleRefreshModels = ()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>{\n                const newCache = new Map(prev);\n                newCache.delete(cacheKey);\n                return newCache;\n            });\n            console.log(\"Forcing refresh for endpoint:\", selectedEndpoint.name);\n            if (selectedEndpoint.name === \"OpenRouter\") {\n                loadOpenRouterModels();\n            } else if (selectedEndpoint.name === \"DeepSeek\") {\n                loadDeepSeekModels();\n            }\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl rounded-2xl border border-blue-600/30 shadow-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none rounded-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-blue-700/30 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-blue-100\",\n                                                children: \"Selecionar Modelo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"p-2 rounded-xl hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-blue-200\",\n                                                children: \"Endpoint\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleRefreshModels,\n                                                disabled: loading || !selectedEndpoint,\n                                                className: \"p-2 rounded-lg hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                title: \"Atualizar modelos\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 \".concat(loading ? \"animate-spin\" : \"\"),\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.id) || \"\",\n                                        onChange: (e)=>{\n                                            const endpoint = endpoints.find((ep)=>ep.id === e.target.value);\n                                            setSelectedEndpoint(endpoint || null);\n                                        },\n                                        className: \"w-full bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Selecione um endpoint\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            endpoints.map((endpoint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: endpoint.id,\n                                                    className: \"bg-blue-900 text-blue-100\",\n                                                    children: endpoint.name\n                                                }, endpoint.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 17\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, undefined),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-blue-700/30 space-y-6 relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-blue-900/30 backdrop-blur-sm rounded-xl border border-blue-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-blue-200 mb-3 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-blue-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Modelo Customizado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"openai/gpt-4-1\",\n                                                        value: customModelId,\n                                                        onChange: (e)=>setCustomModelId(e.target.value),\n                                                        onKeyDown: (e)=>e.key === \"Enter\" && handleUseCustomModel(),\n                                                        className: \"flex-1 bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleUseCustomModel,\n                                                        disabled: !customModelId.trim(),\n                                                        className: \"px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 disabled:from-blue-800 disabled:to-blue-800 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 disabled:hover:scale-100\",\n                                                        children: \"Usar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-300/70 mt-3\",\n                                                children: \"Digite o ID completo do modelo (ex: openai/gpt-4, anthropic/claude-3-sonnet)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 bg-blue-900/30 backdrop-blur-sm rounded-xl p-1 border border-blue-600/20\",\n                                        children: [\n                                            \"paid\",\n                                            \"free\",\n                                            \"favorites\"\n                                        ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            category\n                                                        })),\n                                                className: \"flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-all duration-200 \".concat(filters.category === category ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg\" : \"text-blue-300 hover:text-blue-200 hover:bg-blue-800/30\"),\n                                                children: category === \"paid\" ? \"Pagos\" : category === \"free\" ? \"Gr\\xe1tis\" : \"Favoritos\"\n                                            }, category, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            value: searchTerm,\n                                                            onChange: setSearchTerm,\n                                                            suggestions: [],\n                                                            isSearching: isSearching,\n                                                            placeholder: \"Buscar modelos... (ex: 'gpt-4', 'vision', 'cheap')\",\n                                                            showSuggestions: false\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: filters.sortBy,\n                                                        onChange: (e)=>setFilters((prev)=>({\n                                                                    ...prev,\n                                                                    sortBy: e.target.value\n                                                                })),\n                                                        className: \"bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                                        disabled: hasSearched && searchTerm.trim().length > 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"newest\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Mais recentes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"price_low\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Menor pre\\xe7o\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"price_high\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Maior pre\\xe7o\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"context_high\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Maior contexto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            !hasSearched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedCategory(null),\n                                                        className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 \".concat(!selectedCategory ? \"bg-blue-600 text-white\" : \"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200\"),\n                                                        children: \"Todos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    smartCategories.slice(0, 6).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setSelectedCategory(category.id),\n                                                            className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-1 \".concat(selectedCategory === category.id ? \"bg-blue-600 text-white\" : \"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200\"),\n                                                            title: category.description,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: category.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, category.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            hasSearched && searchTerm.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300\",\n                                                        children: [\n                                                            filteredModels.length,\n                                                            \" resultado\",\n                                                            filteredModels.length !== 1 ? \"s\" : \"\",\n                                                            ' para \"',\n                                                            searchTerm,\n                                                            '\"'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: clearSearch,\n                                                        className: \"text-blue-400 hover:text-blue-300 transition-colors duration-200\",\n                                                        children: \"Limpar busca\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-scroll p-6 max-h-[32rem] relative z-10\",\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-200 text-sm font-medium\",\n                                                    children: \"Carregando modelos...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-red-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-300 font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 640,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-blue-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-300 font-medium\",\n                                                children: hasSearched && searchTerm.trim() ? 'Nenhum resultado para \"'.concat(searchTerm, '\"') : selectedCategory ? \"Nenhum modelo na categoria selecionada\" : \"Nenhum modelo encontrado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-blue-400/70 text-sm mt-2 space-y-1\",\n                                                children: hasSearched && searchTerm.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Tente:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"list-disc list-inside space-y-1 text-left max-w-xs mx-auto\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Verificar a ortografia\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Usar termos mais gen\\xe9ricos\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Explorar as categorias\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Limpar filtros ativos\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Tente ajustar os filtros ou usar as categorias\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: filteredModels.slice(0, displayedModelsCount).map((model)=>{\n                                            // Encontrar o resultado da busca para este modelo (se houver)\n                                            const searchResult = hasSearched ? searchResults.find((r)=>r.model.id === model.id) : null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModelCard, {\n                                                model: model,\n                                                isSelected: currentModel === model.id,\n                                                onSelect: ()=>handleSelectModel(model),\n                                                onToggleFavorite: ()=>handleToggleFavorite(model),\n                                                isToggling: favoriteToggling.has(model.id),\n                                                service: _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService,\n                                                searchTerm: hasSearched ? searchTerm : \"\",\n                                                searchResult: searchResult\n                                            }, model.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length > displayedModelsCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLoadMoreModels,\n                                            className: \"px-6 py-3 bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm border border-blue-600/30 hover:border-blue-500/50 rounded-xl text-blue-200 hover:text-blue-100 transition-all duration-200 flex items-center space-x-2 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Carregar mais modelos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 708,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 9l-7 7-7-7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 710,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-4 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-400/70\",\n                                                children: [\n                                                    \"Mostrando \",\n                                                    Math.min(displayedModelsCount, filteredModels.length),\n                                                    \" de \",\n                                                    filteredModels.length,\n                                                    \" modelos\",\n                                                    models.length !== filteredModels.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-blue-300\",\n                                                        children: [\n                                                            \"(\",\n                                                            models.length,\n                                                            \" total)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            hasSearched && searchTerm.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-4 text-xs text-blue-400/60\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"\\uD83D\\uDD0D Busca: \",\n                                                            searchTerm\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"⚡ \",\n                                                            searchResults.length,\n                                                            \" resultados\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 718,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-slate-700/30 space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-slate-100 mb-2\",\n                                            children: \"Modelos DeepSeek\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 748,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-slate-400\",\n                                            children: \"Escolha entre nossos modelos especializados\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 747,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 746,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-6\",\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-200 text-sm font-medium\",\n                                                    children: \"Carregando modelos...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 761,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 759,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 758,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-red-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-300 font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 768,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && models.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-blue-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-300 font-medium\",\n                                                children: \"Nenhum modelo encontrado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-400/70 text-sm mt-1\",\n                                                children: \"Tente ajustar os filtros de busca\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 780,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && models.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeepSeekModelCard, {\n                                                model: model,\n                                                isSelected: currentModel === model.id,\n                                                onSelect: ()=>handleSelectModel(model)\n                                            }, model.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 756,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\" && (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"DeepSeek\" && selectedEndpoint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8 text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-blue-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 810,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 809,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-300 font-medium\",\n                                children: \"Sele\\xe7\\xe3o de modelos dispon\\xedvel para OpenRouter e DeepSeek\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 814,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-400/70 text-sm mt-1\",\n                                children: \"Selecione um desses endpoints para ver os modelos dispon\\xedveis\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 815,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 808,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: showExpensiveModelModal,\n                model: pendingExpensiveModel,\n                onConfirm: handleConfirmExpensiveModel,\n                onCancel: handleCancelExpensiveModel\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 821,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 437,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModelSelectionModal, \"aZQkJ8e05K/tmXoFd2ZCR4cCD7g=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch\n    ];\n});\n_c = ModelSelectionModal;\nconst ModelCard = (param)=>{\n    let { model, isSelected, onSelect, onToggleFavorite, isToggling = false, service = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService, searchTerm = \"\", searchResult } = param;\n    const isExpensive = service === _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService && _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model) > 0.00002; // $20 por 1M tokens\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-5 rounded-xl border transition-all duration-200 backdrop-blur-sm hover:scale-[1.02] relative \".concat(isSelected ? \"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20\" : \"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40\"),\n        children: [\n            isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-2 -right-2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 rounded-full p-1.5 shadow-lg border-2 border-slate-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 866,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 865,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 864,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 863,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-blue-100 truncate\",\n                                                        children: (searchResult === null || searchResult === void 0 ? void 0 : searchResult.highlightedName) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: searchResult.highlightedName\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 879,\n                                                            columnNumber: 21\n                                                        }, undefined) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                                            text: model.name,\n                                                            highlight: searchTerm\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 881,\n                                                            columnNumber: 21\n                                                        }, undefined) : model.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 877,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-amber-500/20 text-amber-300 px-2 py-0.5 rounded-full border border-amber-500/30 font-medium\",\n                                                        children: \"CARO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 887,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    searchResult && searchResult.matchedFields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-1\",\n                                                        children: searchResult.matchedFields.slice(0, 3).map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs bg-green-500/20 text-green-300 px-1.5 py-0.5 rounded border border-green-500/30\",\n                                                                title: \"Encontrado em: \".concat(field),\n                                                                children: field === \"name\" ? \"\\uD83D\\uDCDD\" : field === \"description\" ? \"\\uD83D\\uDCC4\" : field === \"provider\" ? \"\\uD83C\\uDFE2\" : field === \"tags\" ? \"\\uD83C\\uDFF7️\" : \"\\uD83D\\uDD0D\"\n                                                            }, field, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-300/70 truncate mt-1 font-mono\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                                    text: model.id,\n                                                    highlight: searchTerm\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 906,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 905,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    service.isFreeModel(model) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-green-600/30 text-green-300 text-xs rounded-full border border-green-500/30 font-medium\",\n                                        children: \"Gr\\xe1tis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 910,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 874,\n                                columnNumber: 11\n                            }, undefined),\n                            model.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-300/80 line-clamp-2\",\n                                    children: (searchResult === null || searchResult === void 0 ? void 0 : searchResult.highlightedDescription) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: searchResult.highlightedDescription\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 921,\n                                        columnNumber: 19\n                                    }, undefined) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                        text: model.description,\n                                        highlight: searchTerm\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 923,\n                                        columnNumber: 19\n                                    }, undefined) : model.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 919,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Contexto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 933,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: service.formatContextLength(model.context_length)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 934,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Input\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 937,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.prompt),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 938,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 936,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Output\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.completion),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 942,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 940,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 931,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 873,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 ml-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggleFavorite,\n                                disabled: isToggling,\n                                className: \"p-2.5 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed \".concat(model.isFavorite ? \"text-yellow-400 hover:text-yellow-300 bg-yellow-500/20 border border-yellow-500/30\" : \"text-blue-300 hover:text-yellow-400 bg-blue-800/30 border border-blue-600/20 hover:bg-yellow-500/20 hover:border-yellow-500/30\"),\n                                title: isToggling ? \"Processando...\" : model.isFavorite ? \"Remover dos favoritos\" : \"Adicionar aos favoritos\",\n                                children: isToggling ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-current\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 959,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: model.isFavorite ? \"currentColor\" : \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 948,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSelect,\n                                className: \"px-6 py-2.5 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 shadow-lg hover:shadow-blue-500/30\",\n                                children: \"Selecionar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 967,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 947,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 872,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 856,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ModelCard;\nconst DeepSeekModelCard = (param)=>{\n    let { model, isSelected, onSelect } = param;\n    const getModelIcon = (modelId)=>{\n        if (modelId === \"deepseek-chat\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-blue-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 991,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 990,\n                columnNumber: 9\n            }, undefined);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-cyan-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 997,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 996,\n                columnNumber: 9\n            }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative p-6 rounded-2xl border transition-all duration-300 cursor-pointer group backdrop-blur-sm hover:scale-[1.02] \".concat(isSelected ? \"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20\" : \"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40\"),\n        onClick: onSelect,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-br from-blue-600/10 to-cyan-600/10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1011,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                children: getModelIcon(model.id)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1017,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-blue-100 text-lg\",\n                                        children: model.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1021,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-300/70 mt-1\",\n                                        children: model.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1022,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1020,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1016,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Contexto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatContextLength(model.context_length)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1030,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1028,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1035,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: [\n                                            _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatPrice(model.pricing.prompt),\n                                            \"/1M\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1036,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1034,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1041,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: [\n                                            _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatPrice(model.pricing.completion),\n                                            \"/1M\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1042,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1040,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1027,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            onSelect();\n                        },\n                        className: \"w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 hover:scale-105 \".concat(isSelected ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg shadow-blue-500/30\" : \"bg-blue-800/40 text-blue-200 hover:bg-gradient-to-r hover:from-blue-600 hover:to-cyan-600 hover:text-white border border-blue-600/30\"),\n                        children: isSelected ? \"Selecionado\" : \"Selecionar Modelo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1049,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1014,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 1004,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = DeepSeekModelCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ModelSelectionModal);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ModelSelectionModal\");\n$RefreshReg$(_c1, \"ModelCard\");\n$RefreshReg$(_c2, \"DeepSeekModelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\n"));

/***/ })

});